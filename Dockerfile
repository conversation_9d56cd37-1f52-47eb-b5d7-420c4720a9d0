FROM openjdk:11-jre
VOLUME /tmp
EXPOSE 8080
LABEL "com.datadoghq.ad.logs"='[{"source": "java", "service": "atlas-app"}]'
ARG DEPENDENCY=atlas-app-spring/target/dependency
COPY ${DEPENDENCY}/BOOT-INF/lib /app/lib
COPY ${DEPENDENCY}/META-INF /app/META-INF
COPY ${DEPENDENCY}/BOOT-INF/classes /app
RUN wget -O dd-java-agent.jar https://dtdg.co/latest-java-tracer
RUN sh -c 'touch /dd-java-agent.jar'
COPY atlas.sh atlas.sh
RUN ["chmod", "+x", "atlas.sh"]
ENTRYPOINT ["/atlas.sh"]
