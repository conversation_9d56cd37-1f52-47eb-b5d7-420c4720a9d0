package com.carsaver.partner.web.api;

import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.deal.CustomerDealSummaryRequest;
import com.carsaver.partner.model.retail.CustomerDealSummaryResponse;
import com.carsaver.partner.model.retail.CustomerTagsResponse;
import com.carsaver.partner.service.CustomerDealSummaryService;
import com.carsaver.partner.service.CustomerTagService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/customers")
@RequiredArgsConstructor
public class CustomerDealSummaryController {

    private final CustomerDealSummaryService customerDealSummaryService;
    private final CustomerTagService customerTagService;


    @GetMapping("/{user}/deal-summary")
    public ResponseEntity<CustomerDealSummaryResponse> getCustomerDealSummary(@PathVariable UserView user, @RequestParam(required = false) String dealerId) {
        CustomerDealSummaryResponse response = customerDealSummaryService.getDealSummary(user, dealerId);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/v2/{user}/deal-summary")
    public ResponseEntity<CustomerDealSummaryResponse> getCustomerDealSummary(@PathVariable UserView user, @Valid @RequestBody CustomerDealSummaryRequest request) {
        CustomerDealSummaryResponse response = customerDealSummaryService.getDealSummaryV2(user, request);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/{user-id}/tags")
    public ResponseEntity<CustomerTagsResponse> fetchCustomerTags(@PathVariable("user-id") String userId, @RequestBody(required = false) List<String> dealerIds) {
        CustomerTagsResponse response = customerTagService.fetchCustomerTags(userId, dealerIds);
        return ResponseEntity.ok(response);
    }
}
