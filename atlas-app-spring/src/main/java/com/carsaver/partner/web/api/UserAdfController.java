package com.carsaver.partner.web.api;

import com.carsaver.magellan.model.AdfLeadComment;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.client.leads.ProspectAdfLeadTransactions;
import com.carsaver.partner.client.leads.ProspectLeadClient;
import com.carsaver.partner.service.crm.CrmService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController("userAdfController")
public class UserAdfController {


    private final ProspectLeadClient prospectLeadClient;
    private final SplitFeatureFlags splitFeatureFlags;
    private final CrmService crmService;


    public UserAdfController(ProspectLeadClient prospectLeadClient, SplitFeatureFlags splitFeatureFlags, CrmService crmService) {
        this.prospectLeadClient = prospectLeadClient;
        this.splitFeatureFlags = splitFeatureFlags;
        this.crmService = crmService;
    }

    @PreAuthorize("hasPermission(#dealer, 'customer:read')")
    @PostMapping("/api/dealer/{dealer}/users/{user}/send-to-crm")
    public void findById(@PathVariable DealerView dealer, @PathVariable UserView user, @RequestBody(required = false) AdfLeadComment adfLeadComment) {
        crmService.sendToCrm(dealer, user, adfLeadComment);
    }

    @PreAuthorize("hasPermission(#dealer, 'customer:read')")
    @PostMapping("/api/dealer/{dealer}/prospects/{prospectId}/send-to-crm")
    public ResponseEntity<List<ProspectAdfLeadTransactions>> sendCRMProspect(@PathVariable DealerView dealer, @PathVariable String prospectId,
                                                                             @RequestBody(required = false) AdfLeadComment adfLeadComment) {
        Objects.requireNonNull(dealer);
        Objects.requireNonNull(prospectId);
        if (splitFeatureFlags.isSendToCrmEnhancementFeatureEnabled() && adfLeadComment != null) {
            return ResponseEntity.ok().body(prospectLeadClient.sendProspectAdfLeadToCrm(prospectId, dealer.getId(), adfLeadComment));
        } else {
            return ResponseEntity.ok().body(prospectLeadClient.sendProspectAdfLeadToCrm(prospectId, dealer.getId()));
        }
    }
}
