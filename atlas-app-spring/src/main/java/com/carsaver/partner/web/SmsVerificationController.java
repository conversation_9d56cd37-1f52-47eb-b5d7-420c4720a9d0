package com.carsaver.partner.web;

import com.carsaver.magellan.api.EmailValidationService;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.request.user.SmsVerifiedUpdateRequest;
import com.carsaver.magellan.model.EmailValidationView;
import com.carsaver.magellan.model.UserView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Objects;
import java.util.Optional;

@Controller
@RequestMapping("/sms-verification")
public class SmsVerificationController {

    @Autowired
    private EmailValidationService emailValidationService;

    @Autowired
    private UserClient userClient;

    @GetMapping(params = {"code", "userId"})
    public String verifySmsCode(@RequestParam String userId, @RequestParam("code") String code) {
        UserView user = userClient.findById(userId);
        Objects.requireNonNull(user, "User does not exist: " + userId);

        Optional<EmailValidationView> emailValidationView = emailValidationService.findEmailCode(user.getEmail());

        if (emailValidationView.isPresent() && emailValidationView.get().getCode().equalsIgnoreCase(code)) {
            userClient.updateSmsVerified(user.getId(), SmsVerifiedUpdateRequest.builder().smsVerified(true).build());

            return "redirect:/sms-verification/success";
        }

        return "redirect:/sms-verification/failure";
    }

    @GetMapping("/success")
    public String success(){
        return "smsVerification";
    }

    @GetMapping("/failure")
    public String failure(){
        return "smsVerification";
    }
}
