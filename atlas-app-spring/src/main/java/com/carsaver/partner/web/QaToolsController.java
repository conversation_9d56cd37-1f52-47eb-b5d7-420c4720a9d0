package com.carsaver.partner.web;

import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.InventoryClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.request.certificate.DealPreferenceRequest;
import com.carsaver.magellan.client.request.inventory.HomeNetVehicleImportRequest;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.model.ImportCarLogView;
import com.carsaver.magellan.model.ImportJobView;
import com.carsaver.magellan.model.certificate.DealPreferences;
import com.carsaver.magellan.model.user.CreditPreApproval;
import com.carsaver.magellan.model.user.CreditProfile;
import com.carsaver.magellan.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Controller
@Profile({"beta", "local"})
public class QaToolsController {

    @Autowired
    private InventoryClient inventoryClient;

    @Autowired
    private UserClient userClient;

    @Autowired
    private CertificateClient certificateClient;

    @GetMapping({"/qaTools","/qaTools/inventory"})
    public String qaTools() {
        return "qaTools";
    }

    @PostMapping({"/api/qaTools/inventory/importJob/homenet", "/api/qaTools/{dealerId}/inventory/importJob/homenet"})
    public ResponseEntity<List<ImportJobView>> submit(@RequestBody HomeNetVehicleImportRequest importRequest){
        log.info("Import Request: {}",importRequest);
        inventoryClient.queueHomenetImport(importRequest);
        ImportJobView lastJob = inventoryClient.findLastCreatedJob();
        return ResponseEntity.ok(List.of(lastJob));
    }

    @GetMapping({"/api/qaTools/inventory/importJob/homenet/{importJobId}", "/api/qaTools/{dealerId}/inventory/importJob/homenet/{importJobId}"})
    public ResponseEntity<ImportJobView> refreshImportJob(@PathVariable String importJobId){
        log.info("Import Request Refresh: {}",importJobId);
        ImportJobView lastJob = inventoryClient.findImportedJobById(importJobId);
        return ResponseEntity.ok(lastJob);
    }


    @GetMapping({"/api/qaTools/inventory/importJob/logs/{importJobId}", "/api/qaTools/{dealerId}/inventory/importJob/logs/{importJobId}"})
    public ResponseEntity<List<ImportCarLogView>> fetchImportJobErrors(@PathVariable String importJobId){
        log.info("Import Request Refresh: {}",importJobId);
        List<ImportCarLogView> logs = inventoryClient.findImportLogsByJobId(importJobId);
        return ResponseEntity.ok(logs);
    }

    @GetMapping(path= "/api/qaTools/pre-approvals", params = {"userId"})
    public ResponseEntity<Collection<CreditPreApproval>> fetchPreApprovals(@RequestParam String userId){
        var preApprovals = userClient.findCreditProfileByUser(userId)
                .map(CreditProfile::getPreApprovals)
                .orElse(Collections.emptyMap())
                .values();


        return ResponseEntity.ok(preApprovals);
    }

    @PostMapping(path= "/api/qaTools/pre-approvals/{preApprovalNumber}/expire", params = {"userId"})
    public ResponseEntity<?> expirePreApproval(@PathVariable String preApprovalNumber, @RequestParam String userId){
        var preApprovals = userClient.findCreditProfileByUser(userId)
                .map(CreditProfile::getPreApprovals)
                .orElse(Collections.emptyMap())
                .values()
                .stream().filter(creditPreApproval -> creditPreApproval.getApprovalNumber().equals(preApprovalNumber))
                .findFirst();

        if (preApprovals.isPresent()){
            LocalDate yesterday = LocalDate.now().minusDays(1);
            preApprovals.get().setPreApprovalDate(yesterday);

            updateCreditProfile(userId, preApprovals);
            updateAllCertificateWithPreApprovalDate(userId,preApprovals.get());

            return ResponseEntity.ok().build();
        }

        return ResponseEntity.badRequest().build();
    }

    @PostMapping(path= "/api/qaTools/pre-approvals/{preApprovalNumber}/renew", params = {"userId"})
    public ResponseEntity<?> renewPreApproval(@PathVariable String preApprovalNumber, @RequestParam String userId){
        var preApprovals = userClient.findCreditProfileByUser(userId)
                .map(CreditProfile::getPreApprovals)
                .orElse(Collections.emptyMap())
                .values()
                .stream().filter(creditPreApproval -> creditPreApproval.getApprovalNumber().equals(preApprovalNumber))
                .findFirst();

        if (preApprovals.isPresent()){
            LocalDate weekFromNow = LocalDate.now().plusWeeks(1);
            preApprovals.get().setPreApprovalDate(weekFromNow);

            updateCreditProfile(userId, preApprovals);
            updateAllCertificateWithPreApprovalDate(userId,preApprovals.get());

            return ResponseEntity.ok().build();
        }

        return ResponseEntity.badRequest().build();
    }

    private void updateCreditProfile(String userId, Optional<CreditPreApproval> preApprovals) {
        var creditProfile = CreditProfile
                .builder()
                .preApprovals(Map.of(preApprovals.get().getFinancierId(), preApprovals.get()))
                .build();

        CreditProfile creditProfileToPatch = userClient.findCreditProfileByUser(userId).orElseGet(CreditProfile::new);

        mergePreApprovals(creditProfile, creditProfileToPatch);

        //do patch
        BeanUtils.copyNonNullProperties(creditProfile, creditProfileToPatch);

        userClient.updateCreditProfile(userId, creditProfileToPatch);
    }

    private void mergePreApprovals(CreditProfile creditProfile, CreditProfile creditProfileToPatch) {
        if(creditProfile.getPreApprovals() != null){
            var existingPreApprovals =
                    Optional.ofNullable(creditProfileToPatch)
                            .map(CreditProfile::getPreApprovals)
                            .orElseGet(HashMap::new);

            existingPreApprovals.putAll(creditProfile.getPreApprovals());
            creditProfileToPatch.setPreApprovals(existingPreApprovals);

            //set null to skip BeanUtils.copyNonNullProperties(...);
            creditProfile.setPreApprovals(null);
        }
    }

    private void updateAllCertificateWithPreApprovalDate(String userId, CreditPreApproval preApproval){
        var certificates = certificateClient.findActiveByUser(userId, PageRequestUtils.maxSizeRequest())
                .getContent()
                .stream()
                .filter(certificate -> {
                    var preApprovalNumber = Optional.ofNullable(certificate.getDealPreferences())
                            .map(DealPreferences::getAppliedPreApproval)
                            .map(CreditPreApproval::getApprovalNumber).orElse("");

                    return preApproval.getApprovalNumber().equals(preApprovalNumber);
                })
                .collect(Collectors.toList());

        certificates.forEach(certificate -> {
            var dealPreferences = certificate.getDealPreferences();
            Optional.ofNullable(dealPreferences.getAppliedPreApproval())
                    .ifPresent(appliedPreApproval -> appliedPreApproval.setPreApprovalDate(preApproval.getPreApprovalDate()));
            var dealPreferenceRequest = DealPreferenceRequest.builder()
                    .dealPreferences(dealPreferences)
                    .build();

            certificateClient.update(certificate.getId(),dealPreferenceRequest);
        });
    }
}
