package com.carsaver.partner.web.api;

import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.protectioproducts.ProtectionProducts;
import com.carsaver.partner.exception.MissingRequiredFieldException;
import com.carsaver.partner.service.protection_products.ProtectionProductsRequestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@Slf4j
@RestController
public class ProtectionProductsController {

    private final ProtectionProductsRequestService protectionProductsRequestService;

    public ProtectionProductsController(ProtectionProductsRequestService protectionProductsRequestService) {
        this.protectionProductsRequestService = protectionProductsRequestService;
    }

    @PreAuthorize("hasPermission(#certificate, 'customer:read') or hasAuthority('read:user')")
    @GetMapping("/api/deal/protection-product/{certificate}")
    public ResponseEntity getProtectionProducts(@PathVariable CertificateView certificate) {

        Optional<ProtectionProducts> response;
        try {
            ProtectionProducts protectionProducts = protectionProductsRequestService.getProtectionProducts(certificate);
            response = Optional.ofNullable(protectionProducts);

        } catch(MissingRequiredFieldException ex) {
            return ResponseEntity.badRequest().body(ex.getMessage());
        } catch(Exception ex){
            //generic message, so we do not return a stack error message if unexpected error is thrown
            log.error("Exception thrown getting the deal's Protection Products {}",ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error has occurred");
        }

        return  ResponseEntity.ok().body(response);

    }

}
