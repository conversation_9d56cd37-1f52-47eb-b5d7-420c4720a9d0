package com.carsaver.partner.web.api;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/features")
public class FeaturesApiController {

    @Value("${atlas.features:}")
    private List<String> features;

    @Cacheable("features")
    @GetMapping
    public List<String> getFeatures() {
        return this.features;
    }
}
