package com.carsaver.partner.web.api;

import com.carsaver.core.DealerStatus;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.DealerProgramURL;
import com.carsaver.partner.service.DealerProgramConvertor;
import com.carsaver.partner.service.DealerProgramURLConvertor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class DealerProgramApiController {


    @Autowired
    private ProgramSubscriptionClient programSubscriptionClient;

    @Autowired
    private DealerProgramConvertor dealerProgramConvertor;

    @Autowired
    private DealerProgramURLConvertor dealerProgramURLConvertor;

    @Value("${features-toggle.buy-at-home-program-enable}")
    private boolean isBuyAtHomeProgramEnabled;

    @Value("${program.nissan-buy-at-home-id}")
    private String buyAtHomeProgramId;


    @PreAuthorize("hasPermission(#dealerId, 'dealer:read')")
    @GetMapping("/api/dealer/{dealerId}/programs")
    public ResponseEntity<List<DealerProgram>> getDealerPrograms(@PathVariable String dealerId) {
        List<DealerProgram> dealerPrograms = programSubscriptionClient.findByDealer(dealerId).getContent()
            .stream()
            .filter(p -> p.getStatus() != DealerStatus.CANCELLED)
            .map(dealerProgramConvertor::convert)
            .collect(Collectors.toList());

        return ResponseEntity.ok(dealerPrograms);
    }

    @GetMapping(value = "/api/dealer/{dealerId}/buy-at-home-program-subscription")
    public ResponseEntity<ProgramSubscriptionExistsResponse> fetchBuyAtHomeProgram(@PathVariable String dealerId) {
        final ProgramSubscriptionExistsResponse response = new ProgramSubscriptionExistsResponse();
        if (isBuyAtHomeProgramEnabled) {
            programSubscriptionClient.findByProgramAndDealer(buyAtHomeProgramId, dealerId)
                .ifPresentOrElse(programSubscriptionView -> {
                        response.setProgramId(programSubscriptionView.getProgramId());
                        response.setActive(programSubscriptionView.isActive());
                        response.setExists(true);
                    },
                    () -> {
                        response.setProgramId(null);
                        response.setActive(false);
                        response.setExists(false);
                    });
            return ResponseEntity.ok(response);
        }
        return ResponseEntity.badRequest().body(response);
    }

    @PreAuthorize("hasPermission(#dealerId, 'dealer:read')")
    @GetMapping("/api/dealer/{dealerId}/program-urls")
    public ResponseEntity<List<DealerProgramURL>> getDealerProgramsURLs(@PathVariable String dealerId) {
        List<DealerProgramURL> dealerPrograms = programSubscriptionClient.findByDealer(dealerId).getContent()
            .stream()
            .filter(p -> p.getStatus() != DealerStatus.CANCELLED)
            .map((ProgramSubscriptionView programSubscription) -> dealerProgramURLConvertor.convert(programSubscription, dealerId))
            .collect(Collectors.toList());

        return ResponseEntity.ok(dealerPrograms);
    }


    @Data
    @NoArgsConstructor
    public static class ProgramSubscriptionExistsResponse {
        String programId;
        boolean exists;
        boolean active;
    }
}
