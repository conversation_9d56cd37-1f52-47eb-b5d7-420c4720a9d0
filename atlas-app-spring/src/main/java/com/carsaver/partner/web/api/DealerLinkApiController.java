package com.carsaver.partner.web.api;

import com.carsaver.core.DealerLinkStatus;
import com.carsaver.magellan.api.DealerLinkService;
import com.carsaver.magellan.model.ResolutionView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api/dealer-links")
public class DealerLinkApiController {
    private final DealerLinkService dealerLinkService;

    @Autowired
    public DealerLinkApiController(DealerLinkService dealerLinkService) {
        this.dealerLinkService = dealerLinkService;
    }

    @GetMapping("/resolutions")
    public List<ResolutionView> getDealerLinkResponses() {

        return dealerLinkService.getResolutions();
    }

    @GetMapping("/statuses")
    public List<DealerLinkStatus> getDealerLinkStatuses() {

        return Arrays.asList(DealerLinkStatus.INVALID, DealerLinkStatus.LOST, DealerLinkStatus.SOLD, DealerLinkStatus.WORKING);
    }
}
