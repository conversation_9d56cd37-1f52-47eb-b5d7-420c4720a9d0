package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.partner.model.lead.LeadTypes;
import com.carsaver.partner.salesstages.SalesStages;
import com.carsaver.partner.search.facets.UserDocFacets;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.util.SearchHelper;
import com.carsaver.partner.web.api.user.elasticsearch.model.CriteriaAggregationRequest;
import com.carsaver.partner.web.api.user.elasticsearch.model.CriteriaFilterRequest;
import com.carsaver.partner.web.api.user.elasticsearch.model.DealerLinkCriteria;
import com.carsaver.partner.web.api.user.elasticsearch.model.EligibleProspectCriteria;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchCriteria;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchRequest;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.model.IntegerRange;
import com.carsaver.search.model.SearchMethod;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.carsaver.partner.web.api.user.elasticsearch.CommonHelper.EXCLUDE_PROSPECT_STAGES_BUT_ELIGIBLE;
import static com.carsaver.partner.web.api.user.elasticsearch.CommonHelper.NESTED_PROGRAM_SUBSCRIPTION_STAGE_BY_DEALER_ID;
import static com.carsaver.partner.web.api.user.elasticsearch.CommonHelper.NESTED_VOI_FACETS_BY_DEALER_ID;
import static com.carsaver.partner.web.api.user.elasticsearch.CommonHelper.handleCreatedDateFilter;
import static com.carsaver.partner.web.api.user.elasticsearch.CommonHelper.handleLastActiveFilter;
import static com.carsaver.partner.web.api.user.elasticsearch.CommonHelper.handleMaturityDateFilter;

@Slf4j
@Service
public class DealerUserElasticSearchService {

    public static final String NESTED_STATUS_DEALER_LINKS = "status";
    public static final String DEALER_LINK_STATUS_NESTED = "statusNested";
    public static final String USERS_LEAD_CONNECTION_TYPE_NESTED = "connectionLeads";
    public static final String USERS_LEAD_OTHERS_TYPE_NESTED = "otherLeads";
    public static final String LANGUAGE = "language";

    public static final String[] INCLUDES = new String[]{
        "id",
        "tenant",
        "firstName",
        "lastName",
        "stage",
        "rankedStage",
        "phoneNumber",
        "eligible",
        "email",
        "smsEnabled",
        "signUpVehicle",
        "vehicleOfInterest",
        "timeZone",
        "address",
        "tags",
        "tenant",
        "program",
        "traits",
        "createdDate",
        "enrollmentDate",
        "prospectSinceDate",
        "lastModifiedDate",
        "lastLoginAt",
        "preApprovalCount",
        "programSubscriptionStages",
        "websiteVisitsBeforeSignup",
        "docType",
        "callsAttempted",
        "callsConnected",
        "clientAdvisor",
        "tradeYear",
        "tradeMake",
        "tradeModel",
        "tradeTrim",
        "tradeVin",
        "tradeStyleId",
        "tradeOffer",
        "tradeMileage",
        "tradeEquity",
        "tradePayment",
        "tradePaymentType",
        "maturityDate",
        "tier",
        "lenderId",
        "lenderName",
        "remainingPayments",
        "tradePayoff",
        "tradeRate",
        "tradeTerm",
        "tradeValue",
        "logins",
        "type",
        "lastActives",
        "tradePurchaseType",
        "tradePurchaseDate",
        "websiteVisits"
    };
    private final CustomersDocService customersDocService;
    private final boolean excludeTestUsers;
    private final ExportService exportService;
    private final SearchHelper searchHelper;
    private final SplitFeatureFlags splitFeatureFlags;
    private final LocaleService localeService;

    public DealerUserElasticSearchService(
        CustomersDocService customersDocService,
        @Value("${features-toggle.exclude-test-users}") boolean excludeTestUsers,
        ExportService exportService,
        SearchHelper searchHelper,
        SplitFeatureFlags splitFeatureFlags,
        LocaleService localeService
    ) {
        this.customersDocService = customersDocService;
        this.excludeTestUsers = excludeTestUsers;
        this.exportService = exportService;
        this.searchHelper = searchHelper;
        this.splitFeatureFlags = splitFeatureFlags;
        this.localeService = localeService;
    }

    public Optional<UserAndProspectDoc> search(String userId) {
        return Optional.ofNullable(customersDocService.findById(userId));
    }

    public SearchResults<UserAndProspectDoc> search(SearchRequest request, Pageable pageable) {
        final CriteriaFilterRequest searchForm = buildSearchCriteria(request);

        return customersDocService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));
    }

    public FacetInfoResult findFacetInfo(SearchRequest request, String facet) {
        facet = handleStageFacet(facet);
        facet = handleVehicleOfInterestFacet(facet);

        if (splitFeatureFlags.isEnableElasticNestedFields() && Objects.equals(facet, "status")) {
            facet = "statusNested";
        }

        final CriteriaAggregationRequest searchForm = buildFacetCriteria(request, facet);

        if ("tradeInsCounts".equalsIgnoreCase(facet)) {
            FacetInfoResult facetInfoResult = customersDocService.facets(searchForm, UserDocFacets.class, facet);
            List<TermFacet> tradeInsCounts = (List<TermFacet>) facetInfoResult.getResults();
            List<TermFacet> validTradeInsCounts = new ArrayList<>();

            long yesCount = tradeInsCounts.stream().filter(it -> !it.getId().equals("0")).map(TermFacet::getCount).mapToLong(Long::longValue).sum();
            long noCount = tradeInsCounts.stream()
                .filter(it -> it.getId().equals("0"))
                .mapToLong(TermFacet::getCount)
                .sum();

            final TermFacet yesResult = TermFacet.builder().id("Yes").name("Yes").count(yesCount).uniqueCount(0).build();
            final TermFacet noResult = TermFacet.builder().id("No").name("No").count(noCount).uniqueCount(0).build();

            validTradeInsCounts.add(yesResult);
            validTradeInsCounts.add(noResult);
            facetInfoResult.setResults(validTradeInsCounts.toArray());

            return facetInfoResult;
        }

        String userLeadFacet = "userLeads";
        if (USERS_LEAD_CONNECTION_TYPE_NESTED.equalsIgnoreCase(facet) || USERS_LEAD_OTHERS_TYPE_NESTED.equalsIgnoreCase(facet)) {
            FacetInfoResult facetInfoResult = customersDocService.facets(searchForm, UserDocFacets.class, userLeadFacet);
            List<TermFacet> filteredResults = handleUserLeadFacet(facetInfoResult, facet);

            facetInfoResult.setResults(filteredResults);
            return facetInfoResult;
        }

        if (Objects.equals(facet, LANGUAGE)) {
            return handleLanguageFacet(searchForm, facet);
        }

        return customersDocService.facets(searchForm, UserDocFacets.class, facet);
    }

    private FacetInfoResult handleLanguageFacet(CriteriaAggregationRequest searchForm, String facet) {
        FacetInfoResult facetInfoResult = customersDocService.facets(searchForm, UserDocFacets.class, facet);
        List<TermFacet> filteredResults = (List<TermFacet>) facetInfoResult.getResults();

        filteredResults = filteredResults.stream()
            .filter(r -> {
                String languageCode = r.getName();
                return LocaleService.LANG_CODE_EN.equals(languageCode) || LocaleService.LANG_CODE_ES.equals(languageCode);
            })
            .peek(r -> {
                String languageName = localeService.getLanguageNameByCode(r.getName());
                r.setName(languageName);
                r.setId(languageName);
            })
            .collect(Collectors.toList());

        facetInfoResult.setResults(filteredResults);
        return facetInfoResult;
    }

    private List<TermFacet> handleUserLeadFacet(FacetInfoResult facetInfoResult, String facet) {
        List<TermFacet> leadTypeCount = (List<TermFacet>) facetInfoResult.getResults();

        return leadTypeCount.stream()
            .filter(leadType -> {
                if (USERS_LEAD_CONNECTION_TYPE_NESTED.equalsIgnoreCase(facet)) {
                    return LeadTypes.CONNECTION_LEAD_TYPES.contains(leadType.getName());
                } else if (USERS_LEAD_OTHERS_TYPE_NESTED.equalsIgnoreCase(facet)) {
                    return LeadTypes.OTHERS_LEAD_TYPES.contains(leadType.getName());
                }
                return false; // Default to false if no condition matches
            })
            .sorted((t1, t2) -> t1.getName().compareToIgnoreCase(t2.getName()))
            .collect(Collectors.toList());
    }

    private String handleVehicleOfInterestFacet(String facet) {
        if ("vehicleOfInterestSavedOrViewed".equals(facet)) {
            return "dealerCertificateSaved";
        }
        if ("vehicleOfInterestStockTypes".equals(facet)) {
            return "dealerCertificateStockType";
        }
        if ("vehicleOfInterestCertified".equals(facet)) {
            return "dealerCertificateCertified";
        }
        if ("vehicleOfInterestYears".equals(facet)) {
            return "dealerCertificateYear";
        }
        if ("vehicleOfInterestMakes".equals(facet)) {
            return "dealerCertificateMakes";
        }
        if ("vehicleOfInterestModels".equals(facet)) {
            return "dealerCertificateModels";
        }
        return facet;
    }

    public void exportUserToCsv(
        HttpServletResponse response,
        List<String> columns,
        List<String> labels,
        SearchRequest request,
        String timezone
    ) throws Exception {
        final CriteriaFilterRequest searchForm = buildSearchCriteria(request);
        exportService.exportUserToCsv(
            response,
            columns,
            labels,
            searchForm,
            timezone
        );
    }

    private @NotNull CriteriaFilterRequest buildSearchCriteria(SearchRequest request) {
        final CriteriaFilterRequest searchForm = new CriteriaFilterRequest(INCLUDES);

        //copy filters from UI
        BeanUtils.copyProperties(request, searchForm);

        final List<String> dealerACLs = List.of(request.getDealerIds());
        searchForm.setDealerAcls(dealerACLs);

        handleProgramSubscriptionStagesFilter(request, searchForm);
        handleNestedFilters(searchForm, request, dealerACLs);
        handleMaturityDateFilter(request, searchForm);
        handleLastActiveFilter(request, searchForm);
        handleCreatedDateFilter(request, searchForm);
        handleHasTradeInFilter(request, searchForm);
        handleLanguageFilter(request, searchForm);

        if (splitFeatureFlags.isEnableElasticNestedFields() && !StringUtils.isEmpty(request.getStatus())) {
            searchForm.setStatusNested(request.getStatus());
            searchForm.setStatus(null);
        } else {
            handleCustomerStatus(request, searchForm);
        }

        //set should criteria - STARTs
        setEligibleProspectCriteria(searchForm, dealerACLs);
        setDealerLinksCriteria(searchForm, dealerACLs, request.getDealerLinks());
        //set should criteria - END

        setExcludeTestUsers(searchForm);
        setExcludeProspects(searchForm);
        return searchForm;
    }

    void handleLanguageFilter(SearchRequest request, SearchCriteria searchForm) {
        if (request.getLanguage() != null && !request.getLanguage().isEmpty()) {
            List<String> languageCodes = request.getLanguage().stream()
                .map(localeService::getLanguageCodeByName)
                .collect(Collectors.toList());

            searchForm.setLanguage(languageCodes);
        }
    }

    private static void handleProgramSubscriptionStagesFilter(SearchRequest request, SearchCriteria searchForm) {
        searchForm.setProgramSubscriptionStages(request.getStages());

        // Workaround for excluded stages.
        if (searchForm.getSearchMethods().containsKey("stages")) {
            final SearchMethod stageSearchMethod = searchForm.getSearchMethods().get("stages");
            searchForm.getSearchMethods().put("programSubscriptionStages", stageSearchMethod);
            searchForm.getSearchMethods().put("stageDealerId", stageSearchMethod);
        }
    }

    private void setDealerIdForVOI(SearchCriteria searchForm, SearchRequest request, String dealerId) {

        if (!CollectionUtils.isEmpty(request.getVehicleOfInterestSavedOrViewed())) {
            searchForm.setDealerInventoryVehiclesId(dealerId);
        }

        if (!CollectionUtils.isEmpty(request.getVehicleOfInterestStockTypes())) {
            searchForm.setDealerInventoryVehiclesId(dealerId);
        }

        if (!CollectionUtils.isEmpty(request.getVehicleOfInterestCertified())) {
            searchForm.setDealerInventoryVehiclesId(dealerId);
        }

        if (!CollectionUtils.isEmpty(request.getVehicleOfInterestYears())) {
            searchForm.setDealerInventoryVehiclesId(dealerId);
        }

        if (!CollectionUtils.isEmpty(request.getVehicleOfInterestMakes())) {
            searchForm.setDealerInventoryVehiclesId(dealerId);
        }

        if (!CollectionUtils.isEmpty(request.getVehicleOfInterestModels())) {
            searchForm.setDealerInventoryVehiclesId(dealerId);
        }
    }

    private @NotNull CriteriaAggregationRequest buildFacetCriteria(SearchRequest request, String facet) {
        final CriteriaAggregationRequest searchForm = new CriteriaAggregationRequest(INCLUDES);

        //copy filters from UI
        BeanUtils.copyProperties(request, searchForm);

        //secure query for current user dealer access list - START
        final List<String> dealerACLs = List.of(request.getDealerIds());
        searchForm.setDealerAcls(dealerACLs);
        //secure query for current user dealer access list - END

        handleProgramSubscriptionStagesFilter(request, searchForm);
        handleNestedAggregate(searchForm, facet, dealerACLs);
        handleMaturityDateFilter(request, searchForm);
        handleLastActiveFilter(request, searchForm);
        handleCreatedDateFilter(request, searchForm);
        handleHasTradeInFilter(request, searchForm);
        handleLanguageFilter(request, searchForm);

        if (splitFeatureFlags.isEnableElasticNestedFields() && !StringUtils.isEmpty(request.getStatus())) {
            searchForm.setStatusNested(request.getStatus());
            searchForm.setStatus(null);
        } else {
            handleCustomerStatus(request, searchForm);
        }

        //set should criteria - START
        setEligibleProspectCriteria(searchForm, dealerACLs);
        setDealerLinksCriteria(searchForm, dealerACLs, request.getDealerLinks());
        //set should criteria - END

        //Set search method based on user action
        searchHelper.normalizeSearchMethods(searchForm, request, true);

        //Vehicle of Interest facets required dealerId
        setDealerIdForVOI(searchForm, request, dealerACLs.get(0));

        setExcludeTestUsers(searchForm);
        setExcludeProspects(searchForm);
        return searchForm;
    }

    /**
     * determine which facet to use for stage. Use the global stage if there are multiple dealers
     */
    String handleStageFacet(String facet) {
        if ("stages".equals(facet)) {
            return NESTED_PROGRAM_SUBSCRIPTION_STAGE_BY_DEALER_ID;
        }
        return facet;
    }

    void handleCustomerStatus(SearchRequest request, SearchCriteria searchForm) {
        if (StringUtils.isEmpty(request.getStatus())) {
            return;
        }
        searchForm.setStatus(request.getStatus().stream().map(String::toUpperCase).collect(Collectors.toList()));
    }

    void handleNestedFilters(CriteriaFilterRequest searchForm, SearchRequest request, List<String> dealerACLs) {
        if (CollectionUtils.isEmpty(dealerACLs)) {
            throw new IllegalArgumentException("Dealer ACLs must contain exactly one dealer id");
        }

        searchForm.setNestedSortFilterValue(dealerACLs.get(0));
        //Program subscription Stages required dealerId
        if (!CollectionUtils.isEmpty(searchForm.getProgramSubscriptionStages())) {
            searchForm.setStageDealerId(dealerACLs.get(0));
        }

        searchHelper.normalizeSearchMethods(searchForm, request, true);
        setDealerIdForVOI(searchForm, request, dealerACLs.get(0));

        if (splitFeatureFlags.isEnableElasticNestedFields() && !StringUtils.isEmpty(request.getStatus())) {
            searchForm.setStatusDealerId(dealerACLs.get(0));
        }

        if (!StringUtils.isEmpty(request.getDealerIds()) && (!StringUtils.isEmpty(request.getConnectionLeads())
            || (!StringUtils.isEmpty(request.getOtherLeads())))) {
            searchForm.setUserLeadsDealerId(dealerACLs.get(0));
        }
    }

    void handleNestedAggregate(CriteriaAggregationRequest searchForm, String facet, List<String> dealerACLs) {
        if (CollectionUtils.isEmpty(dealerACLs)) {
            throw new IllegalArgumentException("Dealer ACLs must contain exactly one dealer id");
        }

        if (StringUtils.isEmpty(facet)) {
            throw new IllegalArgumentException("Facet must not be empty");
        }

        //Program subscription Stages required dealerId
        if (NESTED_PROGRAM_SUBSCRIPTION_STAGE_BY_DEALER_ID.equals(facet)) {
            searchForm.setNestedAggregateFilterValueForProgramSubscriptionStages(dealerACLs.get(0));
        }

        //Vehicle of Interest facets required dealerId
        if (NESTED_VOI_FACETS_BY_DEALER_ID.contains(facet)) {
            searchForm.setNestedAggregateFilterValueForDealerInventoryVehicles(dealerACLs.get(0));
        }

        if (NESTED_PROGRAM_SUBSCRIPTION_STAGE_BY_DEALER_ID.contains(facet) &&
            (!StringUtils.isEmpty(searchForm.getConnectionLeads()) || !StringUtils.isEmpty(searchForm.getOtherLeads()))) {
            searchForm.setUserLeadsDealerId(dealerACLs.get(0));
        }

        if (USERS_LEAD_CONNECTION_TYPE_NESTED.contains(facet) || USERS_LEAD_OTHERS_TYPE_NESTED.contains(facet)) {
            searchForm.setUserLeadsDealerId(dealerACLs.get(0));
        }
    }

    void handleHasTradeInFilter(SearchRequest request, SearchCriteria searchForm) {
        if (StringUtils.isEmpty(request.getHasTradeIn())) {
            return;
        }

        if ("yes".equalsIgnoreCase(request.getHasTradeIn())) {
            searchForm.setHasTradeIns(IntegerRange.builder().start(1).build());
        }

        if ("no".equalsIgnoreCase(request.getHasTradeIn())) {
            searchForm.setHasTradeIns(IntegerRange.builder().end(0).build());
        }
    }

    void setEligibleProspectCriteria(SearchCriteria searchCriteria, List<String> dealerACLs) {
        if (CollectionUtils.isEmpty(dealerACLs) || dealerACLs.size() > 1) {
            throw new IllegalArgumentException("Dealer ACLs must contain exactly one dealer id");
        }

        EligibleProspectCriteria eligibleProspectByDealerSearchCriteria = EligibleProspectCriteria
            .builder()
            .eligibleProspectStage(SalesStages.ELIGIBLE_PROSPECT.getStageTitle())
            .upgradeProspectDealerId(dealerACLs.get(0))
            .dealerACLs(dealerACLs)
            .build();

        searchCriteria.setEligibleProspectCriteria(eligibleProspectByDealerSearchCriteria);
    }

    void setDealerLinksCriteria(SearchCriteria searchCriteria, List<String> dealerACLs, List<String> dealerLinksFromRequest) {
        final List<String> dealerLinks = CollectionUtils.isEmpty(dealerLinksFromRequest) ? dealerACLs : dealerLinksFromRequest;
        if (!CollectionUtils.isEmpty(dealerLinks)) {
            if (splitFeatureFlags.isEnableElasticNestedFields()) {
                searchCriteria.setDealerLinkCriteria(new DealerLinkCriteria(null, dealerLinks));
            } else {
                searchCriteria.setDealerLinkCriteria(new DealerLinkCriteria(dealerLinks, null));
            }
        }
    }

    void setExcludeTestUsers(SearchCriteria searchCriteria) {
        searchCriteria.setExcludeTestTag(excludeTestUsers ? "test" : null);
    }

    void setExcludeProspects(SearchCriteria searchCriteria) {
//        criteriaRequest.setExcludeStages(EXCLUDE_PROSPECT_STAGES);
        searchCriteria.setExcludeStages(EXCLUDE_PROSPECT_STAGES_BUT_ELIGIBLE);
    }

    public SearchResults<UserAndProspectDoc> searchInShowroomUsers(String dealerId) {
        Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE);

        final CriteriaFilterRequest searchForm = buildSearchCriteria(createSearchRequestForShowroomUsers(dealerId));
        searchForm.setMostRecentDealerCheckIn(dealerId);

        return customersDocService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));
    }

    public SearchRequest createSearchRequestForShowroomUsers(String dealerId) {
        return SearchRequest.builder()
            .searchMethods(new HashMap<>())
            .dealerIds(dealerId)
            .build();
    }

}
