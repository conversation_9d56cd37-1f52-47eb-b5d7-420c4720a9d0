package com.carsaver.partner.web.api.training.user.model;

import com.carsaver.magellan.model.trainingvideo.TrainingCourseView;
import com.carsaver.magellan.model.trainingvideo.TrainingVideoRecordView;
import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
public class TrainingCourse {

    private String id;
    private String title;
    private String description;
    private int sort;
    List<UserTrainingVideo> videos;

    public static TrainingCourse from(TrainingCourseView course, Collection<TrainingVideoRecordView> userRecord) {

        var builder = TrainingCourse.builder()
                .id(course.getId())
                .title(course.getTitle())
                .sort(course.getSort());
        if (course.getTrainingVideos() != null){
            builder.videos(course.getTrainingVideos().stream().map(video -> UserTrainingVideo.from(video,userRecord)).collect(Collectors.toList()));
        }
        return builder.build();
    }
}