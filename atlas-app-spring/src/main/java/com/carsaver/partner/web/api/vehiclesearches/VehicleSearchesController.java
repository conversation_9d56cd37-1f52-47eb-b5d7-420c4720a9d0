package com.carsaver.partner.web.api.vehiclesearches;

import com.carsaver.partner.service.vehiclesearches.VehicleSearches;
import com.carsaver.partner.service.vehiclesearches.VehicleSearchesService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
public class VehicleSearchesController {

    private final VehicleSearchesService service;

    @GetMapping("/api/vehicle-searches")
    public List<VehicleSearches> getVehicleSearches(@RequestParam String userId, @RequestParam String dealerId) {
        return service.process(userId, dealerId);
    }
}
