package com.carsaver.partner.web.api.user.elasticsearch.model;

import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Getter
public class ProgramCriteria extends AbstractSearchCriteria {

    // Intended for program user
    @TermQuery(field = "program.id")
    private List<String> programIds;

    @TermQuery(field = "dealerAcls")
    private List<String> dealerAcls;
}
