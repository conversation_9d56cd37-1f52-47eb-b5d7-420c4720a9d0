package com.carsaver.partner.web;

import com.carsaver.partner.filter.DealerUserAccessFilter;
import com.carsaver.partner.model.ProgramModel;
import com.carsaver.partner.model.user.UserView;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.DealerEntryPointsService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.SessionAttribute;

import java.util.List;
import java.util.Objects;

@Slf4j
@Controller
public class DealerEntryPointsController {

    public static final String DEFAULT_MAIN_LANDING_PAGE = "main";

    @Autowired
    private DealerEntryPointsService service;

    @Autowired
    private SplitFeatureFlags splitFeatureFlags;
    @GetMapping({"/"})
    public String dashboard(@SessionAttribute(DealerUserAccessFilter.PROGRAMS_LIST) List<ProgramModel> programs) {
        if (SecurityUtils.isProgramUser()) {
            return redirectProgramUser(programs);
        }
        return DEFAULT_MAIN_LANDING_PAGE;
    }

    String redirectProgramUser(List<ProgramModel> programs) {
        boolean isSingleProgram = !CollectionUtils.isEmpty(programs) && programs.size() == 1;

        // For program users logging in that have a single program, redirect them to their program
        if (isSingleProgram) {
            String programId = programs.get(0).getId();
            return "redirect:/customers?programIds=" + programId;
        }

        return DEFAULT_MAIN_LANDING_PAGE;
    }

    @PreAuthorize("hasPermission(#dealerIds, 'dealer:read')")
    @GetMapping(value = "/dealer/details", params = {"dealerIds"})
    public String main(@RequestParam("dealerIds") String dealerIds) {
        return "dealer";
    }

    //    @PreAuthorize("hasPermission(#dealerIds, 'customer:read')")
    @GetMapping(value = "/customers/**")
    public String customers(@RequestParam(value = "programIds", required = false) String programIds, @RequestParam(value = "dealerIds", required = false) String dealerIds) {
        return "customers";
    }

    @GetMapping("/leads/**")
    public String leads() {
        return "leads";
    }

    @GetMapping("/dealer/{dealerId}/leads/{leadId}")
    public String baseLeadDetails(@PathVariable String dealerId, @PathVariable String leadId) {
        Objects.requireNonNull(dealerId);
        Objects.requireNonNull(leadId);
        return String.format("redirect:/leads/%s?dealerIds=%s", leadId, dealerId);
    }

    @GetMapping("/vehicles/**")
    public String vehicles() {
        return "vehicles";
    }

    @PreAuthorize("hasPermission(#dealerIds, 'inventory:new-pricing:read') or hasPermission(#dealerIds, 'inventory:used-pricing:read')")
    @GetMapping(value = "/pricing/**", params = {"dealerIds"})
    public String pricing(@RequestParam List<String> dealerIds) {
        return "pricing";
    }

    @GetMapping("/prospects/**")
    public String prospects() {
        return "prospects";
    }

    @GetMapping("/reporting/**")
    public String reporting() {
        return "reporting";
    }

    @GetMapping("/sales/**")
    public String sales() {
        return "sales";
    }

    @GetMapping("/userSelfService")
    public String userSelfService() {
        return "userSelfService";
    }

    @GetMapping("/training/**")
    public String training() {
        return "training";
    }

    @GetMapping(value = "/protection-products/**", params = {"dealerIds"})
    public String protectionProducts() {
        return "protectionProducts";
    }

    @GetMapping("/widget-documentation/**")
    public String widgetDocumentation() {
        return "widgetDocumentation";
    }

    @PreAuthorize("hasPermission(#dealerIds, 'website-cta:read')")
    @GetMapping(value = "/widget-customization/**", params = {"dealerIds"})
    public String widgetCustomization(@RequestParam("dealerIds") String dealerIds) {
        return "widget";
    }

    @GetMapping("/dealer/supported-accessories")
    public String accessories() {
        return "accessories";
    }

    @GetMapping("/dealers")
    public String dealeres() {
        return "dealers";
    }

    @GetMapping("/deals/{dealId}")
    public String customers(@PathVariable("dealId") Integer certificateId, @RequestParam List<String> dealerIds) {
        UserView user = service.searchUser(certificateId);
        return "redirect:/customers/" + user.getId() + "?dealerIds=" + dealerIds.get(0);
    }


    @PreAuthorize("hasPermission(#dealerIds, 'return-policy:read')")
    @GetMapping(value = "/return-policy/**", params = {"dealerIds"})
    public String returnPolicy(@RequestParam("dealerIds") String dealerIds) {
        String result =  "returnPolicy";
        return result;
    }

    @PreAuthorize("hasPermission(#dealerIds, 'trade-in-adjustment:read')")
    @GetMapping(value = "/trade-in-adjustment/**", params = {"dealerIds"})
    public String tradeInAdjustment(@RequestParam("dealerIds") String dealerIds) {
        String result =  "tradeInAdjustment";
        return result;
    }
    @GetMapping(value = "/sell-at-home/**", params = {"dealerIds"})
    public String sellAtHome(@RequestParam("dealerIds") String dealerIds) {
        String result = splitFeatureFlags.isSellAtHomeFeatureEnabled() ? "sellAtHome" : DEFAULT_MAIN_LANDING_PAGE;
        return result;
    }

    @GetMapping(value = "/domo/**")
    public String domo() {
        return splitFeatureFlags.isDomoIntegrationEnabled() ? "domo" : DEFAULT_MAIN_LANDING_PAGE;
    }

    @GetMapping(value = "/walmart", params = {"dealerIds"})
    public String walmart(@RequestParam("dealerIds") String dealerIds) {

        return "walmart";
    }
}
