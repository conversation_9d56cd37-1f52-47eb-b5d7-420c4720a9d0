package com.carsaver.partner.web.form;

import com.carsaver.magellan.client.request.UpdateRequest;
import com.carsaver.magellan.validation.EmailDomain;
import com.carsaver.partner.validation.UniqueEmail;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class UserUpdateForm implements UpdateRequest {

    private String userId;

    @NotBlank
    @Size(min=1, max=255)
    private String firstName;

    @NotBlank
    @Size(min=1, max=255)
    private String lastName;

    @NotBlank
    @Size(min=1, max=255)
    @EmailDomain
    private String email;

    @NotBlank
    @Pattern(regexp="(^[0-9]{10})")
    private String phoneNumber;
    private String phoneNumberExt;
    private Integer jobTitleId;
    private Boolean internalSms;

}
