package com.carsaver.partner.web.api.user.elasticsearch.model;

import com.carsaver.search.annotation.BoolConfig;
import com.carsaver.search.annotation.BoolQuery;
import com.carsaver.search.annotation.CompoundQuery;
import com.carsaver.search.annotation.MatchQuery;
import com.carsaver.search.annotation.MultiMatchQuery;
import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.IntegerRange;
import com.carsaver.search.model.LocalDateRange;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@BoolConfig(minimumShouldMatch = 1)
public class CriteriaAggregationRequest extends AbstractSearchCriteria implements SearchCriteria {

    public CriteriaAggregationRequest(String[] includes) {
        setIncludes(includes);
    }

    /**
     * Always force test tags to be excluded. Doing it this way allows for us to still filter on
     * tags apart from this forced exclusion if desired.
     */
    @JsonIgnore
    @TermQuery(field = "tags", boolQuery = BoolQueryOccurrence.MUST_NOT, scope = SearchScope.QUERY)
    private String excludeTestTag = "test";

    @JsonIgnore
    @TermQuery(field = "stage", boolQuery = BoolQueryOccurrence.MUST_NOT, scope = SearchScope.QUERY)
    private List<String> excludeStages;

    @MultiMatchQuery(fields = {"firstName", "lastName"}, operator = Operator.AND, type = MultiMatchQueryBuilder.Type.CROSS_FIELDS)
    private String name;

    @MultiMatchQuery(fields = {"phoneNumber", "email"})
    private String emailOrPhone;

    @MatchQuery(field = "address.city")
    private String city;

    @TermQuery(field = "program.id", scope = SearchScope.QUERY)
    private List<String> programs;

    @TermQuery(field = "signUpVehicle.stockType", scope = SearchScope.POST_FILTER)
    private List<String> vehicleStockTypes;

    @TermQuery(field = "signUpVehicle.certified", scope = SearchScope.POST_FILTER)
    private Boolean vehicleCertified;

    @TermQuery(field = "signUpVehicle.year", scope = SearchScope.POST_FILTER)
    private List<Integer> vehicleYears;

    @TermQuery(field = "signUpVehicle.make", scope = SearchScope.POST_FILTER)
    private List<String> vehicleMakes;

    @TermQuery(field = "signUpVehicle.model", scope = SearchScope.POST_FILTER)
    private List<String> vehicleModels;

    @TermQuery(field = "address.dmaCode", scope = SearchScope.POST_FILTER)
    private List<Integer> dmaCodes;

    @RangeQuery(field = "address.dma.rank")
    private IntegerRange topDmas;

    @TermQuery(field = "address.stateCode", scope = SearchScope.POST_FILTER)
    private List<String> states;

    @TermQuery(field = "dealerAcls")
    private List<String> dealerAcls;

    @TermQuery(field = "dealerLinks.dealerId", scope = SearchScope.POST_FILTER)
    private List<String> dealerLinks;

    @TermQuery(field = "dealerId", nested = true, path = "dealerLinks", scope = SearchScope.QUERY, boolQuery = BoolQueryOccurrence.MUST)
    private List<String> dealerLinksNested;

    @TermQuery(field = "dealerLinks.status.keyword", scope = SearchScope.POST_FILTER)
    private List<String> status;

    @TermQuery(field = "status", nested = true, path = "dealerLinks", scope = SearchScope.POST_FILTER)
    private List<String> statusNested;

    @TermQuery(field = "type", nested = true, path = "userLeads", scope = SearchScope.POST_FILTER, boolQuery = BoolQueryOccurrence.MUST)
    private List<String> connectionLeads;

    @TermQuery(field = "type", nested = true, path = "userLeads", scope = SearchScope.POST_FILTER, boolQuery = BoolQueryOccurrence.MUST)
    private List<String> otherLeads;

    @TermQuery(field = "dealerId", nested = true, path = "userLeads", scope = SearchScope.POST_FILTER)
    private String userLeadsDealerId;

    @TermQuery(field = "dealerId", nested = true, path = "dealerLinks", scope = SearchScope.POST_FILTER)
    private String statusDealerId;

    //Needed for nested aggregate filters
    @TermQuery(field = "stageName", nested = true, path = "programSubscriptionStages", scope = SearchScope.POST_FILTER)
    private List<String> programSubscriptionStages;

    @TermQuery(field = "dealerId", nested = true, path = "programSubscriptionStages", scope = SearchScope.POST_FILTER)
    private String stageDealerId;

    @TermQuery(field = "dealerId", nested = true, path = "programSubscriptionStages", scope = SearchScope.POST_FILTER)
    private String nestedAggregateFilterValueForProgramSubscriptionStages;

    @TermQuery(field = "dealerId", nested = true, path = "dealerInventoryVehicles", scope = SearchScope.POST_FILTER)
    private String nestedAggregateFilterValueForDealerInventoryVehicles;

    @TermQuery(field = "stage", scope = SearchScope.QUERY)
    private List<String> globalStage;

    private String zipCode;

    private Integer distance;

    private LocalDateRange purchaseTimeFrame;

    //Bool Queries to include Eligible Prospects with Dealer Ids
    @BoolQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private EligibleProspectCriteria eligibleProspectCriteria;

    @CompoundQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private DealerLinkCriteria dealerLinkCriteria;

    @BoolQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private ProgramCriteria programCriteria;

    @RangeQuery(field = "tradeMileage", scope = SearchScope.POST_FILTER)
    private IntegerRange tradeMileages;

    @RangeQuery(field = "tradeValue", scope = SearchScope.POST_FILTER)
    private IntegerRange tradeValues;

    @RangeQuery(field = "tradeTerm", scope = SearchScope.POST_FILTER)
    private IntegerRange tradeTerms;

    @RangeQuery(field = "maturityDate", scope = SearchScope.POST_FILTER)
    private LocalDateRange maturityDate;

    @TermQuery(field = "tradeMake.keyword", scope = SearchScope.POST_FILTER)
    private List<String> tradeMakes;

    @TermQuery(field = "tradeModel.keyword", scope = SearchScope.POST_FILTER)
    private List<String> tradeModels;

    @TermQuery(field = "tradeYear", scope = SearchScope.POST_FILTER)
    private List<Integer> tradeYears;

    @TermQuery(field = "tradePaymentType.keyword", scope = SearchScope.POST_FILTER)
    private List<String> tradePaymentType;

    @RangeQuery(field = "remainingPayments", scope = SearchScope.POST_FILTER)
    private IntegerRange remainingPayments;

    @RangeQuery(field = "tradePayment", scope = SearchScope.POST_FILTER)
    private IntegerRange tradePayments;

    @RangeQuery(field = "tradeEquity", scope = SearchScope.POST_FILTER)
    private IntegerRange tradeEquity;

    @RangeQuery(field = "lastLoginAt", scope = SearchScope.POST_FILTER)
    private LocalDateRange lastActive;

    @RangeQuery(field = "createdDate", scope = SearchScope.POST_FILTER)
    private ZonedDateRange createdDate;

    @RangeQuery(path = "traits", field = "traits.tradeInsCount", nested = true, scope = SearchScope.POST_FILTER)
    private IntegerRange hasTradeIns;

    @TermQuery(field = "clientAdvisor.keyword", scope = SearchScope.POST_FILTER)
    private List<String> originalSalespersons;

    @TermQuery(field = "dealerId", nested = true, path = "dealerInventoryVehicles", scope = SearchScope.POST_FILTER)
    private String dealerInventoryVehiclesId;

    @TermQuery(field = "savedOrViewed", nested = true, path = "globalInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> globalVehicleOfInterestSavedOrViewed;

    @TermQuery(field = "savedOrViewed", nested = true, path = "dealerInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> dealerVehicleOfInterestSavedOrViewed;

    @TermQuery(field = "stockType", nested = true, path = "globalInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> globalVehicleOfInterestStockTypes;

    @TermQuery(field = "stockType", nested = true, path = "dealerInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> dealerVehicleOfInterestStockTypes;

    @TermQuery(field = "certified", nested = true, path = "globalInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> globalVehicleOfInterestCertified;

    @TermQuery(field = "certified", nested = true, path = "dealerInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> dealerVehicleOfInterestCertified;

    @TermQuery(field = "year", nested = true, path = "globalInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> globalVehicleOfInterestYears;

    @TermQuery(field = "year", nested = true, path = "dealerInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> dealerVehicleOfInterestYears;

    @TermQuery(field = "make", nested = true, path = "globalInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> globalVehicleOfInterestMakes;

    @TermQuery(field = "make", nested = true, path = "dealerInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> dealerVehicleOfInterestMakes;

    @TermQuery(field = "model", nested = true, path = "globalInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> globalVehicleOfInterestModels;

    @TermQuery(field = "model", nested = true, path = "dealerInventoryVehicles", scope = SearchScope.POST_FILTER)
    private List<String> dealerVehicleOfInterestModels;

    @TermQuery(field = "locale", scope = SearchScope.POST_FILTER)
    private List<String> language;
}
