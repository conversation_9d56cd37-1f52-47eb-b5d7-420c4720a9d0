package com.carsaver.partner.web.api;

import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpSession;
import java.util.List;

public interface CustomerBaseController extends BaseListController {

    default List<String> getFinalListOfDealerAndValidateCustomerPermissions(List<String> dealerIds, HttpSession session, SecurityHelperService securityHelperService) {
        final var finalListOfDealerIds = CollectionUtils.isEmpty(dealerIds) ? getDealerIds(session) : dealerIds;
        securityHelperService.checkPermission(SecurityContextHolder.getContext().getAuthentication(), finalListOfDealerIds, "customer:read");

        return finalListOfDealerIds;
    }
}
