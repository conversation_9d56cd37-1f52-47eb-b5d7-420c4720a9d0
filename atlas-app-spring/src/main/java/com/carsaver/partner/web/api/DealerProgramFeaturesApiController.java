package com.carsaver.partner.web.api;

import com.carsaver.partner.exception.BadRequestException;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.service.DealerProgramFeaturesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import javax.validation.Valid;

@Slf4j
@RestController
@RequiredArgsConstructor
public class DealerProgramFeaturesApiController {

    private final DealerProgramFeaturesService dealerProgramFeaturesService;

    @PreAuthorize("hasPermission(#dealerId, 'dealer:read')")
    @PutMapping("/api/dealer/{dealerId}/programs/{programId}/features/update")
    public ResponseEntity<DealerProgram> updateDealerProgramFeature(
        @PathVariable String dealerId,
        @PathVariable String programId,
        @RequestBody @Valid ToggleConfigRequest toggleConfigRequest,
        HttpSession session) {

        try {
            DealerProgram dealerProgram = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);
            return ResponseEntity.ok(dealerProgram);
        } catch (BadRequestException ex) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (InternalServerError ex) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }


}
