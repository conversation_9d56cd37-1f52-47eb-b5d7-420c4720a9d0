package com.carsaver.partner.web;

import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.elasticsearch.model.LeadDoc;
import com.carsaver.elasticsearch.model.UserDoc;
import com.carsaver.elasticsearch.model.program.ProgramDoc;
import com.carsaver.elasticsearch.service.LeadDocService;
import com.carsaver.magellan.model.BaseLeadView;
import com.carsaver.magellan.util.StringUtils;
import com.carsaver.partner.security.SecurityUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Optional;

@Slf4j
@Controller
public class ExternalLeadRedirectController {

    private final LeadDocService leadDocService;

    public ExternalLeadRedirectController(LeadDocService leadDocService) {
        this.leadDocService = leadDocService;
    }

    @GetMapping({"/stratus/lcs/{lead}"})
    public String leadClaim(@PathVariable BaseLeadView lead) {
        if(lead != null) {
            return String.format("redirect:/leads/%s/claim?dealerIds=%s", lead.getId(), lead.getDealerId());
        }

        return "redirect:/";
    }

    @GetMapping("/adf/leads/{leadId}")
    public String adfRedirectLead(@PathVariable String leadId) {
        final LeadRedirectHelper leadWrap = buildLeadRedirectHelper(leadId);
        final String redirectEndpoint = leadWrap.redirectString();
        return "redirect:"+redirectEndpoint;
    }

    @NotNull
    private LeadRedirectHelper buildLeadRedirectHelper(String leadId) {
        LeadRedirectHelper leadWrap = new LeadRedirectHelper();
        Optional<LeadDoc> doc = Optional.ofNullable(leadDocService.findById(leadId));
        if(doc.isPresent()) {
            leadWrap.setUserId(doc.map(LeadDoc::getUser).map(UserDoc::getId).orElse(null));
            leadWrap.setDealerId(doc.map(LeadDoc::getDealer).map(DealerDoc::getId).orElse(null));
            leadWrap.setProgramId(doc.map(LeadDoc::getUser).map(UserDoc::getProgram).map(ProgramDoc::getId).orElse(null));
        }
        return leadWrap;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class LeadRedirectHelper {
        private String userId;
        private String dealerId;
        private String programId;
        public String redirectString() {
            if(StringUtils.isEmpty(userId) || StringUtils.isEmpty(dealerId)) {
                return "/customers";
            }

            if (SecurityUtils.isDealerUser() || SecurityUtils.isAdminUser()) {
                return String.format("/customers/%s?dealerIds=%s", userId, dealerId);
            } else {
                return String.format("/customers/%s?dealerIds=%s&programIds=%s", userId, dealerId,programId);
            }
        }
    }

}
