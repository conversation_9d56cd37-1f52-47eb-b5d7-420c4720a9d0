package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.partner.web.api.user.elasticsearch.model.SearchCriteria;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchRequest;
import com.carsaver.search.model.LocalDateRange;
import com.carsaver.search.model.ZonedDateRange;
import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;

@UtilityClass
public class CommonHelper {

    public static final String GREATER_THAN_12_MONTHS = "12+";
    public static final String GREATER_THAN_90_DAYS = "90+";
    public static final List<String> NESTED_VOI_FACETS_BY_DEALER_ID = List.of(
        "dealerCertificateModels","globalCertificateModels",
        "dealerCertificateMakes","globalCertificateMakes",
        "dealerCertificateYear","globalCertificateYear",
        "dealerCertificateCertified","globalCertificateCertified",
        "dealerCertificateStockType","globalCertificateStockType",
        "dealerCertificateSaved","globalCertificateSaved"
    );
    public static final String NESTED_PROGRAM_SUBSCRIPTION_STAGE_BY_DEALER_ID = "programSubscriptionStage";
    public static final List<String> EXCLUDE_PROSPECT_STAGES_BUT_ELIGIBLE = List.of("Prospect", "Deleted");


    public static void handleMaturityDateFilter(SearchRequest request, SearchCriteria searchForm) {
        if (!StringUtils.hasText(request.getWithinMaturityDate())) {
            return;
        }

        final Integer withinMaturityDate = GREATER_THAN_12_MONTHS.equals(request.getWithinMaturityDate())
            ? null
            : Integer.parseInt(request.getWithinMaturityDate());

        LocalDate today = LocalDate.now();
        if (withinMaturityDate == null) {
            searchForm.setMaturityDate(LocalDateRange.builder().start(today).build());
        } else {
            searchForm.setMaturityDate(LocalDateRange.builder().start(today).end(today.plusDays(withinMaturityDate)).build());
        }
    }

    public static void handleLastActiveFilter(SearchRequest request, SearchCriteria searchForm) {
        if (!StringUtils.hasText(request.getWithinLastActive())) {
            return;
        }

        final Integer withinLastActive = GREATER_THAN_90_DAYS.equals(request.getWithinLastActive())
            ? null
            : Integer.parseInt(request.getWithinLastActive());

        if (withinLastActive == null) {
            searchForm.setLastActive(LocalDateRange.builder().end(LocalDate.now()).build());
        } else {
            searchForm.setLastActive(LocalDateRange.builder().start(LocalDate.now().minusDays(withinLastActive)).end(LocalDate.now()).build());
        }
    }

    public static void handleCreatedDateFilter(SearchRequest request, SearchCriteria searchForm) {
        if (!StringUtils.hasText(request.getWithinCreatedDate())) {
            return;
        }

        final Integer withinCreatedDate = GREATER_THAN_90_DAYS.equals(request.getWithinCreatedDate())
            ? null
            : Integer.parseInt(request.getWithinCreatedDate());

        if (withinCreatedDate == null) {
            ZonedDateRange build = new ZonedDateRange(null, LocalDate.now(), null);
            searchForm.setCreatedDate(build);
        } else {
            ZonedDateRange build = new ZonedDateRange(LocalDate.now().minusDays(withinCreatedDate), LocalDate.now(), null);
            searchForm.setCreatedDate(build);
        }
    }
}
