package com.carsaver.partner.web.api.training.dealer.model.byCourse;

import com.carsaver.magellan.model.trainingvideo.TrainingVideoRecordView;
import com.carsaver.magellan.model.trainingvideo.TrainingVideoView;
import com.carsaver.partner.model.DealerUser;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@Builder
class TrainingVideo {

    private String videoId;
    private String title;
    private String description;
    private Integer sort;
    private String url;
    private List<UserRecord> dealerUsersRecords;

    public static TrainingVideo from(TrainingVideoView video, Map<DealerUser, Collection<TrainingVideoRecordView>> usersRecords) {

        var builder = TrainingVideo.builder()
                .videoId(video.getId())
                .title(video.getTitle())
                .description(video.getDescription())
                .url(video.getUrl())
                .sort(video.getSort());


        List<UserRecord> dealerUserRecords = new ArrayList<>();
        usersRecords.forEach((dealerUser, userRecord) -> {
            var userRecordBuilder = UserRecord.builder();
            userRecordBuilder.userId(dealerUser.getId());
            userRecordBuilder.fullName(dealerUser.getFullName());
            userRecordBuilder.email(dealerUser.getEmail());

            userRecord.stream()
                    .filter(record -> Objects.equals(record.getVideoId(),video.getId()))
                    .findFirst()
                    .ifPresent(record-> userRecordBuilder.completedAt(record.getCompletedAt()));
            dealerUserRecords.add(userRecordBuilder.build());
        });

        builder.dealerUsersRecords(dealerUserRecords);
        return builder.build();
    }
}
