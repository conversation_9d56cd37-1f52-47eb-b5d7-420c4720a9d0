package com.carsaver.partner.web;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.carsaver.partner.model.document_upload.DocumentDetail;
import com.carsaver.partner.model.document_upload.UserDocuments;
import com.carsaver.partner.service.UserDocumentsService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/document_upload")
public class UserDocumentsController {
    final UserDocumentsService userDocumentsService;

    public UserDocumentsController(UserDocumentsService userDocumentsService) {
        this.userDocumentsService = userDocumentsService;
    }

    @GetMapping(value = "/dealer/{dealerId}/user/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<UserDocuments> getUserDocuments(@PathVariable String dealerId, @PathVariable String userId) {

        try {
            List<UserDocuments.Documents> documents = userDocumentsService.getUserDocuments(dealerId, userId);
            UserDocuments userDocument = UserDocuments.builder().documents(documents).build();
            return ResponseEntity.ok(userDocument);
        }
        catch (Exception ex) {
            log.error("Document Upload Exception when retrieving documents for dealer: {}, user: {}, error: {} ", dealerId, userId, ex.getMessage());
            return ResponseEntity.badRequest().body(null);
        }

    }

    @GetMapping(value = "/download/{fileId}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileId) {
        ResponseEntity<Resource> resourceResponseEntity = loadFileByFileId(fileId, "DOWNLOAD");
        return resourceResponseEntity;
    }

    @GetMapping(value = "/view/{fileId}")
    public ResponseEntity<Resource> viewFile(@PathVariable String fileId) {
        ResponseEntity<Resource> resourceResponseEntity = loadFileByFileId(fileId, "VIEW");
        return resourceResponseEntity;
    }

    @NotNull
    private ResponseEntity<Resource> loadFileByFileId(String fileId, String action) {
        DocumentDetail documentDetail = userDocumentsService.loadFileByFileId(fileId);
        String filename = documentDetail.getFileName();
        HttpHeaders httpHeaders = buildHttpHeaders(documentDetail, filename, action);
        Resource resource = new ByteArrayResource(documentDetail.getBytes());
        ResponseEntity<Resource> responseEntity = ResponseEntity.ok().headers(httpHeaders).body(resource);

        return responseEntity;
    }

    private HttpHeaders buildHttpHeaders(DocumentDetail documentDetail, String filename, String type) {
        String file = "; filename=\"" + filename + "\"";
        String dispositionType = type.equals("DOWNLOAD") ? "attachment" : "inline";
        String dispositionHeaderValue = dispositionType + file;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(HttpHeaders.CONTENT_DISPOSITION, dispositionHeaderValue);
        httpHeaders.add(HttpHeaders.CONTENT_TYPE, documentDetail.getMediaType());
        return httpHeaders;
    }

}
