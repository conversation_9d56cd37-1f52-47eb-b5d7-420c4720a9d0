package com.carsaver.partner.web.api;

import com.carsaver.core.StockType;
import com.carsaver.partner.service.DashboardVehicleDocService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/dealers/dashboard")
public class VehiclePricingMetricsApiController {

    @Autowired
    private DashboardVehicleDocService dashboardVehicleDocService;

    @PostMapping("/new-priced")
    public PricedWidget getNewPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardVehicleDocService.getPriced(criteria.getDealerIds(), StockType.NEW);
    }

    @PostMapping("/used-priced")
    public PricedWidget getUsedPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardVehicleDocService.getPriced(criteria.getDealerIds(), StockType.USED);
    }

    @Data
    public static class DealerKpiCriteria {
        private List<String> dealerIds;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DealerKpiDateRangeCriteria extends DealerKpiCriteria {
        @NotNull
        private LocalDate startDate;
        @NotNull
        private LocalDate endDate;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PricedWidget {
        private int pricedCount;
        private int total;
    }

}
