package com.carsaver.partner.web.api;

import com.carsaver.elasticsearch.model.vehicle.VehicleDoc;
import com.carsaver.elasticsearch.service.VehicleDocService;
import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.TinyUrlClient;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.model.BaseView;
import com.carsaver.magellan.model.TinyUrlRequest;
import com.carsaver.magellan.model.TinyUrlView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.InventoryConfig;
import com.carsaver.partner.client.campaign.suppressions.CampaignModelSuppression;
import com.carsaver.partner.client.campaign.suppressions.CampaignModelSuppressionClient;
import com.carsaver.partner.elasticsearch.criteria.VehicleDealerProgramPair;
import com.carsaver.partner.search.facets.VehicleDocFacets;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.DealerProgramService;
import com.carsaver.search.annotation.CompoundQuery;
import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.IntegerRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/vehicles")
public class VehicleApiController implements BaseListController, DealerBaseController {

    private static final String [] EXCLUDES = {
        "bestQuote",
        "categorizedOptions",
        "deal",
        "financeQuote",
        "inventorySource",
        "leaseQuote",
        "sellerNotes",
        "vehicleCategory"
    };

    @Autowired
    private VehicleDocService docService;

    @Autowired
    private TinyUrlClient tinyUrlClient;

    @Autowired
    private DealerProgramService dealerProgramService;

    @Autowired
    private SecurityHelperService securityHelperService;

    @Autowired
    private CampaignModelSuppressionClient campaignModelSuppressionClient;

    @Autowired
    private CampaignClient campaignClient;

    @Autowired
    private HttpSession session;

    @PostMapping(value = "/search")
    public SearchResults<VehicleDoc> find( @RequestBody DealerVehicleSearchCriteria searchForm
            , @SortDefault(value = "year") Pageable pageable) {
        mergeFields(searchForm);
        searchForm.setExcludes(EXCLUDES);
        pageable = modifyStockTypeSort(pageable);

        return docService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));
    }

    /**
     * Certified (CPO) is not a valid value for stockType, so we need to modify the sort order to sort by stockType using the certified value
     * @param pageable
     * @return
     */
    @NotNull
    private static Pageable modifyStockTypeSort(Pageable pageable) {
        Sort.Order stockTypeOrder = pageable.getSort().getOrderFor("stockType");
        if(stockTypeOrder != null) {
            final Sort.Direction direction = stockTypeOrder.getDirection() == Sort.Direction.ASC ? Sort.Direction.DESC : Sort.Direction.ASC;
            final Sort sort = Sort.by(direction, "certified");
            final Sort newSort = sort.and(pageable.getSort());

            return PageRequest.of(
                pageable.getPageNumber(),
                pageable.getPageSize(),
                newSort
            );
        }
        return pageable;
    }

    @PostMapping(value = "/facet_info", params = "id")
    public FacetInfoResult findFacet(@RequestBody DealerVehicleSearchCriteria searchForm, @RequestParam("id") String facet) {
        mergeFields(searchForm);
        return docService.facets(searchForm, VehicleDocFacets.class, facet);
    }

    private void mergeFields(DealerVehicleSearchCriteria searchCriteria) {
        final List<String> dealerIds = getDealerIdsAndCheckPermissions(searchCriteria, session);
        final List<VehicleDealerProgramPair> dealerProgramsPair = CollectionUtils.isEmpty(searchCriteria.programIds)
            ? getAllProgramsFromDealers(dealerProgramService, dealerIds, getProgramIds(session))
            : getAllProgramsFromCriteria(searchCriteria.programIds, dealerIds);

        handleModelSuppression(searchCriteria, dealerProgramsPair);

        if (!SecurityUtils.isProgramUser()) {
            return;
        }

        if (!isNonLTWVehicleEnabled(searchCriteria)) {
            searchCriteria.setVehicleLifetimeWarrantyEligible(true);
        }

        searchCriteria.setVehicleDealerProgramPairs(dealerProgramsPair);
        searchCriteria.setDealerIds(null);
        searchCriteria.setProgramIds(null);
    }

    private List<String> getDealerIdsAndCheckPermissions(DealerVehicleSearchCriteria searchCriteria, HttpSession session) {
        final List<String> dealerIds = searchCriteria.getDealerIds() == null || searchCriteria.getDealerIds().isEmpty()
                ? getDealerIds(session)
                : searchCriteria.getDealerIds();
        securityHelperService.checkPermission(SecurityContextHolder.getContext().getAuthentication(), dealerIds,"");
        return dealerIds;
    }

    void handleModelSuppression(DealerVehicleSearchCriteria searchCriteria, List<VehicleDealerProgramPair> dealerPrograms) {
        try {
            List<Integer> suppressionModelIds = dealerPrograms
                .stream()
                .flatMap(vehicleDealer -> vehicleDealer.getOnlyLivePrograms().stream())
                .distinct()
                .flatMap(programId -> campaignClient.findByProgramId(programId).getContent().stream().map(BaseView::getId))
                .distinct()
                .flatMap(campaignId -> campaignModelSuppressionClient.findByCampaignId(campaignId).getContent().stream().map(CampaignModelSuppression::getModelId))
                .distinct()
                .collect(Collectors.toList());

            if (!suppressionModelIds.isEmpty()) {
                searchCriteria.setSuppressionModelIds(suppressionModelIds);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    boolean isNonLTWVehicleEnabled(DealerVehicleSearchCriteria searchCriteria) {
        return ListUtils.emptyIfNull(searchCriteria.getProgramIds())
            .stream()
            .findFirst()
            .map(campaignClient::findByProgramId)
            .orElse(CollectionModel.empty())
            .getContent()
            .stream()
            .findFirst()
            .map(CampaignView::getInventoryConfig)
            .map(InventoryConfig::getNonLTWVehicleEnabled)
            .orElse(false);
    }

    @PostMapping(value = "/shortVehicleURL")
    public ResponseEntity<?> shortVehicleUrl(@RequestBody TinyUrlRequest tinyUrlRequest) {
        TinyUrlView tinyUrlView = tinyUrlClient.create(tinyUrlRequest);

        return ResponseEntity.ok(tinyUrlView.getRequestUrl());
    }

    @Override
    public List<VehicleDealerProgramPair> getAllProgramsFromDealers(DealerProgramService dealerProgramService, List<String> dealerIds, List<String> programsAccessList) {
        Objects.requireNonNull(dealerIds);

        final List<VehicleDealerProgramPair> dealerPrograms = new LinkedList<>();
        dealerIds.forEach(dealerId -> {
            final List<String> dealerProgramsLive = dealerProgramService.findDealerProgramsLive(dealerId);
            final Set<String> dealerProgramAccessList = dealerProgramsLive.stream().filter(programsAccessList::contains).collect(Collectors.toSet());

            VehicleDealerProgramPair vehicleDealerProgramPair = VehicleDealerProgramPair
                    .builder()
                    .dealerId(dealerId)
                    .onlyLivePrograms(dealerProgramAccessList)
                    .build();
            dealerPrograms.add(vehicleDealerProgramPair);
        });
        return dealerPrograms;
    }

    public List<VehicleDealerProgramPair> getAllProgramsFromCriteria(List<String> programIds, List<String> dealerIds){
        Objects.requireNonNull(programIds);

        final List<VehicleDealerProgramPair> dealerPrograms = new LinkedList<>();
        dealerIds.forEach(dealerId -> {
            VehicleDealerProgramPair dealerProgramPair = new VehicleDealerProgramPair();
            dealerProgramPair.setDealerId(dealerId);
            dealerProgramPair.setOnlyLivePrograms(new HashSet<>(programIds));
            dealerPrograms.add(dealerProgramPair);
        });
        return dealerPrograms;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DealerVehicleSearchCriteria extends AbstractSearchCriteria {

        @TermQuery(field = "vin")
        private String vin;

        @TermQuery(field = "stockNumber")
        private String stockNumber;

        @TermQuery(field = "active")
        private Boolean active;

        @TermQuery(field = "certified")
        private Boolean certified;

        @TermQuery(field = "vehicleLifetimeWarrantyEligible")
        private Boolean vehicleLifetimeWarrantyEligible;

        /**
         * We override the dealerIds field to ensure it is in the query instead of post filter
         * to properly filter on the associated dealer only.
         */
        @TermQuery(field = "dealer.id")
        private List<String> dealerIds;

        @TermQuery(field = "stockType", scope = SearchScope.POST_FILTER)
        private List<String> stockTypes;

        @TermQuery(field = "year", scope = SearchScope.POST_FILTER)
        private List<Integer> years;

        @TermQuery(field = "make", scope = SearchScope.POST_FILTER)
        private List<String> makes;

        @TermQuery(field = "makeId", scope = SearchScope.POST_FILTER)
        private List<Integer> makeIds;

        @TermQuery(field = "bodyStyle", scope = SearchScope.POST_FILTER)
        private List<String> bodyStyles;

        @TermQuery(field = "model", scope = SearchScope.POST_FILTER)
        private List<String> models;

        @TermQuery(field = "modelId", scope = SearchScope.POST_FILTER, boolQuery = BoolQueryOccurrence.MUST_NOT)
        private List<Integer> suppressionModelIds;

        @RangeQuery(field = "miles", scope = SearchScope.POST_FILTER)
        private IntegerRange miles;

        @TermQuery(field = "exteriorColorGeneric", scope = SearchScope.POST_FILTER)
        private List<String> exteriorColorGeneric;

        @TermQuery(field = "dealer.stateCode", scope = SearchScope.POST_FILTER)
        private List<String> states;

        @TermQuery(field = "address.dma.code", scope = SearchScope.POST_FILTER)
        private List<Integer> dmaCodes;

        @RangeQuery(field = "address.dma.rank")
        private IntegerRange topDmas;

        @TermQuery(field = "programIds", scope = SearchScope.POST_FILTER)
        private List<String> programIds;

        @CompoundQuery
        private List<VehicleDealerProgramPair> vehicleDealerProgramPairs;
    }

}
