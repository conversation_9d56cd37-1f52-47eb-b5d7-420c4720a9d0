package com.carsaver.partner.web.api;

import com.carsaver.elasticsearch.model.vehicle.VehicleDoc;
import com.carsaver.elasticsearch.service.VehicleDocService;
import com.carsaver.magellan.export.CsvWriter;
import com.carsaver.magellan.export.DataStreamProvider;
import com.carsaver.magellan.export.service.CsvExportService;
import com.carsaver.magellan.export.service.ExportService;
import com.carsaver.partner.util.ExportHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.List;

import static org.springframework.util.StringUtils.isEmpty;


@Slf4j
@RestController
public class VehicleExportApiController implements BaseListController {

    private final ExportHelper exportHelper;

    private final VehicleDocService docService;

    public VehicleExportApiController(ExportHelper exportHelper, VehicleDocService docService) {
        this.exportHelper = exportHelper;
        this.docService = docService;
    }

    @GetMapping(value="/vehicles/report.csv", produces = "text/csv")
    public void exportVehicleToCSV(@RequestParam List<String> columns, @RequestParam List<String> labels,
                                   @ModelAttribute("searchForm") VehicleApiController.DealerVehicleSearchCriteria searchForm, @RequestParam String negativeSearchColumns, HttpServletResponse response, HttpSession session) throws Exception {
        exportHelper.validateLabels(columns, labels);

        if (!isEmpty(negativeSearchColumns)) {
            exportHelper.applyNegativeSearchColumns(searchForm, Arrays.asList(negativeSearchColumns.split(",")));
        }
        final var finalListOfDealerIds = CollectionUtils.isEmpty(searchForm.getDealerIds()) ? getDealerIds(session) : searchForm.getDealerIds();
        searchForm.setDealerIds(finalListOfDealerIds);
        CsvWriter csvWriter = exportHelper.prepareCsvWriter(response, columns, labels).build();
        try(ExportService<VehicleDoc> exportService = new CsvExportService<>(csvWriter)) {
            DataStreamProvider<VehicleDoc> dataStreamProvider = () -> docService.scrollStream(searchForm);
            exportService.export(dataStreamProvider);
        }
    }

}
