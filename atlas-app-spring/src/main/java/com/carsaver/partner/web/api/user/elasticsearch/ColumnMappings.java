package com.carsaver.partner.web.api.user.elasticsearch;

import java.util.HashMap;
import java.util.Map;

public class ColumnMappings {
    private static final Map<String, String> columnMap = new HashMap<>();
    private static final Map<String, String> labelMap = new HashMap<>();

    static {
        columnMap.put("program.name", "pname");
        columnMap.put("firstName", "fn");
        columnMap.put("lastName", "ln");
        columnMap.put("stage", "st");
        columnMap.put("email", "em");
        columnMap.put("clientAdvisor", "ca");
        columnMap.put("lastLoginAt", "lla");
        columnMap.put("websiteVisits", "wvs");
        columnMap.put("logins", "lg");
        columnMap.put("websiteVisitsBeforeSignup", "wvbs");
        columnMap.put("traits.tradeInsCount", "tic");
        columnMap.put("tradeYear", "ty");
        columnMap.put("tradeMake", "tm");
        columnMap.put("tradeModel", "tmo");
        columnMap.put("tradeValue", "tv");
        columnMap.put("tradeMileage", "tmil");
        columnMap.put("tradeEquity", "te");
        columnMap.put("tradePurchaseType", "tpt");
        columnMap.put("tradePurchaseDate", "tpd");
        columnMap.put("tradePayment", "tp");
        columnMap.put("lenderName", "lnm");
        columnMap.put("tradePaymentType", "tptp");
        columnMap.put("remainingPayments", "rp");
        columnMap.put("tradeTerm", "tt");
        columnMap.put("maturityDate", "md");
        columnMap.put("tier", "t");
        columnMap.put("preApprovalCount", "pac");
        columnMap.put("traits.preQualificationsCount", "pq");
        columnMap.put("traits.leadsCount", "lc");
        columnMap.put("traits.financeAppsSubmittedCount", "fasc");
        columnMap.put("traits.salesCount", "sc");
        columnMap.put("phoneNumber", "pn");
        columnMap.put("smsEnabled", "sms");
        columnMap.put("vehicleOfInterest.stockType", "voist");
        columnMap.put("vehicleOfInterest.inTransit", "voiit");
        columnMap.put("vehicleOfInterest.certified", "voic");
        columnMap.put("vehicleOfInterest.stockNumber", "voisn");
        columnMap.put("vehicleOfInterest.year", "voiy");
        columnMap.put("vehicleOfInterest.make", "voim");
        columnMap.put("vehicleOfInterest.model", "voimo");
        columnMap.put("timeZone", "tz");
        columnMap.put("address", "addr");
        columnMap.put("address.street", "addrst");
        columnMap.put("address.city", "addrc");
        columnMap.put("address.stateCode", "addrs");
        columnMap.put("address.zipCode", "addrz");
        columnMap.put("address.dma.name", "dmn");
        columnMap.put("address.dma.rank", "dmr");
        columnMap.put("address.dma.code", "dmc");
        columnMap.put("traits.activeVehiclesInGarageCount", "avigc");
        columnMap.put("traits.garageSavedVehicleCount", "gsvc");
        columnMap.put("traits.garageViewedVehicleCount", "gvc");
        columnMap.put("traits.dealerUserTraitsList.vehiclesInGarageCount", "vigc");
        columnMap.put("traits.new", "dun");
        columnMap.put("traits.used", "duu");
        columnMap.put("signUpDate", "cd");
        columnMap.put("status", "cs");
        columnMap.put("stageTransition", "stdt");
        columnMap.put("assignedDealer", "ad");
        columnMap.put("locale", "lcl");
        columnMap.put("leadType", "lt");
    }

    static {
        labelMap.put("Program", "pname");
        labelMap.put("First Name", "fn");
        labelMap.put("Last Name", "ln");
        labelMap.put("Stage", "st");
        labelMap.put("Email", "em");
        labelMap.put("Originating Salesperson", "ca");
        labelMap.put("Last Login", "lla");
        labelMap.put("Website Visits", "wvs");
        labelMap.put("Logins", "lg");
        labelMap.put("PIN Visits", "wvbs");
        labelMap.put("Trade-ins", "tic");
        labelMap.put("Trade Year", "ty");
        labelMap.put("Trade Make", "tm");
        labelMap.put("Trade Model", "tmo");
        labelMap.put("Trade Offer", "tv");
        labelMap.put("Trade Mileage", "tmil");
        labelMap.put("Est. Trade Equity", "te");
        labelMap.put("Trade Purchase Type", "tpt");
        labelMap.put("Trade Purchase Date", "tpd");
        labelMap.put("Current Payment", "tp");
        labelMap.put("Lender", "lnm");
        labelMap.put("Trade Type", "tptp");
        labelMap.put("Remaining Payments", "rp");
        labelMap.put("Financing Term", "tt");
        labelMap.put("Financing Maturity Date", "md");
        labelMap.put("Credit Tier", "t");
        labelMap.put("Pre-approvals", "pac");
        labelMap.put("Pre-quals", "pq");
        labelMap.put("Leads", "lc");
        labelMap.put("Applications", "fasc");
        labelMap.put("Sales", "sc");
        labelMap.put("Phone Number", "pn");
        labelMap.put("Sms", "sms");
        labelMap.put("VOI StockType", "voist");
        labelMap.put("VOI In Transit", "voiit");
        labelMap.put("VOI Certified", "voic");
        labelMap.put("VOI Year", "voiy");
        labelMap.put("VOI Make", "voim");
        labelMap.put("VOI Model", "voimo");
        labelMap.put("Time Zone", "tz");
        labelMap.put("Address", "addr");
        labelMap.put("Street", "addrst");
        labelMap.put("City", "addrc");
        labelMap.put("State", "addrs");
        labelMap.put("Zip", "addrz");
        labelMap.put("DMA Name", "dmn");
        labelMap.put("DMA Rank", "dmr");
        labelMap.put("DMA Code", "dmc");
        labelMap.put("Active Vehicles in Garage", "avigc");
        labelMap.put("Saved Vehicles", "gsvc");
        labelMap.put("Recently Viewed Vehicles", "gvc");
        labelMap.put("Garage Vehicles", "vigc");
        labelMap.put("Garage: New", "dun");
        labelMap.put("Garage: Used", "duu");
        labelMap.put("Sign Up Date", "cd");
        labelMap.put("Customer Status", "cs");
        labelMap.put("Stage Transition", "stdt");
        labelMap.put("Stock Number", "voisn");
        labelMap.put("Assigned Dealers", "ad");
        labelMap.put("Language", "lcl");
        labelMap.put("Lead Type", "lt");
    }


    public static String getFullColumnName(String shortName) {
        return columnMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue().equals(shortName))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(shortName);
    }

    public static String getFullLabelName(String shortName) {
        return labelMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue().equals(shortName))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(shortName);
    }

    public static String getShortColumnName(String fullName) {
        return columnMap.getOrDefault(fullName, fullName);
    }

    public static String getShortLabelName(String fullName) {
        return labelMap.getOrDefault(fullName, fullName);
    }
}
