package com.carsaver.partner.web.api.user;

import com.carsaver.magellan.api.vehicle.VehicleInfoService;
import com.carsaver.magellan.client.UserVehicleClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.user.UserVehicleDiscrepancy;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.magellan.model.vehicle.UserVehicleQuoteView;
import com.carsaver.magellan.model.vehicle.VehicleInfo;
import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.model.UserVehicleSellInfoResponse;
import com.carsaver.partner.model.VehicleCountDTO;
import com.carsaver.partner.model.trade.TradeType;
import com.carsaver.partner.model.user.UserTrade;
import com.carsaver.partner.service.UserVehicleService;
import com.carsaver.partner.service.sellathome.UserVehicleSellInfoService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.util.CampaignConfigUtility;
import com.carsaver.partner.web.api.CustomerBaseController;
import com.carsaver.partner.web.api.SecurityHelperService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController("userTradesApiController")

public class UserTradesApiController implements CustomerBaseController {

    public static final String SELL_AT_HOME = "SELL_AT_HOME";
    public static final String SELL = "SELL";
    public static final String TRADE = "TRADE";
    @Autowired
    private UserVehicleClient userVehicleClient;

    @Autowired
    private VehicleInfoService vehicleInfoService;

    @Autowired
    private UserVehicleService userVehicleService;

    @Autowired
    private SecurityHelperService securityHelperService;

    @Autowired
    private SplitFeatureFlags splitFeatureFlags;

    @Autowired
    NissanWebClient nissanWebClient;

    @Autowired
    UserVehicleSellInfoService userVehicleSellInfoService;

    @Value("${dealer-service.api-uri-local:${dealer-service.api-uri}}")
    private String dealerServiceUrl;

    @Value("${program.nissan-buy-at-home-id}")
    private String nissanProgramId;

    @Autowired
    private CampaignConfigUtility campaignConfigUtility;

    @GetMapping("/api/users/{userId}/trades")
    public UserTrade getUserTrades(@RequestParam(value = "dealerIds", required = false) List<String> dealerIds,
                                   @PathVariable String userId,
                                   HttpSession session,
                                   @SortDefault(value = "createdDate", direction = Sort.Direction.DESC) Pageable pageable
    ) {
        // This appears to check existence of multiple dealerIds, permissions, and throws exception if they fail.
        List<String> safeDealerIds = getFinalListOfDealerAndValidateCustomerPermissions(dealerIds, session, securityHelperService);

        // CS-6199 multiple dealer trades. Trade now retrieved by provider "dealer" + dealerId.
        // If list of dealerIds has one item, then retrieve the trade by provider "dealer" + dealerId.
        // Else, use the Guaranteed trade which is Blackbook or JD Power, at the time of this writing.
        boolean isUseDealerId = safeDealerIds.size() == 1;
        String dealerId = resolveDealerIdFromList(safeDealerIds);
        //Trade Vehicle should not show Sell@Home vehicles so filtering with recordType default is TRADE
        List<UserTrade.Trade> userTrades = userVehicleClient.findAllByUserAndRecordTypeAndDeletedFalse(userId, TRADE, pageable)
            .getContent()
            .stream()
            .map(x -> convertToTrade(x, dealerId, isUseDealerId))
            .collect(Collectors.toList());

        UserTrade result = UserTrade.builder().trades(userTrades).build();


        if (splitFeatureFlags.isLeasePayoffEnabled()) {
            boolean isLeasePayoffEnabled = campaignConfigUtility.isLeasePayoffEnabled(userId);
            if (result != null && result.getTrades() != null) {
                result.getTrades().forEach(trade -> trade.setIsLeasePayoffEnabled(isLeasePayoffEnabled));
            }
        }

        return result;
    }

    @GetMapping("/api/users/{userId}/sell-at-home")
    public UserTrade getUserSellAtHome(@RequestParam(value = "dealerIds", required = false) List<String> dealerIds,
                                       @PathVariable String userId,
                                       HttpSession session,
                                       @SortDefault(value = "createdDate", direction = Sort.Direction.DESC) Pageable pageable
    ) {
        // This appears to check existence of multiple dealerIds, permissions, and throws exception if they fail.
        List<String> safeDealerIds = getFinalListOfDealerAndValidateCustomerPermissions(dealerIds, session, securityHelperService);

        // CS-6199 multiple dealer trades. Trade now retrieved by provider "dealer" + dealerId.
        // If list of dealerIds has one item, then retrieve the trade by provider "dealer" + dealerId.
        // Else, use the Guaranteed trade which is Blackbook or JD Power, at the time of this writing.
        boolean isUseDealerId = safeDealerIds.size() == 1;
        String dealerId = resolveDealerIdFromList(safeDealerIds);

        Boolean isSellAtHomeFeatureEnabled = userVehicleSellInfoService.isSellAtHomeFeatureEnabledForUserId(dealerId, userId);
        UserTrade result = null;
        if(Boolean.TRUE.equals(isSellAtHomeFeatureEnabled)) {
            //Trade Vehicle should not show Sell@Home vehicles so filtering with recordType default is TRADE
            List<UserTrade.Trade> userTrades = userVehicleClient.findAllByUserAndRecordTypeAndDeletedFalse(userId, SELL, pageable)
                    .getContent()
                    .stream()
                    .map(x -> convertToTrade(x, dealerId, isUseDealerId))
                    .collect(Collectors.toList());

            result = UserTrade.builder().trades(userTrades).build();
        }
        return result;
    }

    @GetMapping("/api/users/trades/{tradeId}")
    public ResponseEntity<UserTrade.Trade> getUserTrade(@RequestParam(value = "dealerIds", required = false) List<String> dealerIds,
                                                        @PathVariable String tradeId,
                                                        HttpSession session) {
        // This appears to check existence of multiple dealerIds, permissions, and throws exception if they fail.
        List<String> safeDealerIds = getFinalListOfDealerAndValidateCustomerPermissions(dealerIds, session, securityHelperService);

        // CS-6199 multiple dealer trades. Trade now retrieved by provider "dealer" + dealerId.
        boolean isUseDealerId = true;
        String dealerId = resolveDealerIdFromList(safeDealerIds);

        Optional<UserVehicleView> userVehicleView = userVehicleClient.findById(tradeId);
        ResponseEntity<UserTrade.Trade> result = userVehicleView
            .map(x -> convertToTrade(x, dealerId, isUseDealerId))
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());

        if (splitFeatureFlags.isLeasePayoffEnabled() && userVehicleView.isPresent()) {
            boolean isLeasePayoffEnabled = campaignConfigUtility.isLeasePayoffEnabled(userVehicleView.get().getUserId());
            if (result.getBody() != null) {
                result.getBody().setIsLeasePayoffEnabled(isLeasePayoffEnabled);
            }
        }

        return result;
    }



    @PreAuthorize("hasPermission(#dealer, 'customer:deal:edit')")
    @PatchMapping("/api/dealer/{dealer}/users/trades/{userVehicleId}/discrepancy")
    public ResponseEntity<UserVehicleDiscrepancy> updateTradeDiscrepancy(@PathVariable DealerView dealer, @PathVariable String userVehicleId, @RequestBody UserVehicleDiscrepancy discrepancy){
        discrepancy.setDealerId(dealer.getId());
        UserVehicleDiscrepancy savedDiscrepancy = userVehicleClient.updateDiscrepancy(userVehicleId, discrepancy);
        return ResponseEntity.ok(savedDiscrepancy);
    }

    @PreAuthorize("hasPermission(#dealer, 'customer:deal:edit')")
    @DeleteMapping("/api/dealer/{dealer}/users/trades/{userVehicleId}/discrepancy")
    public ResponseEntity<UserVehicleDiscrepancy> removeTradeDiscrepancy(@PathVariable DealerView dealer, @PathVariable String userVehicleId){
        userVehicleClient.removeDiscrepancy(userVehicleId);
        return ResponseEntity.ok().build();
    }

    /**
     *
     * @param dealerIds Please pass in single dealerId only, multiple dealerIds are not supported
     * @param session Session is needed for teh permission check same as trades
     * @param pageable Page info pass in page,size,sort as qery param where sort should be comma seperated ex. createdDate,DESC
     * @return Paginated response for the SELL vehicles
     */
    @GetMapping("/api/dealer/user-vehicle-sell-info")
    public UserVehicleSellInfoResponse getSellAtHomeVehicles(@RequestParam(value = "dealerIds", required = false) String dealerIds,
                                                             HttpSession session,
                                                             @SortDefault(value = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) {
        getFinalListOfDealerAndValidateCustomerPermissions(List.of(dealerIds), session, securityHelperService);

        String sortParams = pageable.getSort().stream()
            .map(order -> order.getProperty() + "," + order.getDirection())
            .collect(Collectors.joining("&sort="));

        // Endpoint to dealer-service
        String url = UriComponentsBuilder.fromUriString(dealerServiceUrl)
            .path("/user-vehicle-sell-info")
            .queryParam("dealerIds", String.join(",", dealerIds))
            .queryParam("page", pageable.getPageNumber())
            .queryParam("size", pageable.getPageSize())
            .build()
            .toUriString();
        //if in case FE needs to sort pass in
        url += (sortParams.isEmpty() ? "" : "&sort=" + sortParams);
        log.debug("{}: url:{}", SELL_AT_HOME, url);

        UserVehicleSellInfoResponse response = nissanWebClient.get(url, UserVehicleSellInfoResponse.class, SELL_AT_HOME);
        if (response != null) {
            response.setProgramId(nissanProgramId);
        }

        return response;
    }

    /**
     *
     * @param dealerIds Please pass in single dealerId only, multiple dealerIds are not supported
     * @return totalCount, nearingExpirationCount and dealerLink(domain is being configured in yaml for each env)
     */
    @GetMapping("/api/dealer/user-vehicle-sell-info/count")
    public ResponseEntity<VehicleCountDTO> getCounts(@RequestParam(value = "dealerIds", required = false) String dealerIds) {

        String url = UriComponentsBuilder.fromUriString(dealerServiceUrl)
            .path("/user-vehicle-sell-info")
            .path("/count")
            .queryParam("dealerIds", String.join(",", dealerIds))
            .build()
            .toUriString();


        VehicleCountDTO response = nissanWebClient.get(url, VehicleCountDTO.class, SELL_AT_HOME);

        return ResponseEntity.ok(response);
    }



    // TODO: JUnit tests!
    String resolveDealerIdFromList(List<String> dealerIds) {
        // Previous validation call confirms at least one Dealer Id in List.
        // Still, use safe approach here. Check for NULL list and NULL list item.
        // findFirst will throw NPE if the list item is NULL, hence limit(1).
        String result = Optional.ofNullable(dealerIds).stream()
            .flatMap(List::stream)
            .limit(1)
            .reduce("No Dealer Id Sent", (first, second) -> second);

        return result;
    }

    UserTrade.Trade convertToTrade(UserVehicleView userVehicle, String dealerId, boolean isUseDealerId) {
        UserVehicleQuoteView guaranteedUserVehicleQuote = userVehicleService.getGuaranteedUserVehicleQuote(userVehicle);

        UserVehicleQuoteView dealerUserVehicleQuote = userVehicleService.getDealerUserVehicleQuote(userVehicle, dealerId);

        VehicleInfo vehicleInfo = vehicleInfoService.lookUp(userVehicle).orElse(null);
        UserTrade.Trade result = UserTrade.Trade.from(userVehicle, dealerUserVehicleQuote, guaranteedUserVehicleQuote, vehicleInfo);

        //Set Payoff for lease payoff
        if (splitFeatureFlags.isLeasePayoffEnabled()) {
            if (TradeType.LEASE_PAYOFF.name().equals(userVehicle.getTradeType()) && guaranteedUserVehicleQuote != null) {
                Double payoffAmount = userVehicleService.getUserVehiclePayoff(userVehicle, guaranteedUserVehicleQuote);
                result.setPayoff(Math.abs(payoffAmount));
                result.setOffer(guaranteedUserVehicleQuote.getAdjustedTradeValue());
                Optional.ofNullable(userVehicle.getBalance())
                    .map(Double::intValue)
                    .map(Math::abs)
                    .map(value -> value * -1)
                    .ifPresent(result::setEquity);
            }
        }

        return result;
    }


}
