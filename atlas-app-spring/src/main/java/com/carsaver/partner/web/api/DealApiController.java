package com.carsaver.partner.web.api;

import com.carsaver.magellan.api.deal.CertificateService;
import com.carsaver.magellan.api.deal.DealJacketService;
import com.carsaver.magellan.api.deal.DealService;
import com.carsaver.magellan.api.deal.DealUpdateRequest;
import com.carsaver.magellan.client.AppointmentClient;
import com.carsaver.magellan.client.ConnectionClient;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.ConnectionView;
import com.carsaver.magellan.model.DealerLinkView;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.connection.DealerConnectionRequest;
import com.carsaver.magellan.model.deal.DealExpiration;
import com.carsaver.magellan.model.lead.AppointmentRequest;
import com.carsaver.partner.model.ScheduleAppointmentStore;
import com.carsaver.partner.model.SendLeadStore;
import com.carsaver.partner.model.deal.CustomerDealModel;
import com.carsaver.partner.service.DealModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@Slf4j
@RestController
public class DealApiController {

    @Autowired
    private DealModelService dealModelService;

    @Autowired
    private DealService dealService;

    @Autowired
    private DealJacketService dealJacketService;

    @Autowired
    private CertificateService certificateService;

    @Autowired
    private DealerLinkClient dealerLinkClient;

    @Autowired
    private ConnectionClient connectionClient;

    @Autowired
    private AppointmentClient appointmentClient;

    @PreAuthorize("hasPermission(#deal, 'customer:read') or hasAuthority('read:user')")
    @GetMapping("/api/deal/{deal}/expiration")
    public DealExpiration getDealExpirationData(@PathVariable CertificateView deal) {
        return dealService.getDealExpiration(deal);
    }

    @PreAuthorize("hasPermission(#certificate, 'customer:read') or hasAuthority('read:user')")
    @GetMapping("/api/deal/{certificate}")
    public ResponseEntity<CustomerDealModel> baseLeadDetails(@PathVariable CertificateView certificate) {

        return ResponseEntity.of(dealModelService.createDealModel(certificate));
    }

    // TODO ATS-274 edit deal permissions to be defined
    @PatchMapping("/api/deal/{certificate}")
    public ResponseEntity<CustomerDealModel> updateSalesPriceOnDeal(@PathVariable CertificateView certificate, @RequestBody DealUpdateRequest dealUpdateRequest){
        CertificateView updatedCertificate = certificateService.updateDealAndQuote(certificate, dealUpdateRequest);
        return ResponseEntity.of(dealModelService.createDealModel(updatedCertificate));
    }

    @GetMapping("/api/deal/{certificate}/sendLead")
    public SendLeadStore getSendLeadData(@PathVariable CertificateView certificate) {
        return SendLeadStore.from(certificate);
    }

    @PostMapping("/api/deal/{certificate}/sendLead")
    public ResponseEntity sendLead(
        @PathVariable CertificateView certificate,
        @RequestBody ConnectionView connectionBody
    ) {
        DealerLinkView dealerLink = certificate.getDealerLink();
        if (dealerLink == null) {
            dealerLink = new DealerLinkView();
            dealerLink.setDealerId(connectionBody.getDealerId());
            dealerLink.setUserId(connectionBody.getUserId());
            dealerLink.setSource(Optional.ofNullable(certificate.getSource()).orElse(new Source()));
            dealerLinkClient.create(dealerLink);
        }

        DealerConnectionRequest connectionRequest = DealerConnectionRequest.from(certificate);
        connectionRequest.setSource(Optional.ofNullable(certificate.getSource()).orElse(new Source()));
        connectionRequest.setMessage(connectionBody.getMessage());
        connectionRequest.setType("atlas-manual");

        connectionClient.create(connectionRequest);

        return ResponseEntity.ok().build();
    }

    @GetMapping("/api/deal/{certificate}/appointment")
    public ScheduleAppointmentStore getScheduleAppointmentData(@PathVariable CertificateView certificate) {
        return ScheduleAppointmentStore.from(certificate);
    }

    @PostMapping("/api/deal/{certificate}/appointment")
    public ResponseEntity scheduleAppointment(
        @PathVariable CertificateView certificate,
        @RequestBody AppointmentRequest request
    ) {
        request.setSource(Optional.ofNullable(certificate.getSource()).orElse(new Source()));
        request.setCertificateId(certificate.getId());
        appointmentClient.schedule(request);
        return ResponseEntity.ok().build();
    }

}
