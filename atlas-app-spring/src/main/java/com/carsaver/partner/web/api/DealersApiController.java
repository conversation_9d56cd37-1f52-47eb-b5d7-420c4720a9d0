package com.carsaver.partner.web.api;

import com.carsaver.core.DealerStatus;
import com.carsaver.elasticsearch.criteria.DealerSearchCriteria;
import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.magellan.export.CsvWriter;
import com.carsaver.magellan.export.DataStreamProvider;
import com.carsaver.magellan.export.service.CsvExportService;
import com.carsaver.magellan.export.service.ExportService;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.util.StringUtils;
import com.carsaver.partner.elasticsearch.DealerDocService;
import com.carsaver.partner.reporting.service.ProgramService;
import com.carsaver.partner.search.facets.DealerDocFacets;
import com.carsaver.partner.service.DealerUserService;
import com.carsaver.partner.util.ExportHelper;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.SearchScope;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
public class DealersApiController implements BaseListController {

    @Autowired
    private ExportHelper exportHelper;

    @Autowired
    private DealerDocService docService;

    @PostMapping("/api/dealers/search")
    public SearchResults<DealerDoc> find(@RequestBody DealerSearchCriteriaForm searchForm, @SortDefault(value = "name") Pageable pageable, HttpSession session) {
        final var finalListOfDealerIds = CollectionUtils.isEmpty(searchForm.getDealerIds()) ? getDealerIds(session) : searchForm.getDealerIds();
        searchForm.setDealerIds(finalListOfDealerIds);
        searchForm.setSubscriptionProgramIds(searchForm.getProgramIds());
        searchForm.setProgramStatuses(ProgramService.IS_ACTIVE_DEALER_STATUSES);
        return docService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));
    }

    @PostMapping(value = "/api/dealers/facet_info", params = "id")
    public FacetInfoResult findFacet(@RequestBody DealerSearchCriteriaForm searchForm, @RequestParam("id") String facet, HttpSession session) {
        final var finalListOfDealerIds = CollectionUtils.isEmpty(searchForm.getDealerIds()) ? getDealerIds(session) : searchForm.getDealerIds();
        searchForm.setDealerIds(finalListOfDealerIds);
        log.info("Searching Dealers Facet {} by {}", facet, searchForm);
        return docService.facets(searchForm, DealerDocFacets.class, facet);
    }

    @GetMapping(value="dealers/report.csv", produces = "text/csv")
    public void exportDealersToCsv(@RequestParam List<String> columns, @RequestParam List<String> labels,
        @ModelAttribute("searchForm") DealerSearchCriteriaForm searchForm, @RequestParam String negativeSearchColumns, HttpServletResponse response, HttpSession session) throws Exception {
        exportHelper.validateLabels(columns, labels);
        if (!StringUtils.isEmpty(negativeSearchColumns)) {
            exportHelper.applyNegativeSearchColumns(searchForm, Arrays.asList(negativeSearchColumns.split(",")));
        }
        final var finalListOfDealerIds = CollectionUtils.isEmpty(searchForm.getDealerIds()) ? getDealerIds(session) : searchForm.getDealerIds();
        searchForm.setDealerIds(finalListOfDealerIds);
        CsvWriter csvWriter = exportHelper.prepareCsvWriter(response, columns, labels).build();
        try(ExportService<DealerDoc> exportService = new CsvExportService<>(csvWriter)) {
            DataStreamProvider<DealerDoc> dataStreamProvider = () -> docService.scrollStream(searchForm);
            exportService.export(dataStreamProvider);
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DealerSearchCriteriaForm extends DealerSearchCriteria {

        @TermQuery(
                field = "id"
        )
        private List<String> dealerIds;

        @TermQuery(
            field = "inventoryPricingMetrics.percentageNewPriced",
            scope = SearchScope.POST_FILTER
        )
        private List<Integer> percentageNewPriced;

        @TermQuery(
            field = "inventoryPricingMetrics.percentageUsedPriced",
            scope = SearchScope.POST_FILTER
        )
        private List<Integer> percentageUsedPriced;

        @TermQuery(
                field = "programIds",
                scope = SearchScope.POST_FILTER
        )
        private List<String> programId;

        @TermQuery(nested = true, path = "subscriptions", field = "program.id", scope = SearchScope.QUERY)
        private List<String> subscriptionProgramIds;

        @TermQuery(nested = true, path = "subscriptions", field = "status", scope = SearchScope.QUERY)
        private List<DealerStatus> programStatuses;

    }
}
