package com.carsaver.partner.customer;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.partner.exception.BadRequestException;
import com.carsaver.partner.exception.ForbiddenException;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.LoggedInUserProgramService;
import com.carsaver.partner.web.api.CustomerBaseController;
import com.carsaver.partner.web.api.SecurityHelperService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@AllArgsConstructor
public class CustomerDetailsController implements CustomerBaseController {
    private final CustomerDetailsLinkService customerDetailsLinkService;
    private final SecurityHelperService securityHelperService;
    private final LoggedInUserProgramService loggedInUserProgramService;

    /**
     * Retrieves customer details links based on either dealer ID or program ID.
     *
     * @param dealerId Optional dealer ID to filter results
     * @param programId Optional program ID to filter results
     * @param userId The ID of the user whose details are being retrieved
     * @return List of CustomerDetailsLink objects
     * @throws BadRequestException if neither dealerId nor programId is provided, or if both are provided
     * @throws ForbiddenException if programId is provided but user is not a program user
     */
    @GetMapping("/api/users/{userId}/details")
    public List<CustomerDetailsLink> getCustomerDetailsLinks(
        @RequestParam(name = "dealerId", required = false) String dealerId,
        @RequestParam(name = "programId", required = false) String programId,
        @PathVariable String userId) {
        validateRequestParameters(dealerId, programId);

        // get a list of dealer ids out of the dealerId passed in, or from the programId
        List<String> dealerIds = StringUtils.isNotBlank(dealerId) ? List.of(dealerId) : getDealerIdsFromProgram(programId, userId);

        // return the detail links
        return customerDetailsLinkService.getCustomerDetailsLinks(dealerIds, userId);
    }

    /**
     * Validates the request parameters to ensure either dealerId or programId is provided
     * and the user has appropriate permissions.
     *
     * @param dealerId dealer ID from the request
     * @param programId program ID from the request
     * @throws BadRequestException if neither parameter is provided
     * @throws ForbiddenException if programId is provided but user lacks program user permissions
     */
    private void validateRequestParameters(String dealerId, String programId) {
        if (StringUtils.isBlank(dealerId) && StringUtils.isBlank(programId)) {
            throw new BadRequestException("A dealerId or a programId is required");
        }

        if (StringUtils.isNotBlank(programId)  && !SecurityUtils.isProgramUser()) {
            throw new ForbiddenException("User supplied a programId but is not a program user");
        }
    }

    /**
     * Retrieves all dealer IDs associated with a specific program.
     *
     * @param programId The ID of the program to retrieve dealer IDs for
     * @param userId The ID of the user whose details are being retrieved
     * @return List of dealer IDs associated with the specified program
     */
    private List<String> getDealerIdsFromProgram(String programId, String userId) {
        List<DealerView> dealerViews = loggedInUserProgramService.getSelectedProgram(userId, List.of(programId));

        return dealerViews.stream()
            .map(DealerView::getId)
            .collect(Collectors.toList());
    }

}
