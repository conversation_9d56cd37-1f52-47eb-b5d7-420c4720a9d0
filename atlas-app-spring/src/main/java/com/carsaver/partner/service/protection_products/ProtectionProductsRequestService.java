package com.carsaver.partner.service.protection_products;

import com.carsaver.magellan.api.deal.DealJacketRequest;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.protectioproducts.ProtectionProducts;
import com.carsaver.partner.model.DealProtectionProduct;
import com.carsaver.partner.model.protection_products.deal_jacket_response.DealJacketResponse;
import com.carsaver.partner.model.protection_products.deal_jacket_response.InsuranceWarrantiesItem;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ProtectionProductsRequestService {

    private final ProtectionProductsService protectionProductsService;
    private final ProtectionPlansRouteOneService protectionPlansRouteOneService;
    private final ProtectionProductsServiceSafety safety;

    public ProtectionProductsRequestService(ProtectionProductsService protectionProductsService, ProtectionPlansRouteOneService protectionPlansRouteOneService,
                                            ProtectionProductsServiceSafety safety) {
        this.protectionProductsService = protectionProductsService;
        this.protectionPlansRouteOneService = protectionPlansRouteOneService;
        this.safety = safety;
    }

    public void addProtectionProductsToRequest(DealJacketRequest request, CertificateView certificate) {
        // does all the checks need to allow it to be used
        ProtectionProducts protectionProducts = getProtectionProducts(certificate);
        request.setProtectionProducts(protectionProducts);
    }

    /**
     * When recalculating the quotes the front-end will pass the current selected list.
     * Method check to see if there are dealJacketResponse from front-end,
     * if not then go to API and fetch the production products
     * @param request
     * @param certificate
     * @param dealJacketResponse
     */
    public void addProtectionProductsToRequest(DealJacketRequest request, CertificateView certificate, DealJacketResponse dealJacketResponse) {
        ProtectionProducts protectionProducts;

        if (dealJacketResponse != null) {
            protectionProducts = buildProtectionProducts(certificate, dealJacketResponse);
        } else {
            protectionProducts = this.getProtectionProducts(certificate);
        }

        request.setProtectionProducts(protectionProducts);
    }

    /**
     * Goes to API to fetch the protection products from RouteOne
     * @param certificate
     * @return
     */
    public ProtectionProducts getProtectionProducts(CertificateView certificate) {
        // does all the checks need to allow it to be used
        Boolean isDealerProtectionProductsFeatureEnabled = protectionPlansRouteOneService.isRouteOneProtectionProductsEnabled(certificate);
        ProtectionProducts protectionProducts = null;

        if (isDealerProtectionProductsFeatureEnabled) {
            Optional<DealProtectionProduct> dealProtectionProduct = protectionProductsService.fetchDealProtectionProductByCertificateId(certificate.getId());

            if (dealProtectionProduct.isPresent()) {
                safety.isSafeToProceed(certificate.getId());
                String routeOneDealJacketId = dealProtectionProduct.get().getRouteOneDealJacketId();
                DealJacketResponse dealJacketResponse = protectionProductsService.fetchRouteOneDealJacket(routeOneDealJacketId);
                protectionProducts = buildProtectionProductsObject(dealJacketResponse);
            }
        }
        return protectionProducts;
    }

    public DealJacketResponse getProtectionProducts(Integer certificateId) {
        DealJacketResponse dealJacketResponse = null;
        // does all the checks need to allow it to be used
        Optional<DealProtectionProduct> dealProtectionProduct = protectionProductsService.fetchDealProtectionProductByCertificateId(certificateId);
        if (dealProtectionProduct.isPresent()) {
            safety.isSafeToProceed(certificateId);
            String routeOneDealJacketId = dealProtectionProduct.get().getRouteOneDealJacketId();
            dealJacketResponse = protectionProductsService.fetchRouteOneDealJacket(routeOneDealJacketId);
        }

        return dealJacketResponse;
    }

    /**
     * Uses front-end data as the current protection products values.
     * Used when reCalculating quotes
     * @param certificate
     * @param dealJacketResponse
     * @return
     */
    public ProtectionProducts buildProtectionProducts(CertificateView certificate, DealJacketResponse dealJacketResponse) {

        // does all the checks need to allow it to be used
        Boolean isDealerProtectionProductsFeatureEnabled = protectionPlansRouteOneService.isRouteOneProtectionProductsEnabled(certificate);
        ProtectionProducts protectionProducts = null;

        if (isDealerProtectionProductsFeatureEnabled) {
            protectionProducts = buildProtectionProductsObject(dealJacketResponse);
        }

        return protectionProducts;
    }

    private ProtectionProducts buildProtectionProductsObject(DealJacketResponse dealJacketResponse){
        ProtectionProducts protectionProducts = null;

        if (dealJacketResponse == null || CollectionUtils.isEmpty(dealJacketResponse.getInsuranceWarranties())) {
            return protectionProducts;
        }

        List<InsuranceWarrantiesItem> list = Optional.ofNullable(dealJacketResponse).map(DealJacketResponse::getInsuranceWarranties).orElse(Collections.emptyList());

        // convert to magellan version
        List<com.carsaver.magellan.model.protectioproducts.InsuranceWarranty> warranties = list.stream().map(i -> com.carsaver.magellan.model.protectioproducts.InsuranceWarranty.builder()
                .premium(i.getPremium())
                .productName(i.getProductName())
                .taxableIndicator(false)
                .build())
            .collect(Collectors.toList());
        protectionProducts = ProtectionProducts.builder().insuranceWarranties(warranties).build();
        return protectionProducts;
    }
}
