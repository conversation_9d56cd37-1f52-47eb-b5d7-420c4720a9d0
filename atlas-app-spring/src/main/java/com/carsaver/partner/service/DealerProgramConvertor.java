package com.carsaver.partner.service;

import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.foundation.ProductView;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.partner.client.DealerChatConfigClient;
import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.config.FeatureSubscriptionProperties;
import com.carsaver.partner.config.FeatureToggleProperties;
import com.carsaver.partner.config.ProgramProperties;
import com.carsaver.partner.model.DealerChatConfig;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.UserModel;
import com.carsaver.partner.service.nesna.DealerNesnaProductsService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;
@Component
@RequiredArgsConstructor
public class DealerProgramConvertor {

    public static final int ECOMMERCE_PRODUCT_ID = 102;
    private final ProgramProperties programProperties;

    private final FeatureSubscriptionProperties featureSubscriptionProperties;

    private final FeatureToggleProperties featureToggleProperties;

    private final ProgramClient programClient;

    private final UserClient userClient;

    private final SplitFeatureFlags splitFeatureFlags;

    private final DealerChatConfigClient dealerChatConfigClient;

    private final FeatureSubscriptionsClient featureSubscriptionsClient;

    private final DealerNesnaProductsService dealerNesnaProductsService;



    public DealerProgram convert(ProgramSubscriptionView programSubscription) {
        DealerProgram result = null;

        ProgramView program = programClient.findById(programSubscription.getProgramId()).orElse(null);
        if (program != null) {
            result = doConvert(program, programSubscription);
        }

        return result;
    }

    DealerProgram doConvert(ProgramView program, ProgramSubscriptionView programSubscription) {
        UserView successManager = Optional.ofNullable(programSubscription.getSuccessManagerId())
            .map(userClient::findById)
            .orElse(null);

        UserView accountManager = Optional.ofNullable(programSubscription.getAccountManagerId())
            .map(userClient::findById)
            .orElse(null);

        // Nissan chat feature code block here, if other programs need it then we can enhance the code to accommodate
        // and remove the nissan check
        boolean isChatEnabledValue = false;
        boolean isChatFeatureEnabledValue = false;
        DealerChatConfig dealerChatConfig = null;
        boolean isLibertyMutualEnabledValue = false;
        boolean isLibertyMutualFeatureEnabledValue = false;
        boolean isRouteOneFAndIEnabledValue = false;
        boolean isRouteOneFAndIFeatureEnabledValue = false;
        boolean isSellAtHomeEnabledValue = false;
        boolean isSellAtHomeFeatureEnabledValue = splitFeatureFlags.isSellAtHomeFeatureEnabled();
        boolean isInAppAlertsFeatureEnabledValue = true;
        boolean isGarageFeatureEnabledValue = true;
        boolean isSmsAlertsFeatureEnabledValue = true;
        boolean isEmailAlertsFeatureEnabledValue = true;
        boolean isSpanishTranslationsFeatureEnabled = splitFeatureFlags.isEnableLanguageSelection();
        boolean isCarsaverFAndIFeatureEnabled = splitFeatureFlags.isCarsaverFAndIEnabled();
        boolean isCarsaverFAndIFeatureEnabledValue = false;
        boolean isDealerTrackEnabled = false;
        boolean isBoostPlusFeaturesFeatureToggleEnabled = splitFeatureFlags.isBoostPlusFeaturesEnabled();
        boolean isBoostRetentionFeaturesFeatureToggleEnabled = splitFeatureFlags.isBoostRetentionFeaturesEnabled();
        boolean isOttoChatbotSplitFeatureEnabled = splitFeatureFlags.isOttoAIChatbotEnabled();

        boolean isInAppAlertsEnabled =  false;
        boolean isGarageEnabled = false;
        boolean isSmsAlertsEnabled = false;
        boolean isEmailAlertsEnabled = false;
        boolean isNissanBuyAtHomeProgram = programProperties.getNissanBuyAtHomeId().equals(programSubscription.getProgramId());
        boolean isBMWProgram = Optional.of(program).map(ProgramView::getName).filter(name -> name.toLowerCase().contains("bmw")).isPresent();
        boolean isBoostProgram = Optional.of(program).map(ProgramView::getProduct).map(ProductView::getId).orElse(-1) == ECOMMERCE_PRODUCT_ID && !isNissanBuyAtHomeProgram;
        boolean isWalmartProgram =  programProperties.getWalmartId().equals(programSubscription.getProgramId());
        boolean isNesnaFAndIEnabledValue = false;
        boolean isNesnaFAndIFeatureEnabledValue = splitFeatureFlags.isNesnaFAndIFeatureEnabled();
        boolean isNesnaConfigured=false;
        boolean isSpanishTranslationsEnabled = false;
        boolean isCarsaverFAndIEnabled = false;
        boolean isBoostFeaturesEnabled = false;
        boolean isBoostRetentionEnabled = false;
        boolean isOttoChatbotEnabled = false;

        if (isNissanBuyAtHomeProgram) {

            // Chat Feature
            Optional<DealerChatConfig> dealerChatConfigOptional = dealerChatConfigClient.getDealerChatConfigByDealerAndProgramOption(programSubscription.getDealerId(), programSubscription.getProgramId());
            isChatFeatureEnabledValue = featureToggleProperties.isChatEnable();
            isChatEnabledValue = dealerChatConfigOptional.map(DealerChatConfig::getChatEnabled).orElse(false);
            dealerChatConfig = dealerChatConfigOptional.orElse(null);

            // Otto Chatbot feature
            if (isOttoChatbotSplitFeatureEnabled) {
                isOttoChatbotEnabled = getOttoChatbotEnabledStatus(programSubscription);
            }

            if (featureToggleProperties.isLibertyMutualEnable()) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getLibertyMutualFeatureId());
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isLibertyMutualFeatureEnabledValue = featureToggleProperties.isLibertyMutualEnable();
                isLibertyMutualEnabledValue = response == null ? false : response.getActive();
            }

            if (featureToggleProperties.isRouteOneFinanceAndInsuranceEnable()) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getRouteoneFAndIFeatureId());
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isRouteOneFAndIFeatureEnabledValue = featureToggleProperties.isRouteOneFinanceAndInsuranceEnable();
                isRouteOneFAndIEnabledValue = response == null ? false : response.getActive();
            }

            if (isNesnaFAndIFeatureEnabledValue) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getNesnaFAndIFeatureId());
                request.setActive(true);
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isNesnaFAndIEnabledValue = response == null ? false : response.getActive();
                isNesnaConfigured = dealerNesnaProductsService.checkDealerNesnaConfiguration(programSubscription.getDealerId());
            }

            if (isSellAtHomeFeatureEnabledValue) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getSellAtHomeFeatureId());
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isSellAtHomeEnabledValue = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
            }
            if (isInAppAlertsFeatureEnabledValue) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getInAppAlertsFeatureId());
                FeatureSubscriptionResponse response = Optional.ofNullable(featureSubscriptionsClient.getFeatureSubscription(request))
                    .orElseGet(() -> {
                        request.setActive(Boolean.TRUE);
                        return featureSubscriptionsClient.saveFeatureSubscription(request);
                    });
                isInAppAlertsEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
            }
            if (isGarageFeatureEnabledValue) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getGarageAlertsFeatureId());
                FeatureSubscriptionResponse response = Optional.ofNullable(featureSubscriptionsClient.getFeatureSubscription(request))
                    .orElseGet(() -> {
                        request.setActive(Boolean.TRUE);
                        return featureSubscriptionsClient.saveFeatureSubscription(request);
                    });
                isGarageEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
            }
            if (isSmsAlertsFeatureEnabledValue) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getSmsAlertsFeatureId());
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isSmsAlertsEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
            }
            if (isEmailAlertsFeatureEnabledValue) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getEmailAlertsFeatureId());
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isEmailAlertsEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
            }

            if (isSpanishTranslationsFeatureEnabled) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getSpanishTranslationFeatureId());
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isSpanishTranslationsEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
            }

            if (isBoostPlusFeaturesFeatureToggleEnabled) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getBoostFeaturesFeatureId());
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isBoostFeaturesEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
            }

            // Boost Retention Feature
            if (isBoostRetentionFeaturesFeatureToggleEnabled) {
                isBoostRetentionEnabled = getBoostRetentionEnabledStatus(programSubscription);
            }

        } else if (isBMWProgram && !isBoostProgram) {
            if (featureToggleProperties.isChatEnable()) {
                Optional<DealerChatConfig> dealerChatConfigOptional =
                    dealerChatConfigClient.getDealerChatConfigByDealerAndProgramOption(programSubscription.getDealerId(), programSubscription.getProgramId());
                isChatFeatureEnabledValue = featureToggleProperties.isChatEnable();
                isChatEnabledValue = dealerChatConfigOptional.map(DealerChatConfig::getChatEnabled).orElse(false);
                dealerChatConfig = dealerChatConfigOptional.orElse(null);
            }
        } else if (isWalmartProgram) {
            if (isCarsaverFAndIFeatureEnabled) {
                FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                request.setDealerId(programSubscription.getDealerId());
                request.setEntityId(programSubscription.getProgramId());
                request.setFeatureRId(featureSubscriptionProperties.getCarsaverFAndIFeatureId());
                FeatureSubscriptionResponse response = getFeatureSubscription(request);
                isCarsaverFAndIEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);

                isCarsaverFAndIFeatureEnabledValue = isCarsaverFAndIFeatureEnabled;
            }
        } else if (isBoostProgram) {

                if (isSpanishTranslationsFeatureEnabled) {
                    FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                    request.setDealerId(programSubscription.getDealerId());
                    request.setEntityId(programSubscription.getProgramId());
                    request.setFeatureRId(featureSubscriptionProperties.getSpanishTranslationFeatureId());
                    FeatureSubscriptionResponse response = getFeatureSubscription(request);
                    isSpanishTranslationsEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
                }

                if (isSellAtHomeFeatureEnabledValue) {
                    FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                    request.setDealerId(programSubscription.getDealerId());
                    request.setEntityId(programSubscription.getProgramId());
                    request.setFeatureRId(featureSubscriptionProperties.getSellAtHomeFeatureId());
                    FeatureSubscriptionResponse response = getFeatureSubscription(request);
                    isSellAtHomeEnabledValue = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(true);
                }

                if (isInAppAlertsFeatureEnabledValue) {
                    FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                    request.setDealerId(programSubscription.getDealerId());
                    request.setEntityId(programSubscription.getProgramId());
                    request.setFeatureRId(featureSubscriptionProperties.getInAppAlertsFeatureId());
                    FeatureSubscriptionResponse response = Optional.ofNullable(featureSubscriptionsClient.getFeatureSubscription(request))
                        .orElseGet(() -> {
                            request.setActive(Boolean.TRUE);
                            return featureSubscriptionsClient.saveFeatureSubscription(request);
                        });
                    isInAppAlertsEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
                }
                if (isGarageFeatureEnabledValue) {
                    FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                    request.setDealerId(programSubscription.getDealerId());
                    request.setEntityId(programSubscription.getProgramId());
                    request.setFeatureRId(featureSubscriptionProperties.getGarageAlertsFeatureId());
                    FeatureSubscriptionResponse response = Optional.ofNullable(featureSubscriptionsClient.getFeatureSubscription(request))
                        .orElseGet(() -> {
                            request.setActive(Boolean.TRUE);
                            return featureSubscriptionsClient.saveFeatureSubscription(request);
                        });
                    isGarageEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
                }
                if (isSmsAlertsFeatureEnabledValue) {
                    FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                    request.setDealerId(programSubscription.getDealerId());
                    request.setEntityId(programSubscription.getProgramId());
                    request.setFeatureRId(featureSubscriptionProperties.getSmsAlertsFeatureId());
                    FeatureSubscriptionResponse response = getFeatureSubscription(request);
                    isSmsAlertsEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
                }
                if (isEmailAlertsFeatureEnabledValue) {
                    FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                    request.setDealerId(programSubscription.getDealerId());
                    request.setEntityId(programSubscription.getProgramId());
                    request.setFeatureRId(featureSubscriptionProperties.getEmailAlertsFeatureId());
                    FeatureSubscriptionResponse response = getFeatureSubscription(request);
                    isEmailAlertsEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
                }

                if (isCarsaverFAndIFeatureEnabled) {
                    FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                    request.setDealerId(programSubscription.getDealerId());
                    request.setEntityId(programSubscription.getProgramId());
                    request.setFeatureRId(featureSubscriptionProperties.getCarsaverFAndIFeatureId());
                    FeatureSubscriptionResponse response = getFeatureSubscription(request);
                    isCarsaverFAndIEnabled = Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);

                    isCarsaverFAndIFeatureEnabledValue = isCarsaverFAndIFeatureEnabled;
                }

                if (featureToggleProperties.isRouteOneFinanceAndInsuranceEnable()) {
                    FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
                    request.setDealerId(programSubscription.getDealerId());
                    request.setEntityId(programSubscription.getProgramId());
                    request.setFeatureRId(featureSubscriptionProperties.getRouteoneFAndIFeatureId());
                    FeatureSubscriptionResponse response = getFeatureSubscription(request);
                    isRouteOneFAndIFeatureEnabledValue = featureToggleProperties.isRouteOneFinanceAndInsuranceEnable();
                    isRouteOneFAndIEnabledValue = response == null ? false : response.getActive();
                }

                // Dealer Track
                FeatureSubscriptionRequest dealerTrackRequest = new FeatureSubscriptionRequest();
                dealerTrackRequest.setDealerId(programSubscription.getDealerId());
                dealerTrackRequest.setEntityId(programSubscription.getProgramId());
                dealerTrackRequest.setFeatureRId(featureSubscriptionProperties.getLmsFeatureId());
                FeatureSubscriptionResponse dealerTrackResponse = getFeatureSubscription(dealerTrackRequest);
                isDealerTrackEnabled = Optional.ofNullable(dealerTrackResponse).map(FeatureSubscriptionResponse::getActive).orElse(false);

                // Chat Feature
                Optional<DealerChatConfig> dealerChatConfigOptional = dealerChatConfigClient.getDealerChatConfigByDealerAndProgramOption(programSubscription.getDealerId(), programSubscription.getProgramId());
                isChatFeatureEnabledValue = featureToggleProperties.isChatEnable();
                isChatEnabledValue = dealerChatConfigOptional.map(DealerChatConfig::getChatEnabled).orElse(false);
                dealerChatConfig = dealerChatConfigOptional.orElse(null);

                // Otto Chatbot feature
                if (isOttoChatbotSplitFeatureEnabled) {
                    isOttoChatbotEnabled = getOttoChatbotEnabledStatus(programSubscription);
                }

                // Boost Retention Feature
                if (isBoostRetentionFeaturesFeatureToggleEnabled) {
                    isBoostRetentionEnabled = getBoostRetentionEnabledStatus(programSubscription);
                }
            }

        final DealerProgram result = DealerProgram.builder()
            .name(program.getName())
            .status(programSubscription.getStatus())
            .successManager(UserModel.from(successManager))
            .accountManager(UserModel.from(accountManager))
            .supportEmail(program.getSupportEmail())
            .supportPhone(program.getSupportPhone())
            .isChatFeatureEnabled(isChatFeatureEnabledValue)
            .isChatEnabled(isChatEnabledValue)
            .isLibertyMutualFeatureEnabled(isLibertyMutualFeatureEnabledValue)
            .isLibertyMutualEnabled(isLibertyMutualEnabledValue)
            .isRouteOneFAndIFeatureEnabled(isRouteOneFAndIFeatureEnabledValue)
            .isRouteOneFAndIEnabled(isRouteOneFAndIEnabledValue)
            .isNesnaFAndIEnabled(isNesnaFAndIEnabledValue)
            .isNesnaConfigured(isNesnaConfigured)
            .isSellAtHomeEnabled(isSellAtHomeEnabledValue)
            .isSellAtHomeFeatureEnabled(isSellAtHomeFeatureEnabledValue)
            .isNissanBuyAtHome(isNissanBuyAtHomeProgram)
            .isBoostProgram(isBoostProgram)
            .isInAppAlertsFeatureEnabled(isInAppAlertsFeatureEnabledValue)
            .isInAppAlertsEnabled(isInAppAlertsEnabled)
            .isGarageAlertsFeatureEnabled(isGarageFeatureEnabledValue)
            .isGarageAlertsEnabled(isGarageEnabled)
            .isEmailAlertsFeatureEnabled(isEmailAlertsFeatureEnabledValue)
            .isEmailAlertsEnabled(isEmailAlertsEnabled)
            .isSmsAlertsFeatureEnabled(isSmsAlertsFeatureEnabledValue)
            .isSmsAlertsEnabled(isSmsAlertsEnabled)
            .programId(program.getId())
            .dealerChatConfig(dealerChatConfig)
            .isSpanishTranslationFeatureEnabled(isSpanishTranslationsFeatureEnabled)
            .isSpanishTranslationEnabled(isSpanishTranslationsEnabled)
            .isCarsaverFAndIFeatureEnabled(isCarsaverFAndIFeatureEnabledValue)
            .isCarsaverFAndIEnabled(isCarsaverFAndIEnabled)
            .isDealerTrackEnabled(isDealerTrackEnabled)
            .isBoostFeaturesEnabled(isBoostFeaturesEnabled)
            .isBoostRetentionFeatureEnabled(isBoostRetentionFeaturesFeatureToggleEnabled)
            .isBoostRetentionEnabled(isBoostRetentionEnabled)
            .isOttoChatbotEnabled(isOttoChatbotEnabled)
            .build();

        return result;
    }

    private FeatureSubscriptionResponse getFeatureSubscription(FeatureSubscriptionRequest request) {
        return featureSubscriptionsClient.getFeatureSubscription(request);
    }

    private boolean getOttoChatbotEnabledStatus(ProgramSubscriptionView programSubscription) {
        FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
        request.setDealerId(programSubscription.getDealerId());
        request.setEntityId(programSubscription.getProgramId());
        request.setFeatureRId(featureSubscriptionProperties.getOttoAIChatbotFeatureId());
        FeatureSubscriptionResponse response = getFeatureSubscription(request);
        return Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
    }

    private boolean getBoostRetentionEnabledStatus(ProgramSubscriptionView programSubscription) {
        FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
        request.setDealerId(programSubscription.getDealerId());
        request.setEntityId(programSubscription.getProgramId());
        request.setFeatureRId(featureSubscriptionProperties.getBoostRetentionFeatureId());
        FeatureSubscriptionResponse response = getFeatureSubscription(request);
        return Optional.ofNullable(response).map(FeatureSubscriptionResponse::getActive).orElse(false);
    }

}
