package com.carsaver.partner.service.sellathome;

import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.carsaver.partner.web.api.user.UserTradesApiController.SELL_AT_HOME;

@Service
@Slf4j
public class UserVehicleSellInfoService {

    final FeatureSubscriptionsClient featureSubscriptionsClient;
    final UserClient userClient;

    final CampaignClient campaignClient;

    @Value("${features-subscription.sell-at-home-feature-id}")
    private String sellAtHomeFeatureId;

    @Value("${program.nissan-buy-at-home-id}")
    private String buyAtHomeProgramId;

    @Autowired

    public UserVehicleSellInfoService(FeatureSubscriptionsClient featureSubscriptionsClient, UserClient userClient, CampaignClient campaignClient) {
        this.featureSubscriptionsClient = featureSubscriptionsClient;
        this.userClient = userClient;
        this.campaignClient = campaignClient;
    }


    public Boolean isSellAtHomeFeatureEnabledForUserId(String dealerId, String userId) {
        log.info("Determining if {} feature is available for dealerId {} userId {}", SELL_AT_HOME, dealerId, userId);

        Optional<UserView> userViewOpt = Optional.ofNullable(userClient.findById(userId));

        Boolean isFeatureEnabled = userViewOpt
            .map(UserView::getSource)
            .map(Source::getCampaignId)
            .map(campaignClient::findById)
            .map(this::retrieveProgramId)
            .map(programId -> FeatureSubscriptionRequest.builder()
                .entityId(programId)
                .dealerId(dealerId)
                .featureRId(sellAtHomeFeatureId)
                .context(SELL_AT_HOME)
                .build())
            .map(featureSubscriptionsClient::isFeatureEnabled)
            .orElse(Boolean.FALSE);

        return isFeatureEnabled;
    }

    public Boolean isSellAtHomeFeatureEnabled(String dealerId) {
        log.info("Determining if {} feature is available for {}", SELL_AT_HOME, dealerId);


        FeatureSubscriptionRequest request = FeatureSubscriptionRequest.builder()
                .entityId(buyAtHomeProgramId)
                .dealerId(dealerId)
                .featureRId(sellAtHomeFeatureId)
                .context(SELL_AT_HOME)
                .build();

        Boolean result = featureSubscriptionsClient.isFeatureEnabled(request);

        return result;
    }



    String retrieveProgramId(CampaignView campaign) {
        String programId = Optional.ofNullable(campaign).map(CampaignView::getProgramId).orElse(null);
        return programId;
    }

}
