package com.carsaver.partner.service.activity

import com.carsaver.partner.client.activity.ActivityClient
import com.carsaver.partner.client.dealer.DealerClient
import com.carsaver.partner.client.dealer.DealerV2
import com.carsaver.partner.client.digitalretail.ChatHistoryItem
import com.carsaver.partner.client.digitalretail.DealerTokenExchange
import com.carsaver.partner.client.digitalretail.DigitalRetailClient
import com.carsaver.partner.exception.BadRequestException
import com.carsaver.partner.exception.ConflictException
import com.carsaver.partner.exception.NotFoundException
import lombok.AllArgsConstructor
import org.springframework.stereotype.Service
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.stream.Collectors

@Service
@AllArgsConstructor
class ActivityLogsService(
    private val activityClient: ActivityClient,
    private val dealerClient: DealerClient,
    private val activityLogMapper: ActivityLogMapper,
    private val digitalRetailClient: DigitalRetailClient
) {


    fun process(userId: String, dealerIds: List<String>): List<DigitalRetailActivityLogContainer?> {

        if (dealerIds.size != 1) {
            throw BadRequestException("Exactly one dealerId is required.")
        }

        val dealer = dealerClient.getDealerById(dealerIds[0])
            ?: throw NotFoundException("Dealer not found ${dealerIds[0]}")

        val logs = activityClient.getDigitalRetailLogsByUserIdAndDealerIds(userId, dealerIds)

        val token = digitalRetailClient.exchangeDealerToken(DealerTokenExchange(userId, dealerIds[0]))

        val chatHistory = digitalRetailClient.getAllChatHistory(token.token.accessToken)

        val map = activityLogMapper.map(logs, dealer, chatHistory, userId)

        val lists = groupLogsByDaysAndType(map)

        return toContainers(lists, dealer)
    }

    private fun toContainers(
        lists: List<List<ActivityLogDTO>>,
        dealer: DealerV2
    ): List<DigitalRetailActivityLogContainer?> {
        return lists.stream()
            .map { logs: List<ActivityLogDTO> -> this.toContainer(logs, dealer) }
            .collect(Collectors.toList()).filterNotNull()
    }

    private fun toContainer(
        logs: List<ActivityLogDTO>,
        dealer: DealerV2
    ): DigitalRetailActivityLogContainer? {
        val copy = ArrayList(logs)

        copy.sortedBy { it.eventTime }

        val log = logs[0]

        val isCheckin = copy.removeIf { it.activityType == ActivityType.CHECKIN }

        // may be used later but not now
        copy.removeIf { it.activityType == ActivityType.WEBSITE_VISIT_SUCCESSFUL_LOGIN }

        // no other records
        if (copy.isEmpty()) {
            return null
        }

        // assume website if no checking
        val visitType = if (isCheckin) VisitType.SHOWROOM else VisitType.WEBSITE


        val originWebSite: String? = if (logs.any { log.origin == "nissanusa" }) {
            "NissanUSA.com"
        } else {
            ActivityUtils.extractDomain(dealer.websiteUrl)
        }

        return DigitalRetailActivityLogContainer(
            log.eventTime, visitType, log.dealerId, dealer.name, originWebSite, copy
        )
    }

    fun retrieveChatLog(userId: String, dealerId: String, eventTime: ZonedDateTime): ChatHistoryContentResponse {
        val dealer = dealerClient.getDealerById(dealerId = dealerId)
            ?: throw NotFoundException("Dealer not found dealer $dealerId")

        val timeZone = dealer.timeZone ?: throw ConflictException("Dealer time zone is missing")

        val token = digitalRetailClient.exchangeDealerToken(DealerTokenExchange(userId, dealerId))
        val content = digitalRetailClient.getChatHistoryContent(
            eventTime.withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime(),
            token.token.accessToken
        )

        val mapped = mapToActivityLogDTO(content, userId, timeZone)

        return ChatHistoryContentResponse(mapped, content)
    }

    companion object {
        // need to add dealer id here
        fun groupLogsByDaysAndType(logs: List<ActivityLogDTO>): List<List<ActivityLogDTO>> {
            val groupedByDay = logs.groupBy { it.eventTime!!.toLocalDate() }

            val result = mutableListOf<List<ActivityLogDTO>>()

            groupedByDay.keys.sortedDescending().forEach {
                val group = groupedByDay[it]!!

                val (checkIns, others) = group.partition {
                    it.activityType == ActivityType.CHECKIN
                }

                if (checkIns.isEmpty()) {
                    result.add(others)
                } else {
                    val sortedCheckins = checkIns.sortedByDescending { log ->
                        log.eventTime
                    }.map {
                        it to mutableListOf(it)
                    }

                    val generic = mutableListOf<ActivityLogDTO>()
                    others.forEach { log ->

                        sortedCheckins.firstOrNull { potentialPair ->
                            val checkinEventTime = potentialPair.first.eventTime!!

                            log.eventTime!!.isAfter(checkinEventTime)
                                && log.eventTime!!.isBefore(checkinEventTime.plusHours(4))

                        }?.second?.add(log) ?: generic.add(log)

                    }

                    sortedCheckins.forEach {
                        result.add(it.second)
                    }

                    if (generic.isNotEmpty()) {
                        result.add(generic)
                    }
                }
            }

            val sorted = mutableListOf<List<ActivityLogDTO>>()

            result.forEach {
                sorted.add(it.sortedBy { it.eventTime })
            }

            return sorted.sortedByDescending { it.first().eventTime }
        }
    }
}
