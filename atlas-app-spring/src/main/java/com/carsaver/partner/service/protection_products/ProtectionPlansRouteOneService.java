package com.carsaver.partner.service.protection_products;

import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class ProtectionPlansRouteOneService {

    public static final String ROUTE_ONE_PROTECTION_PRODUCTS_SUBSCRIPTION = "RouteOne Protection Products Subscription: ";

    @Value("${routeone.protection-products.feature-r-id}")
    private String featureRId;

    @Value("${features-toggle.protection-products-enabled:false}")
    private boolean isProtectionProductsFeatureEnabled;

    private final FeatureSubscriptionsClient featureSubscriptionClient;

    public ProtectionPlansRouteOneService(FeatureSubscriptionsClient featureSubscriptionClient) {
        this.featureSubscriptionClient = featureSubscriptionClient;
    }

    /**
     * Does all the checks needed to see if the feature is enabled for the dealer
     *
     * @return         Boolean
     */
    public Boolean isRouteOneProtectionProductsEnabled(CertificateView certificate) {
        Boolean enabled = false ;
        if (isProtectionProductsFeatureEnabled) {
            String dealerId = certificate.getDealerId();
            CampaignView campaignView = certificate.getCampaign();
            enabled = isRouteOneProtectionProductsEnabledByDealerId(dealerId, campaignView);
        }
        return enabled;
    }

    /**
     * Only checks if the dealer is enabled
     *
     * @param dealerId  id used to check if it is enabled
     * @return          Boolean
     */
    public Boolean isRouteOneProtectionProductsEnabledByDealerId(String dealerId, CampaignView campaignView) {
        log.info("Determining if route one protection products feature is available for {}", dealerId);

        String programId = retrieveProgramId(campaignView);

        FeatureSubscriptionRequest request = FeatureSubscriptionRequest.builder()
            .entityId(programId)
            .dealerId(dealerId)
            .featureRId(featureRId)
            .context(ROUTE_ONE_PROTECTION_PRODUCTS_SUBSCRIPTION)
            .build();

        Boolean result = featureSubscriptionClient.isFeatureEnabled(request);

        return result;
    }

    String retrieveProgramId(CampaignView campaign) {
        String programId = Optional.ofNullable(campaign).map(CampaignView::getProgramId).orElse(null);
        return programId;
    }


}
