package com.carsaver.partner.service.activity

import com.carsaver.partner.client.activity.ActivityLog
import com.carsaver.partner.client.dealer.CertificateV2
import com.carsaver.partner.client.dealer.DealerClient
import com.carsaver.partner.client.dealer.DealerV2
import com.carsaver.partner.client.digitalretail.ChatHistoryItem
import com.carsaver.partner.client.digitalretail.ChatHistorySummary
import com.carsaver.partner.client.inventory.v2.InventoryClientV2
import com.carsaver.partner.client.inventory.v2.InventoryVehicleV2
import com.carsaver.partner.client.leads.v2.LeadClientV2
import com.carsaver.partner.client.leads.v2.LeadV2
import com.carsaver.partner.client.vehicle.StyleSummaryV2
import com.carsaver.partner.client.vehicle.VehicleClientV2
import com.carsaver.partner.exception.ConflictException
import lombok.AllArgsConstructor
import lombok.Data
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.ZoneId
import java.util.concurrent.ConcurrentHashMap

@Service
@AllArgsConstructor
class ActivityLogMapper(
    private val leadsClient: LeadClientV2,
    private val dealerClient: DealerClient,
    private val inventoryClient: InventoryClientV2,
    private val vehicleClientV2: VehicleClientV2
) {

    private val logger = LoggerFactory.getLogger(ActivityLogMapper::class.java)


    fun map(
        activityLogs: List<ActivityLog>,
        dealer: DealerV2,
        chatHistory: List<ChatHistorySummary>,
        userId: String
    ): List<ActivityLogDTO> {
        val retrievedData = RetrievedData()

        val timeZone = dealer.timeZone ?: throw ConflictException("Dealer time zone is missing")

        val mappedLogs: List<ActivityLogDTO> = activityLogs.map { activityLog: ActivityLog ->

            val log = ActivityLogDTO()
            val from: ActivityType? = ActivityType.from(activityLog)
            log.activityType = from
            log.userId = activityLog.userId
            log.dealerId = activityLog.dealerId
            log.campaignId = safeGetMetadata(activityLog, "campaignId") as String?
            log.eventTime = activityLog.eventTime?.withZoneSameInstant(ZoneId.of(timeZone))
            log.certificateId = getCertificateId(activityLog)
            log.vehicleId = safeGetMetadata(activityLog, "vehicleId") as String?
            log.styleId = safeGetMetadataAsInt(activityLog, "styleId")
            log.origin = safeGetMetadata(activityLog, "origin") as String?
            log.leadId = activityLog.leadId
            log.preQualStatus = safeGetMetadata(activityLog, "preQualStatus") as String?
            log
        }

        val mappedChatHistory = chatHistory.map { item ->
            mapToActivityLogDTO(item, userId, timeZone)
        }

        val all = mappedLogs + mappedChatHistory

        return all.map {
            populateForLinks(it, retrievedData)
        }
    }


    private fun populateForLinks(
        activityLog: ActivityLogDTO,
        retrievedData: RetrievedData
    ): ActivityLogDTO {
        if (setOf(
                ActivityType.VEHICLE_VIEWED,
                ActivityType.DEAL_SAVED,
                ActivityType.LEAD_SENT,
                ActivityType.CREDIT_APPLICATION_SUBMITTED
            ).contains(activityLog.activityType)
        ) {
            val singleRecordData = SingleRecordData()

            try {
                if (activityLog.campaignId?.isNotBlank() == false) {
                    populateCertIfNeeded(singleRecordData, activityLog, retrievedData)
                    activityLog.campaignId = singleRecordData.cert?.source?.campaignId
                }

                populateVehicleIfNeeded(singleRecordData, activityLog, retrievedData)

                populateStyleInfo(activityLog, retrievedData, singleRecordData)
            } catch (e: MissingDataException) {
                logger.error("Error populating data for activity log $activityLog", e)
            } catch (e: AcceptableMissingException) {
                logger.info("Acceptable missing data for activity log {} {}", e.message, activityLog)
            }
        }

        return activityLog
    }

    private fun populateStyleInfo(
        activityLog: ActivityLogDTO,
        retrievedData: RetrievedData,
        singleRecordData: SingleRecordData
    ) {
        if (singleRecordData.style != null) {
            return
        }

        if (activityLog.styleId == null) {
            populateVehicleIfNeeded(singleRecordData, activityLog, retrievedData)
        }

        // would have failed above so should be populated now.
        singleRecordData.style = retrievedData.styles.computeIfAbsent(activityLog.styleId!!) {
            vehicleClientV2.getStyleByStyleId(it) ?: throw MissingDataException(
                "Cannot find style with id $it"
            )
        }

        activityLog.make = singleRecordData.style?.make
        activityLog.model = singleRecordData.style?.model
        activityLog.year = singleRecordData.style?.year
    }

    private fun populateVehicleIfNeeded(
        singleRecordData: SingleRecordData,
        activityLog: ActivityLogDTO,
        retrievedData: RetrievedData
    ) {
        if (singleRecordData.inventory != null) {
            return
        }

        if (StringUtils.isBlank(activityLog.vehicleId)) {
            populateCertIfNeeded(singleRecordData, activityLog, retrievedData)
        }

        // would have failed above so should be populated now.
        singleRecordData.inventory = retrievedData.inventoryVehicles.computeIfAbsent(
            activityLog.vehicleId!!
        ) {
            inventoryClient.getInventoryVehicleById(it) ?: throw MissingDataException(
                "Cannot find vehicle with id $it"
            )
        }

        activityLog.isVehicleActive = singleRecordData.inventory?.active
        activityLog.styleId = singleRecordData.inventory?.styleId
        activityLog.vin = singleRecordData.inventory?.vin
    }


    private fun populateCertIfNeeded(
        singleRecordData: SingleRecordData,
        activityLog: ActivityLogDTO,
        retrievedData: RetrievedData
    ) {
        if (singleRecordData.cert != null) {
            return
        }

        if (activityLog.certificateId == null) {
            populateConnectionLeadIfNeeded(activityLog, retrievedData, singleRecordData)
        }

        // would have failed above so should be populated now.
        singleRecordData.cert = retrievedData.certificates.computeIfAbsent(
            activityLog.certificateId!!
        ) { id: Long ->
            val cert = dealerClient.getCertificateById(id) ?: throw MissingDataException("Cannot find cert with id $id")
            if (cert.inventoryId == null) {
                throw AcceptableMissingException("Certificated does not reference vehicle $id")
            }
            cert
        }

        activityLog.vehicleId = singleRecordData.cert?.inventoryId
        activityLog.campaignId = singleRecordData.cert?.source?.campaignId
    }

    private fun populateConnectionLeadIfNeeded(
        activityLog: ActivityLogDTO,
        retrievedData: RetrievedData,
        data: SingleRecordData
    ) {
        if (data.lead != null) {
            return
        }

        // would have failed above so should be populated now.
        if (StringUtils.isNotBlank(activityLog.leadId)) {
            data.lead = retrievedData.leads.computeIfAbsent(
                activityLog.leadId!!
            ) { id: String ->
                leadsClient.getLeadById(id) ?: throw MissingDataException(
                    "Cannot find lead with id $id"
                )
            }

            if (data.lead?.certificateId == null) {
                // may not be about a vehicle at all (for example, session end leads)
                throw AcceptableMissingException(
                    "Certificate ID is missing from lead, probably not about a vehicle "
                        + activityLog.leadId + " " + data.lead
                )
            }
            activityLog.certificateId = data.lead?.certificateId
        } else {
            throw MissingDataException("Lead ID is missing")
        }
    }


    companion object {
        @JvmStatic
        fun getCertificateId(activityLog: ActivityLog): Long? {
            if (activityLog.metadata != null) {
                val certificateId = activityLog.metadata!!["certificateId"]
                when (certificateId) {
                    is Int -> {
                        return certificateId.toLong()
                    }

                    is Long -> {
                        return certificateId
                    }

                    is String -> {
                        return certificateId.toLong()
                    }
                }
            }
            return null
        }

        @JvmStatic
        fun safeGetMetadataAsInt(activityLog: ActivityLog, value: String?): Int? {
            return when (val o = safeGetMetadata(activityLog, value)) {
                null -> {
                    null
                }

                is String -> {
                    o.toInt()
                }

                else -> {
                    o as Int?
                }
            }
        }

        fun safeGetMetadata(activityLog: ActivityLog, value: String?): Any? {
            if (activityLog.metadata != null) {
                return activityLog.metadata!![value]
            }
            return null
        }
    }


}

@Data
data class SingleRecordData(
    var lead: LeadV2? = null,
    var cert: CertificateV2? = null,
    var inventory: InventoryVehicleV2? = null,
    var style: StyleSummaryV2? = null
)

data class RetrievedData(
    val leads: ConcurrentHashMap<String, LeadV2> = ConcurrentHashMap(),
    val certificates: ConcurrentHashMap<Long, CertificateV2> = ConcurrentHashMap(),
    val inventoryVehicles: ConcurrentHashMap<String, InventoryVehicleV2> = ConcurrentHashMap(),
    val styles: ConcurrentHashMap<Int, StyleSummaryV2> = ConcurrentHashMap()
)

class MissingDataException(message: String?) : RuntimeException(message)

class AcceptableMissingException(message: String?) : RuntimeException(message)

fun mapToActivityLogDTO(item: ChatHistorySummary, userId: String, timeZone: String): ActivityLogDTO {
    val log = ActivityLogDTO()
    log.activityType = ActivityType.CHAT
    log.userId = userId
    log.dealerId = item.dealerId
    log.campaignId = item.campaignId
    log.eventTime = item.startTime?.atZone(ZoneId.of("UTC"))?.withZoneSameInstant(ZoneId.of(timeZone))
    log.vehicleId = item.vehicleId
    log.styleId = null
    log.origin = null
    log.leadId = null
    log.preQualStatus = null
    return log
}

fun mapToActivityLogDTO(item: ChatHistoryItem, userId: String, timeZone: String): ActivityLogDTO {
    val log = ActivityLogDTO()
    log.activityType = ActivityType.CHAT
    log.userId = userId
    log.dealerId = item.dealerId
    log.campaignId = item.campaignId
    log.eventTime = item.startTime?.atZone(ZoneId.of("UTC"))?.withZoneSameInstant(ZoneId.of(timeZone))
    log.vehicleId = item.vehicleId
    log.styleId = null
    log.origin = null
    log.leadId = null
    log.preQualStatus = null
    return log
}
