package com.carsaver.partner.service;

import com.carsaver.core.StockType;
import com.carsaver.magellan.auth.SessionUtils;
import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.client.FollowUpManagerClient;
import com.carsaver.magellan.client.ProgramManagerClient;
import com.carsaver.magellan.client.SalesManagerClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.request.programmanager.StockTypeUpdateRequest;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.security.DealerPermissionView;
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation;
import com.carsaver.magellan.model.user.FollowUpManagerRoleAssociation;
import com.carsaver.magellan.model.user.ProgramManagerRoleAssociation;
import com.carsaver.magellan.model.user.SalesManagerRoleAssociation;
import com.carsaver.partner.client.DealerSalesPerson;
import com.carsaver.partner.client.UserServiceClient;
import com.carsaver.partner.model.DealerUser;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.web.form.DealerPermissionsForm;
import com.carsaver.partner.web.form.UserForm;
import com.carsaver.stereotype.ContactType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DealerUserService {

    private final UserClient userClient;
    private final BasicUserAssociationClient basicUserAssociationClient;
    private final ProgramManagerClient programManagerClient;
    private final SalesManagerClient salesManagerClient;
    private final FollowUpManagerClient followUpManagerClient;
    private final UserServiceClient userServiceClient;
    private final SplitFeatureFlags splitFeatureFlags;

    public List<DealerUser> getDealerUsers(String dealerId){
        Collection<ProgramManagerRoleAssociation> programManagerAssociations = programManagerClient.findByDealerId(dealerId).getContent();
        Collection<FollowUpManagerRoleAssociation> followUpManagerAssociations = followUpManagerClient.findByDealerId(dealerId).getContent();
        Collection<SalesManagerRoleAssociation> salesManagerAssociations = salesManagerClient.findByDealerId(dealerId).getContent();

        final Collection<DealerSalesPerson> salesPersonsAssociations;
        if (splitFeatureFlags.isDealerSalesPersonEnabled(dealerId, SessionUtils.getLoggedInUser())) {
            salesPersonsAssociations = userServiceClient.getAllSalesPerson(dealerId);
        } else {
            salesPersonsAssociations = Collections.emptyList();
        }

        return basicUserAssociationClient.findDealerGrantedPrivileges(dealerId).getContent()
                .stream()
                .map(basicUser -> {
                    ProgramManagerRoleAssociation userProgramManagerAssociation = programManagerAssociations.stream().filter(pma -> pma.getUserId().equals(basicUser.getUserId())).findFirst().orElse(null);
                    FollowUpManagerRoleAssociation userFollowUpManagerAssociation = followUpManagerAssociations.stream().filter(fma -> fma.getUserId().equals(basicUser.getUserId())).findFirst().orElse(null);
                    SalesManagerRoleAssociation userSalesManagerAssociation = salesManagerAssociations.stream().filter(sma -> sma.getUserId().equals(basicUser.getUserId())).findFirst().orElse(null);
                    DealerSalesPerson dealerSalesPerson = salesPersonsAssociations.stream().filter(spa -> Objects.equals(spa.getUserId(), basicUser.getUserId())).findFirst().orElse(null);

                    List<DealerPermissionView> permissions = Optional.ofNullable(basicUser).map(BasicDealerUserRoleAssociation::getPermissionList).orElseGet(Collections::emptyList)
                            .stream()
                            .filter(getPermissionFilter())
                            .collect(Collectors.toList());

                    return DealerUser.from(basicUser.getUser(),
                        basicUser,
                        userProgramManagerAssociation,
                        userFollowUpManagerAssociation,
                        userSalesManagerAssociation,
                        dealerSalesPerson,
                        permissions);
                })
                .collect(Collectors.toList());
    }

    public boolean isProgramManager(String userId, String dealerId){
        boolean result = programManagerClient.getProgramManager(userId, dealerId).isPresent();
        return result;
    }

    public  Predicate<DealerPermissionView> getPermissionFilter() {
        return p -> !p.isLegacy();
    }

    public void removeDealerUserAssociation(String dealerId, String userId){
        programManagerClient.getProgramManager(dealerId, userId)
                .map(ProgramManagerRoleAssociation::getId)
                .ifPresent(programManagerClient::removeProgramManager);

        followUpManagerClient.getFollowUpManager(dealerId, userId)
                .map(FollowUpManagerRoleAssociation::getId)
                .ifPresent(followUpManagerClient::removeFollowUpManager);

        salesManagerClient.getProgramManager(dealerId, userId)
                .map(SalesManagerRoleAssociation::getId)
                .ifPresent(salesManagerClient::removeProgramManager);

        try{
            userServiceClient.unassignSalesPerson(userId, dealerId);
        } catch (Exception ignored){}

        basicUserAssociationClient.getBasicUserAssociation(dealerId, userId)
                .map(BasicDealerUserRoleAssociation::getId)
                .ifPresent(basicUserAssociationClient::removeBasicUser);
    }

    public void associateUserToDealer(DealerPermissionsForm form, String dealerId, String userId, List<Integer> permissions, Optional<UserView> loggedInUser) {
        String pmStockType = form.getPmStockType();
        Boolean programManager = form.getProgramManager();
        StockType pmStockTypeEnum = StockType.of(pmStockType);
        pmStockTypeEnum = pmStockTypeEnum == null || pmStockTypeEnum == StockType.CONFIGURED ? null : pmStockTypeEnum;
        handleProgramManagerAssociation(programManager, pmStockTypeEnum, dealerId, userId);

        String fmStockType = form.getFmStockType();
        Boolean followupManager = form.getFollowupManager();
        StockType fmStockTypeEnum = StockType.of(fmStockType);
        fmStockTypeEnum = fmStockTypeEnum == null || fmStockTypeEnum == StockType.CONFIGURED ? null : fmStockTypeEnum;
        handleFollowupManagerAssociation(followupManager, fmStockTypeEnum, dealerId, userId);

        String smStockType = form.getSmStockType();
        Boolean salesManager = form.getSalesManager();
        StockType smStockTypeEnum = StockType.of(smStockType);
        smStockTypeEnum = smStockTypeEnum == null || smStockTypeEnum == StockType.CONFIGURED ? null : smStockTypeEnum;
        handleSalesManagerAssociation(salesManager, smStockTypeEnum, dealerId, userId);

        handleSalesPersonAssociation(form.getSalesPerson(), dealerId, userId, loggedInUser);
        handleBasicUserAssociation(dealerId, userId, permissions, ContactType.of(form.getContactType()));
    }

    private void handleSalesPersonAssociation(Boolean salesPerson, String dealerId, String userId, Optional<UserView> loggedInUser) {
        if (!splitFeatureFlags.isDealerSalesPersonEnabled(dealerId, loggedInUser)) {
            return;
        }

        if (Boolean.TRUE.equals(salesPerson)) {
            log.info("Adding sales person {} to dealer {}", userId, dealerId);
            userServiceClient.assignSalesPerson(userId, dealerId);
        } else {
            log.info("Removing sales person {} from dealer {}", userId, dealerId);
            userServiceClient.unassignSalesPerson(userId, dealerId);
        }
    }

    public void handleProgramManagerAssociation(Boolean programManager, StockType pmStockType, String dealerId, String userId) {
        // handle existing pm role association
        Optional<ProgramManagerRoleAssociation> opt = programManagerClient.getProgramManager(dealerId, userId);
        if(opt.isPresent()) {
            ProgramManagerRoleAssociation pm = opt.get();

            if(Boolean.TRUE.equals(programManager)) {
                StockTypeUpdateRequest stockTypeUpdateRequest = new StockTypeUpdateRequest(pm.getId(), pmStockType);
                programManagerClient.updateProgramManagerStockType(pm.getId(), stockTypeUpdateRequest);
            } else {
                programManagerClient.removeProgramManager(pm.getId());
            }
        }
        // handle new association
        if(opt.isEmpty() && Boolean.TRUE.equals(programManager)) {
            log.info("Adding program manager {} to dealer {}", userId, dealerId);
            ProgramManagerRoleAssociation programManagerRoleAssociation = new ProgramManagerRoleAssociation();
            programManagerRoleAssociation.setDealerId(dealerId);
            programManagerRoleAssociation.setUserId(userId);
            programManagerRoleAssociation.setStockType(pmStockType);
            programManagerClient.addProgramManager(programManagerRoleAssociation);
        }
    }

    public void handleFollowupManagerAssociation(Boolean followupManager, StockType fmStockType, String dealerId, String userId) {
        // handle existing fm role association
        Optional<FollowUpManagerRoleAssociation> opt = followUpManagerClient.getFollowUpManager(dealerId, userId);
        if(opt.isPresent()) {
            FollowUpManagerRoleAssociation fm = opt.get();

            if(Boolean.TRUE.equals(followupManager)) {
                StockTypeUpdateRequest stockTypeUpdateRequest = new StockTypeUpdateRequest(fm.getId(), fmStockType);
                followUpManagerClient.updateFollowUpManagerStockType(fm.getId(), stockTypeUpdateRequest);
            } else {
                followUpManagerClient.removeFollowUpManager(fm.getId());
            }
        }
        // handle new association
        if(opt.isEmpty() && Boolean.TRUE.equals(followupManager)) {
            log.info("Adding followup manager {} to dealer {}", userId, dealerId);
            FollowUpManagerRoleAssociation followUpManagerRoleAssociation = new FollowUpManagerRoleAssociation();
            followUpManagerRoleAssociation.setDealerId(dealerId);
            followUpManagerRoleAssociation.setUserId(userId);
            followUpManagerRoleAssociation.setStockType(fmStockType);
            followUpManagerClient.addFollowUpManager(followUpManagerRoleAssociation);
        }
    }

    public void handleSalesManagerAssociation(Boolean salesManager, StockType smStockType, String dealerId, String userId) {
        // handle existing fm role association
        Optional<SalesManagerRoleAssociation> opt = salesManagerClient.getProgramManager(dealerId, userId);
        if(opt.isPresent()) {
            SalesManagerRoleAssociation sm = opt.get();

            if(Boolean.TRUE.equals(salesManager)) {
                StockTypeUpdateRequest stockTypeUpdateRequest = new StockTypeUpdateRequest(sm.getId(), smStockType);
                salesManagerClient.updateProgramManagerStockType(sm.getId(), stockTypeUpdateRequest);
            } else {
                salesManagerClient.removeProgramManager(sm.getId());
            }
        }
        // handle new association
        if(opt.isEmpty() && Boolean.TRUE.equals(salesManager)) {
            log.info("Adding followup manager {} to dealer {}", userId, dealerId);
            SalesManagerRoleAssociation roleAssociation = new SalesManagerRoleAssociation();
            roleAssociation.setDealerId(dealerId);
            roleAssociation.setUserId(userId);
            roleAssociation.setStockType(smStockType);
            salesManagerClient.addProgramManager(roleAssociation);
        }
    }

    public void handleBasicUserAssociation(String dealerId, String userId, List<Integer> permissions, ContactType contactType) {
        // handle existing basic user role association
        Optional<BasicDealerUserRoleAssociation> opt = basicUserAssociationClient.getBasicUserAssociation(dealerId, userId);
        if(opt.isPresent()) {
            BasicDealerUserRoleAssociation bu = opt.get();
            bu.setPermissions(permissions);
            bu.setContactType(contactType);
            basicUserAssociationClient.replaceUserPermissions(bu.getId(), bu);
        }
        // handle new association
        if(opt.isEmpty()) {
            log.info("Adding user {} to dealer {}", userId, dealerId);
            BasicDealerUserRoleAssociation basicDealerUserRoleAssociation = new BasicDealerUserRoleAssociation();
            basicDealerUserRoleAssociation.setDealerId(dealerId);
            basicDealerUserRoleAssociation.setUserId(userId);
            basicDealerUserRoleAssociation.setPermissions(permissions);
            basicDealerUserRoleAssociation.setContactType(contactType);

            basicUserAssociationClient.addUserPermissions(basicDealerUserRoleAssociation);
        }
    }

    public DealerUser convert(String dealerId, Optional<UserView> dealerUserOpt) {
        if(dealerUserOpt.isEmpty()) {
            return null;
        }

        UserView user = dealerUserOpt.get();

        BasicDealerUserRoleAssociation basicUserRole = basicUserAssociationClient.getBasicUserAssociation(dealerId, user.getId()).orElse(null);
        ProgramManagerRoleAssociation programManagerRole = programManagerClient.getProgramManager(dealerId, user.getId()).orElse(null);
        FollowUpManagerRoleAssociation followUpManagerRole = followUpManagerClient.getFollowUpManager(dealerId, user.getId()).orElse(null);
        SalesManagerRoleAssociation salesManagerRole = salesManagerClient.getProgramManager(dealerId, user.getId()).orElse(null);

        final DealerSalesPerson dealerSalesPerson;
        if(splitFeatureFlags.isDealerSalesPersonEnabled(dealerId, SessionUtils.getLoggedInUser())) {
            dealerSalesPerson = userServiceClient.getSalesPerson(dealerId, user.getId()).orElse(null);
        } else {
            dealerSalesPerson = null;
        }

        List<DealerPermissionView> permissions = Optional.ofNullable(basicUserRole).map(BasicDealerUserRoleAssociation::getPermissionList).orElseGet(Collections::emptyList)
                .stream()
                .filter(getPermissionFilter()) //TODO - remove when permissions migration is complete
                .collect(Collectors.toList());

        return DealerUser.from(user, basicUserRole, programManagerRole, followUpManagerRole, salesManagerRole, dealerSalesPerson, permissions);
    }

    public Optional<DealerUser> createUser(String dealerId, UserForm userForm, String host, String tenantId){
        Optional<UserView> userOpt = mapToNewDealerUser(userForm, host,tenantId);
        if(userOpt.isEmpty()){
            return Optional.empty();
        }
        Optional<UserView> savedUser = Optional.ofNullable(userClient.save(userOpt.get()));
        if (savedUser.isEmpty()){
            return Optional.empty();
        }

        handleBasicUserAssociation(dealerId, savedUser.get().getId(), null, null);
        var dealerUser = convert(dealerId, savedUser);
        return Optional.ofNullable(dealerUser);
    }

    private Optional<UserView> mapToNewDealerUser(UserForm userForm, String host, String tenantId) {
        if(userForm == null) {
            return Optional.empty();
        }

        UserView user = new UserView();
        BeanUtils.copyProperties(userForm, user);

        user.setTenantId(tenantId);
        user.setType(UserView.TYPE_DEALER);
        user.setPassword(DigestUtils.md5Hex(RandomStringUtils.random(10)));
        user.setInternalSms(true);
        user.setSmsAllowed(true);

        Source source = new Source();
        source.setHostname(host);
        user.setSource(source);

        return Optional.of(user);
    }
}


