package com.carsaver.partner.service.desking.standard.paas;

import com.carsaver.magellan.api.externaloffers.model.ExternalOfferLineItem;
import com.carsaver.magellan.api.externaloffers.model.ExternalOffers;
import com.carsaver.magellan.api.externaloffers.model.ais.AisExternalOffers;
import com.carsaver.magellan.api.externaloffers.model.ais.LoyaltyOffer;
import com.carsaver.magellan.model.ProgramRebates;
import com.carsaver.magellan.model.ProgramView;
import com.carsaver.magellan.model.ProgramWithCashView;
import com.carsaver.magellan.model.dealer.DealType;
import com.carsaver.partner.model.paas.PaasResponse;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class PaasUtil {

    public static List<com.carsaver.magellan.model.ProgramRebates> convertProgramRebates(List<PaasResponse.ProgramRebates> programRebatesList) {
        return Optional.ofNullable(programRebatesList)
            .map(i -> i.stream()
                .map(p -> new com.carsaver.magellan.model.ProgramRebates(p.getProgramId(), p.getDescription(), p.getValue(), p.getIsConditional()))
                .collect(Collectors.toList())
            ).orElse(Collections.EMPTY_LIST);
    }

    public static Double getTotalConditionalExternalOffersQuotes(List<ProgramRebates> programRebatesList) {
        Double total = Optional.ofNullable(programRebatesList)
            .map(i -> i.stream()
                .filter(d -> Boolean.TRUE.equals(d.getIsConditional()))
                .map(ProgramRebates::getValue)
                .mapToDouble(BigDecimal::doubleValue)
                .sum()
            ).orElse(0D);
        return total;
    }

    /**
     * Paas Upgrade data type that is coming back from service response
     */
    public static ExternalOffers getConditionalExternalOffersQuotes(List<ProgramRebates> programRebatesList, DealType dealType) {
        ExternalOffers result = null;
        List<ExternalOfferLineItem> rebatesList = Optional.ofNullable(programRebatesList)
            .map(i -> i.stream()
                .filter(d -> Boolean.TRUE.equals(d.getIsConditional()) && d.getValue() != null && d.getProgramId() != null)
                .map(j -> getLoyaltyOffer(j, dealType))
                .collect(Collectors.toList()))
            .orElse(Collections.EMPTY_LIST);

        if (!rebatesList.isEmpty()) {
            AisExternalOffers externalOffers = new AisExternalOffers();
            externalOffers.setLineItems(rebatesList);
            result = externalOffers;
        }

        return result;
    }

    private static LoyaltyOffer getLoyaltyOffer(ProgramRebates j, DealType dealType) {
        LoyaltyOffer offer = new LoyaltyOffer(j.getDescription(), dealType);
        ProgramView program = new ProgramView();
        program.setId(Integer.valueOf(j.getProgramId()));
        program.setTitle(j.getDescription());
        offer.addLoyalty(new ProgramWithCashView(program, j.getValue().intValue()));
        return offer;
    }

}
