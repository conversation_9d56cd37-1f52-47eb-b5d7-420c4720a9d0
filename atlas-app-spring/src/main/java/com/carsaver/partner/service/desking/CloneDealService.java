package com.carsaver.partner.service.desking;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import com.carsaver.accessories.api.DealAccessories;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.PaasData;
import com.carsaver.magellan.model.PaasPayments;
import com.carsaver.magellan.model.ProgramRebates;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.certificate.DealPreferences;
import com.carsaver.magellan.model.pricing.PricesView;
import com.carsaver.magellan.model.vehicle.UserVehicleQuoteView;
import com.carsaver.partner.model.LeaseDealModel;
import com.carsaver.partner.model.desking.standard.LeaseQuoteResponse;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;
import com.carsaver.magellan.NotFoundException;
import com.carsaver.magellan.client.EvoxImageClient;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.QuoteView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.FinanceConfig;
import com.carsaver.magellan.model.chrome.StyleView;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.deal.DealSheet.DealerFeeItem;
import com.carsaver.magellan.model.deal.DealSheet.RebateLineItem;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import com.carsaver.magellan.model.evox.v2.EvoxImageResult;
import com.carsaver.magellan.model.financiers.FinancierConfig;
import com.carsaver.magellan.model.financiers.PaymentConfig;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.exception.DealNotFoundException;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.CloneDealRequest.AddOns;
import com.carsaver.partner.model.desking.CloneDealRequest.CapCostReduction;
import com.carsaver.partner.model.desking.CloneDealRequest.DueAtSigning;
import com.carsaver.partner.model.desking.CloneDealRequest.FinanceDetails;
import com.carsaver.partner.model.desking.CloneDealRequest.GrossCapitalCost;
import com.carsaver.partner.model.desking.CloneDealRequest.Inceptions;
import com.carsaver.partner.model.desking.CloneDealRequest.LineItem;
import com.carsaver.partner.model.desking.CloneDealRequest.MonthlyPayment;
import com.carsaver.partner.model.desking.CloneDealRequest.Rebates;
import com.carsaver.partner.model.desking.CloneDealRequest.TaxesAndFees;
import com.carsaver.partner.model.desking.CloneDealRequest.TradeAllowance;
import com.carsaver.partner.model.desking.CloneDealRequest.VehicleDetail;
import com.carsaver.partner.model.desking.ClonedDealResponse;
import com.carsaver.partner.model.desking.VehicleUpdateModel;
import com.carsaver.partner.model.desking.standard.LeaseQuoteResponse.Quote;
import com.carsaver.partner.model.desking.standard.LeaseStepBreakDownDTO;
import com.carsaver.partner.service.DealerFeesService;

import lombok.extern.slf4j.Slf4j;

/**
 * CloneDealService
 * <p>
 * Service responsible for the conversion of a garage deal to a clone deal.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CloneDealService {
    private static final String STOCK_NUMBER = "stockNumber";
    private static final String VIN = "vin";
    private static final String DEALER_ID = "dealerId";
    private static final String DESKING = "DESKING";
    public static final String STANDARD_EDITING = "Standard";
    public static final String CASH = "Cash";
    public static final String LEASE = "Lease";
    public static final String INCEPTION_FEE_TAX = "Inception Fee Tax";
    public static final String TOTAL_CAPT_COST_REDUCTION_TAX = "Total Capt Cost Reduction Tax";

    private final NissanWebClient nissanWebClient;

    private final FinancierClient financierClient;

    private final DealerFeesService dealerFeesService;

    private final EvoxImageClient evoxImageClient;

    @Value("${deal-desking.inventory-service.api-uri}")
    private String inventoryServiceUrl;

    @Value("${dealer-service.api-uri}")
    private String dealerServiceUrl;

    private final UserClient userClient;
    private final SplitFeatureFlags splitFeatureFlags;
    private final CloneDealClient client;
    private Environment environment;

    // constructor
    public CloneDealService(NissanWebClient nissanWebClient, FinancierClient financierClient, DealerFeesService dealerFeesService,
                            EvoxImageClient evoxImageClient, UserClient userClient, SplitFeatureFlags splitFeatureFlags, CloneDealClient client,
                            Environment environment) {
        this.nissanWebClient = nissanWebClient;
        this.financierClient = financierClient;
        this.dealerFeesService = dealerFeesService;
        this.evoxImageClient = evoxImageClient;
        this.userClient = userClient;
        this.splitFeatureFlags = splitFeatureFlags;
        this.client = client;
        this.environment = environment;
    }

    /**
     * Retrieves a cloned deal by id.
     *
     * @param id
     * @return ClonedDealResponse
     */
    public ClonedDealResponse retrieveClonedDealById(Long id) {
        String url = dealerServiceUrl + "/clone/by-id/" + id;
        ClonedDealResponse response;
        try {
            response = nissanWebClient.get(url, ClonedDealResponse.class, DESKING);
        } catch (Exception e) {
            log.error("Exception: context {} message {}", DESKING, e.getMessage());
            throw new DealNotFoundException("Cloned Deal Not Found for id " + id);
        }
        return response;
    }

    /**
     * Retrieves a cloned deal by certificate id.
     *
     * @return ClonedDealResponse
     */
    public ClonedDealResponse retrieveClonedDealByCertificateId(Integer certificateId) {
        String url = dealerServiceUrl + "/clone/certificate/" + certificateId;
        ClonedDealResponse response = null;
        try {
            response = nissanWebClient.get(url, ClonedDealResponse.class, DESKING);
        } catch (Exception e) {
            log.warn("No clone deal found for dealer generated deal {}: context {} message {}", certificateId, DESKING, e.getMessage());
        }
        return response;
    }

    /**
     * Clones a deal into a clone deal request and saves it.
     * Used for finance create deal and copy function for both lease and finance
     * @param dealer
     * @param deal
     * @param originalDealId
     */
    public ClonedDealResponse cloneDeal(DealerView dealer, CertificateView deal, Integer originalDealId, String editType) {

        // will be null if it is a new deal being created
        Long originalCertificateId = Optional.ofNullable(originalDealId).map(Integer::longValue).orElse(null);

        CloneDealRequest request;
        if (isCashDeal(deal)) {
            request = createCashCloneDealRequest(dealer, deal, originalCertificateId, editType);
        } else {
            // clone the garage deal into a clone deal request
            request = createCloneDealRequest(dealer, deal, originalCertificateId, editType);
        }

        // save the cloned deal
        ClonedDealResponse response = saveCloneDeal(request);

        return response;
    }

    /**
     * Clones a deal into a clone deal request and saves it.
     * Comes from dealer creating a new lease deal only
     * @param dealer
     * @param model
     */
    public ClonedDealResponse cloneLeaseDeal(DealerView dealer, CertificateView deal, LeaseDealModel model) {

        // clone the garage deal into a clone deal request
        CloneDealRequest request = createLeaseCloneDealRequest(dealer, deal, model);

        // save the cloned deal
        ClonedDealResponse response = saveCloneDeal(request);

        return response;
    }

   /**
    * Creates the clone deal structure.
    * Lease only for both OL calculations and Paas
    * @param dealer
    * @param deal
    * @param model
    * @return CloneDealRequest
    */
   CloneDealRequest createLeaseCloneDealRequest(DealerView dealer, CertificateView deal, LeaseDealModel model) {
       Optional<QuoteView> quote = getQuoteView(model);
       Optional<DealJacket> dealJacketOpt = Optional.ofNullable(model.getDealJacket());
       Optional<DealSheet> dealSheet = Optional.ofNullable(deal.getDealSheet());
       Optional<VehicleView> vehicle = Optional.ofNullable(deal.getVehicle());
       Optional<UserVehicleView> userVehicle = Optional.ofNullable(deal.getTradeVehicle());
       Optional<UserVehicleQuoteView> userVehicleQuote = Optional.ofNullable(deal.getTradeVehicleQuote());

       // clone the garage deal into a clone deal request. Deal will always be standard edit since Quote is a response from lease lowest quote
       CloneDealRequest request = buildCloneRequestBasicInfo(dealer, deal, null, STANDARD_EDITING, LEASE);

       // addOns
       AddOns addOns = getAddOns(Optional.empty(), null);
       request.setAddons(addOns);
       request.setAddonsTotal(getAddonsTotal(addOns));

       // dueAtSigning
       DueAtSigning dueAtSigning = getDueAtSigning(dealSheet, quote, userVehicleQuote, userVehicle);
       request.setDueAtSigning(dueAtSigning);
       request.setDueAtSigningTotal(getDueAtSigningTotal(dueAtSigning));

       // financeDetails
       FinanceDetails financeDetails = getFinanceDetails(quote);
       request.setFinanceDetails(financeDetails);

       // vehicleDetail
       VehicleDetail vehicleDetail = getVehicleDetail(dealSheet, vehicle);
       request.setVehicleDetail(vehicleDetail);

       // grossCapitalCost
       GrossCapitalCost grossCapitalCost = getGrossCapitalCost(deal, quote, dealSheet, vehicle, userVehicleQuote, userVehicle, Optional.empty(), null);
       request.setGrossCapitalCost(grossCapitalCost);
       request.setGrossCapitalCostTotal(getGrossCapitalCostTotal(Optional.of(grossCapitalCost)));

       Inceptions inceptions;
       MonthlyPayment monthlyPayment;

       if (dealJacketOpt.isPresent()) {
           Optional<PaasPayments> payment = dealJacketOpt.map(DealJacket::getPaasPayments);
           inceptions = getInceptionsPaas(deal, payment);
           monthlyPayment = getMonthlyPaymentPaas(payment);
       } else {
            LeaseQuoteResponse.Quote leaseQuote = model.getQuote();
            inceptions = getInceptions(deal, leaseQuote);
            monthlyPayment = getMonthlyPayment(leaseQuote);
       }

       // inceptions
       request.setInceptions(inceptions);
       Double totalInception = getInceptionsTotal(Optional.of(inceptions));
       request.setInceptionsTotal(totalInception);

       // monthlyPayment
       request.setMonthlyPayment(monthlyPayment);
       request.setMonthlyPaymentTotal(getMonthlyPaymentTotal(Optional.of(monthlyPayment)));

       // rebates
       Rebates rebates = getRebates(dealSheet, quote);
       request.setRebates(rebates);
       Double totalRebates = getRebatesTotal(rebates);
       request.setRebatesTotal(totalRebates);

       // capCostReduction
       CapCostReduction capCostReduction = getCapCostReduction(dueAtSigning, totalInception, totalRebates);
       Double totalCapCost  = capCostReduction.getCapCostReduction();
       capCostReduction.setCapCostReduction(null);
       request.setCapCostReduction(capCostReduction);
       request.setCapCostReductionTotal(totalCapCost);

       // tradeAllowances
       TradeAllowance tradeAllowance = getTradeAllowance(dealSheet, userVehicle, userVehicleQuote);
       request.setTradeAllowance(tradeAllowance);
       request.setTradeAllowanceTotal(getTradeAllowanceTotal(tradeAllowance));

       // taxesAndFees
       TaxesAndFees taxesAndFees = getTaxesAndFees();
       request.setTaxesAndFees(taxesAndFees);

       // return the cloned deal request
       return request;
   }

    @NotNull
    private static Optional<QuoteView> getQuoteView(LeaseDealModel model) {
       Optional<QuoteView> quoteView;
       if (model.getDealJacket() != null) {
            quoteView = Optional.of(model.getDealJacket()).map(DealJacket::getQuote);
       } else {
            quoteView = Optional.ofNullable(model.getQuote()).map(Quote::getQuoteView);
       }

        return quoteView;
    }

    /**
     * Creates the clone deal structure.
     * Used for finance create deal and copy function for both lease and finance
     * @param dealer
     * @param deal
     * @param originalDealId
     * @return CloneDealRequest
     */
    CloneDealRequest createCloneDealRequest(DealerView dealer, CertificateView deal, Long originalDealId, String editDeal) {
        Optional<QuoteView> quote = Optional.ofNullable(deal.getQuote());
        Optional<QuoteView> baseQuote = Optional.ofNullable(deal.getBaseQuote());
        Optional<DealSheet> dealSheet = Optional.ofNullable(deal.getDealSheet());
        Optional<VehicleView> vehicle = Optional.ofNullable(deal.getVehicle());
        Optional<UserVehicleView> userVehicle = Optional.ofNullable(deal.getTradeVehicle());
        Optional<UserVehicleQuoteView> userVehicleQuote = Optional.ofNullable(deal.getTradeVehicleQuote());
        Optional<DealPreferences> dealPreferences = Optional.ofNullable(deal.getDealPreferences());

        Optional<DealAccessories> accessories = client.getDealAccessories(originalDealId);
        String programId = Optional.ofNullable(deal.getCampaign()).map(CampaignView::getProgramId).orElse(null);
        ProtectionProductResponse protectionProducts = client.getProtectionProduct(originalDealId, dealer.getId(), findDealersRouteOneId(dealer), programId);

        String dealType = quote.map(q-> q.financeOrLease()).orElse("");

        // clone the garage deal into a clone deal request
        CloneDealRequest request = buildCloneRequestBasicInfo(dealer, deal, originalDealId, editDeal, dealType);

        // addOns
        AddOns addOns = getAddOns(accessories, protectionProducts);
        request.setAddons(addOns);
        Double totalAddOns = getAddonsTotal(addOns);
        request.setAddonsTotal(totalAddOns);

        // dueAtSigning
        DueAtSigning dueAtSigning = getDueAtSigning(dealSheet, dealPreferences, dealType, userVehicle, userVehicleQuote);
        request.setDueAtSigning(dueAtSigning);
        Double totalDueAtSigning = getTotalDueAtSigning(dueAtSigning);
        request.setDueAtSigningTotal(totalDueAtSigning);

        // vehicleDetail
        VehicleDetail vehicleDetail = getVehicleDetail(dealSheet, vehicle);
        request.setVehicleDetail(vehicleDetail);

        // financeDetails
        FinanceDetails financeDetails = getFinanceDetails(quote);
        request.setFinanceDetails(financeDetails);

        // grossCapitalCost
        GrossCapitalCost grossCapitalCost = getGrossCapitalCost(deal, quote, dealSheet, vehicle, userVehicleQuote, userVehicle, accessories, protectionProducts);
        request.setGrossCapitalCost(grossCapitalCost);
        Double totalGrossCapitalCost = getGrossCapitalCostTotal(Optional.of(grossCapitalCost));
        request.setGrossCapitalCostTotal(totalGrossCapitalCost);

        // monthlyPayment
        MonthlyPayment monthlyPayment = getMonthlyPayment(quote, baseQuote);
        request.setMonthlyPayment(monthlyPayment);
        Double totalMonthlyPaymentTotal = getMonthlyPaymentTotal(monthlyPayment, request.getDealType());
        request.setMonthlyPaymentTotal(totalMonthlyPaymentTotal);

        // inceptions
        Inceptions inceptions = getInceptions(deal, quote, monthlyPayment);
        request.setInceptions(inceptions);
        Double totalInceptions = getTotalInception(inceptions,monthlyPayment);
        request.setInceptionsTotal(totalInceptions);

        // rebates
        Rebates rebates = getRebates(dealSheet, quote);
        request.setRebates(rebates);
        Double totalRebates = getRebatesTotal(rebates);
        request.setRebatesTotal(totalRebates);

        // tradeAllowances
        TradeAllowance tradeAllowances = getTradeAllowance(dealSheet, userVehicle, userVehicleQuote);
        request.setTradeAllowance(tradeAllowances);
        Double totalTradeAllowances = getTotalTradeAllowances(tradeAllowances);
        request.setTradeAllowanceTotal(totalTradeAllowances);

        // taxesAndFees
        TaxesAndFees taxesAndFees = getTaxesAndFees(dealSheet, quote);
        request.setTaxesAndFees(taxesAndFees);
        Double totalTaxesAndFees = getTotalTaxesAndFees(taxesAndFees);
        request.setTaxesAndFeesTotal(totalTaxesAndFees);

        // capCostReduction
        CapCostReduction capCostReduction = getCapCostReduction(dueAtSigning, totalInceptions, totalRebates);
        Double totalCapCostReduction = capCostReduction.getCapCostReduction();
        capCostReduction.setCapCostReduction(null);
        request.setCapCostReduction(capCostReduction);
        request.setCapCostReductionTotal(totalCapCostReduction);

        // return the cloned deal request
        return request;
    }

    /**
     * Creates the clone deal structure for cash deals
     *
     * @param dealer
     * @param deal
     * @param originalDealId
     * @return CloneDealRequest
     */
    CloneDealRequest createCashCloneDealRequest(DealerView dealer, CertificateView deal, Long originalDealId, String editDeal) {

        Optional<DealSheet> dealSheet = Optional.ofNullable(deal.getDealSheet());
        Optional<VehicleView> vehicle = Optional.ofNullable(deal.getVehicle());
        Optional<UserVehicleView> userVehicle = Optional.ofNullable(deal.getTradeVehicle());
        Optional<UserVehicleQuoteView> userVehicleQuote = Optional.ofNullable(deal.getTradeVehicleQuote());

        Optional<DealAccessories> accessories = client.getDealAccessories(originalDealId);
        String programId = Optional.ofNullable(deal.getCampaign()).map(CampaignView::getProgramId).orElse(null);
        ProtectionProductResponse protectionProducts = client.getProtectionProduct(originalDealId, dealer.getId(), findDealersRouteOneId(dealer), programId);

        // clone the garage deal into a clone deal request
        CloneDealRequest request = buildCloneRequestBasicInfo(dealer, deal, originalDealId, editDeal, CASH);

        // vehicleDetail
        VehicleDetail vehicleDetail = getVehicleDetail(dealSheet, vehicle);
        request.setVehicleDetail(vehicleDetail);

        // rebates
        Rebates rebates = getCashRebates(dealSheet);
        request.setRebates(rebates);
        Double totalRebates = getRebatesTotal(rebates);
        request.setRebatesTotal(totalRebates);

        // taxesAndFees
        TaxesAndFees taxesAndFees = getCashTaxesAndFees(deal);
        request.setTaxesAndFees(taxesAndFees);
        Double totalTaxesAndFees = getTotalTaxesAndFees(taxesAndFees);
        request.setTaxesAndFeesTotal(totalTaxesAndFees);

        // tradeAllowances
        TradeAllowance tradeAllowances = getTradeAllowance(dealSheet, userVehicle, userVehicleQuote);
        request.setTradeAllowance(tradeAllowances);
        Double totalTradeAllowances = getTotalTradeAllowances(tradeAllowances);
        request.setTradeAllowanceTotal(totalTradeAllowances);

        // addOns
        AddOns addOns = getAddOns(accessories, protectionProducts);
        request.setAddons(addOns);
        Double totalAddOns = getAddonsTotal(addOns);
        request.setAddonsTotal(totalAddOns);

        // initialized the rest of the columns that will not be used but can throw a null pointer if not stored
        request.setFinanceDetails(CloneDealRequest.FinanceDetails.builder().build());
        request.setMonthlyPayment(CloneDealRequest.MonthlyPayment.builder().build());
        request.setDueAtSigning(CloneDealRequest.DueAtSigning.builder().build());
        request.setInceptions(CloneDealRequest.Inceptions.builder().build());
        request.setGrossCapitalCost(CloneDealRequest.GrossCapitalCost.builder().build());
        request.setCapCostReduction(CloneDealRequest.CapCostReduction.builder().build());

        // return the cloned deal request
        return request;
    }

    @NotNull
    private static CloneDealRequest buildCloneRequestBasicInfo(DealerView dealer, CertificateView deal, Long originalDealId, String editDeal, String dealType) {
        CloneDealRequest request = new CloneDealRequest();
        request.setId(null);
        request.setUserId(deal.getUserId());
        request.setDealerId(dealer.getId());
        request.setCertificateId(deal.getId());
        request.setDealName(deal.getDealName());
        request.setDealType(dealType);
        request.setEditType(editDeal);
        request.setOriginalCertificateId(originalDealId);
        return request;
    }

    private static boolean isCashDeal(CertificateView deal) {
        boolean isCashDeal = Optional.ofNullable(deal.getDealPreferences())
            .map(DealPreferences::getDealType)
            .map(i -> i.toString().equalsIgnoreCase(CASH))
            .orElse(false);

        return isCashDeal;
    }

    /**
     * Populates the AddOns.
     *
     * @return AddOns
     * @note This is intentionally left blank
     */
    AddOns getAddOns(Optional<DealAccessories> accessories,  ProtectionProductResponse protectionProducts) {
        AddOns addOns = new AddOns();
        addOns.setLineItems(List.of());

        List<LineItem> list = determindAccessoriesList(accessories);
        addOns.setAccessories(list);

        List<LineItem> list2 = determindProtectionProtectionList(protectionProducts);
        addOns.setProtectionProduct(list2);

        return addOns;
    }

    private static List<LineItem> determindAccessoriesList(Optional<DealAccessories> accessories) {

        if (accessories.isEmpty()) {
            return List.of();
        }

        List<LineItem> list = accessories.get().getAccessories().stream()
            .map(i -> {
                BigDecimal labor  = i.getLaborPrice() != null ? i.getLaborPrice() : BigDecimal.ZERO;
                return LineItem.builder()
                    .name(i.getName())
                    .amount(i.getPartsPrice().add(labor).doubleValue()).build();
            })
            .collect(Collectors.toList());

        return list;
    }


    private static List<LineItem> determindProtectionProtectionList(ProtectionProductResponse protectionProducts) {

        if (protectionProducts == null || CollectionUtils.isEmpty(protectionProducts.getWarranties())) {
            return List.of();
        }

        List<LineItem> list = protectionProducts.getWarranties().stream()
            .map(i -> LineItem.builder()
                    .name(i.getProductName())
                    .amount(i.getPremium())
                    .taxable(i.getTaxableIndicator())
                    .build())
            .collect(Collectors.toList());

        return list;
    }


    /**
     * Returns the total for all addons.
     *
     * @param addOns
     * @return double
     */
    Double getAddonsTotal(AddOns addOns) {
        return addOns.getLineItems().stream().mapToDouble(LineItem::getAmount).sum()
            + addOns.getAccessories().stream().mapToDouble(LineItem::getAmount).sum()
            + addOns.getProtectionProduct().stream().mapToDouble(LineItem::getAmount).sum()
            ;
    }

    /**
     * Populates the CapCostReduction.
     *
     * @return CapCostReduction
     * @note This is intentionally left blank
     */
    CapCostReduction getCapCostReduction(DueAtSigning dueAtSigning, Double totalInception, Double rebatesTotal) {
        CapCostReduction capCostReduction = new CapCostReduction();
        Double consumerCash = Optional.ofNullable(dueAtSigning).map(DueAtSigning::getConsumerCash).map(i -> Double.parseDouble(i)).orElse(0D);
        Double tradeValue = Optional.ofNullable(dueAtSigning).map(DueAtSigning::getPositiveTradeEquity).orElse(0D);

        List<LineItem> lineItemList = new ArrayList<>();

        Double totalConsumerCash = consumerCash + tradeValue;
        double consumerCashAsCapitalReductionValue = 0D;
        if (totalInception.compareTo(totalConsumerCash) < 0) {
            consumerCashAsCapitalReductionValue = totalConsumerCash - totalInception;
        }

        lineItemList.add(LineItem.builder().name("Consumer Cash (including trade)").amount(totalConsumerCash).build());
        lineItemList.add(LineItem.builder().name("Total Inception Fees").amount(totalInception).build());
        lineItemList.add(LineItem.builder().name("Consumer Cash as Capital Reduction").amount(consumerCashAsCapitalReductionValue).build());
        lineItemList.add(LineItem.builder().name("Rebates").amount(rebatesTotal).build());

        capCostReduction.setLineItems(lineItemList);
        capCostReduction.setCapCostReduction(consumerCashAsCapitalReductionValue + rebatesTotal);
        return capCostReduction;
    }

    /**
     * Populates the DueAtSigning for lease
     *
     * @param dealSheet
     * @return DueAtSigning
     */
    DueAtSigning getDueAtSigning(Optional<DealSheet> dealSheet, Optional<QuoteView> quoteView, Optional<UserVehicleQuoteView> userVehicleQuoteView, Optional<UserVehicleView> userVehicleView) {
        DueAtSigning dueAtSigning = new DueAtSigning();
        dueAtSigning.setConsumerCash(quoteView.map(d -> d.getDownPayment()).map(String::valueOf).orElse("0.0"));
        dueAtSigning.setLineItems(List.of());
        dueAtSigning.setPositiveTradeEquity(determinePositiveTradeEquity(dealSheet, userVehicleQuoteView, userVehicleView));
        return dueAtSigning;
    }

    /**
     * Populates the DueAtSigning for finance
     *
     * @param dealSheet
     * @return DueAtSigning
     */
    DueAtSigning getDueAtSigning(Optional<DealSheet> dealSheet, Optional<DealPreferences> dealPreferences, String dealType,
                                 Optional<UserVehicleView> userVehicle, Optional<UserVehicleQuoteView> userVehicleQuote) {
        DueAtSigning dueAtSigning = new DueAtSigning();
        dueAtSigning.setLineItems(List.of());

        // get consumer cash
        Double consumerCash = dealSheet.map(d -> d.getCashDownPayment()).orElse(getConsumerCash(dealPreferences, dealType));
        dueAtSigning.setConsumerCash(consumerCash.toString());
        //get positive trade
        dueAtSigning.setPositiveTradeEquity(determinePositiveTradeEquity(dealSheet, userVehicleQuote, userVehicle));
        return dueAtSigning;
    }

    Double getConsumerCash(Optional<DealPreferences> dealPreferences, String dealType) {
        switch (dealType) {
            case "Finance" : return dealPreferences.map(DealPreferences::getFinancePreferences).map(DealPreferences.FinancePreferences::getDownPayment).map(Integer::doubleValue)
                .orElse(0.0);
            case "Lease" :return dealPreferences.map(DealPreferences::getLeasePreferences).map(DealPreferences.LeasePreferences::getDownPayment).map(Integer::doubleValue)
                .orElse(0.0);
        }

        return 0.0;
    }

    /**
     * Returns the total due at signing.
     *
     * @param dueAtSigning
     * @return Double
     */
    Double getDueAtSigningTotal(DueAtSigning dueAtSigning) {
        Double consumerCash = Double.valueOf(Optional.ofNullable(dueAtSigning.getConsumerCash()).orElse("0.0"));
        Double positiveTradeEquity = Optional.ofNullable(dueAtSigning.getPositiveTradeEquity()).orElse(0D);
        Double totalDueAtSigning = dueAtSigning.getLineItems().stream().mapToDouble(LineItem::getAmount).sum();
        Double result = consumerCash + positiveTradeEquity + totalDueAtSigning;
        return result;
    }

    /**
     * Populates the VehicleDetail.
     *
     * @param dealSheet
     * @param vehicle
     * @return VehicleDetail
     */
    VehicleDetail getVehicleDetail(Optional<DealSheet> dealSheet, Optional<VehicleView> vehicle) {
        VehicleDetail vehicleDetail = new VehicleDetail();
        vehicleDetail.setInvoice(vehicle.map(v -> v.getInvoicePrice()).map(Double::valueOf).orElse(0.0));
        vehicleDetail.setMsrp(vehicle.map(v -> v.getMsrp()).map(Double::valueOf).orElse(0.0));
        vehicleDetail.setSellingPrice(determineSellingPrice(dealSheet, vehicle));
        vehicleDetail.setStockNumber(vehicle.map(v -> v.getStockNumber()).orElse(null));
        vehicleDetail.setVehicleType(vehicle.map(v -> v.getStockType()).map(String::valueOf).orElse(null));
        vehicleDetail.setVin(vehicle.map(v -> v.getVin()).orElse(null));
        vehicleDetail.setInteriorColor(vehicle.map(v -> v.getInteriorColor()).orElse(null));
        vehicleDetail.setExteriorColor(vehicle.map(v -> v.getExteriorColor()).orElse(null));
        vehicleDetail.setMake(vehicle.map(v -> v.getMake()).orElse(null));
        vehicleDetail.setModel(vehicle.map(v -> v.getModel()).orElse(null));
        vehicleDetail.setYear(vehicle.map(v -> v.getYear()).orElse(null));
        vehicleDetail.setInventoryId(vehicle.map(VehicleView::getId).orElse(null));
        return vehicleDetail;
    }

    /**
     * Determines the positive trade equity.
     *
     * @param dealSheet
     * @return Double
     */
    Double determinePositiveTradeEquity(Optional<DealSheet> dealSheet, Optional<UserVehicleQuoteView> userVehicleQuoteView, Optional<UserVehicleView> userVehicleView) {

        // values stored in dealSheet-  upgrade-app version
        if (dealSheet.map(d -> d.getTradeValue()).isPresent()) {
            Double tradeValue = dealSheet.map(d -> d.getTradeValue()).orElse(0.0);
            Double tradePayoff = dealSheet.map(d -> d.getTradePayoff()).orElse(0.0);
            double tradeEquity = tradeValue + tradePayoff;
            Double result = tradeEquity > 0 ? tradeEquity : 0.0;
            return result;
        }

        // values stored in user vehicle table - digital-retail version
        if (userVehicleQuoteView.isPresent() && !userVehicleQuoteView.map(UserVehicleQuoteView::isExpired).orElse(true)) {
            Integer tradeValue = userVehicleQuoteView.map(UserVehicleQuoteView::getAdjustedTradeValue).orElse(0);
            Double balance = userVehicleView.map(UserVehicleView::getBalance).orElse(0.0);
            double equity = tradeValue - balance;
            Double result = equity > 0 ? equity : 0.0;
            return result;
        }

        return 0.0;
    }

    /**
     * Populates the FinanceDetails.
     *
     * @param quote
     * @return FinanceDetails
     */
    FinanceDetails getFinanceDetails(Optional<QuoteView> quote) {
        FinanceDetails financeDetails = new FinanceDetails();
        financeDetails.setAcquisitionFee(quote.map(QuoteView::getAcquisitionFee).orElse(0D));
        financeDetails.setBaseMoneyFactor(quote.map(q -> q.getMoneyFactor()).orElse(0.0));
        financeDetails.setFinanceCompany(determineFinanceCompany(quote));
        financeDetails.setLeaseTerminationFee(null);
        financeDetails.setMilesPerYear(quote.map(q -> q.getMileageAllowed()).orElse(0));
        financeDetails.setMoneyFactoryWithBps(quote.map(q -> q.getMoneyFactor()).map(String::valueOf).orElse(null));
        financeDetails.setResidualAmount(quote.map(q -> q.getResidual()).map(b -> b.doubleValue()).orElse(0.0));
        financeDetails.setResidualPercentage(null);
        financeDetails.setTerm(quote.map(q -> q.getTerm()).orElse(0));
        financeDetails.setTier(quote.map(q -> q.getBeacon()).map(String::valueOf).orElse(null));
        financeDetails.setApr(quote.map(q -> q.getInterestRate()).orElse(0.0));
        return financeDetails;
    }

    /**
     * Determines the acquisition fee. If the fee is not found, use $695.
     *
     * @param quote
     * @return Double
     */
    Double determineAcquisitionFee(Optional<QuoteView> quote, String userId, String dealerId, String campaignId) {
        Double result = 0D;
        if (splitFeatureFlags.isPaasIntegrationEnabled(userId,dealerId, campaignId)) {
            result = quote.map(QuoteView::getAcquisitionFee).orElse(0D);
        }

        return result;
    }


    /**
     * Determines the finance company.
     *
     * @param quote
     * @return String
     */
    String determineFinanceCompany(Optional<QuoteView> quote) {
        Optional<Integer> financierId = quote.map(q -> q.getFinancierId());
        Optional<FinancierView> financierOpt = financierId.isEmpty() ? Optional.empty() : financierClient.findById(financierId.get());
        String financeCompany = financierOpt.map(f -> f.getName()).orElse(null);
        return financeCompany;
    }

    /**
     * Populates the GrossCapitalCost.
     *
     * @param deal
     * @param quote
     * @param dealSheet
     * @param vehicle
     * @return GrossCapitalCost
     */
    GrossCapitalCost getGrossCapitalCost(CertificateView deal, Optional<QuoteView> quote, Optional<DealSheet> dealSheet, Optional<VehicleView> vehicle,
                                         Optional<UserVehicleQuoteView> userVehicleQuoteView, Optional<UserVehicleView> userVehicleView,
                                         Optional<DealAccessories> accessories, ProtectionProductResponse protectionProduct) {
        String campaign = Optional.ofNullable(deal.getSource()).map(Source::getCampaignId).orElse(null);

        GrossCapitalCost grossCapitalCost = new GrossCapitalCost();
        grossCapitalCost.setAcquisitionFee(String.valueOf(determineAcquisitionFee(quote, deal.getUserId(), deal.getDealerId(), campaign)));
        grossCapitalCost.setLineItems(determineGrossCapitalCostLineItems(deal));
        grossCapitalCost.setSellingPrice(determineSellingPrice(dealSheet, vehicle));
        grossCapitalCost.setTradeBalance(String.valueOf(determineNegativeTradeBalance(dealSheet, userVehicleQuoteView, userVehicleView)));
        grossCapitalCost.setAccessories(determindAccessoriesList(accessories));
        grossCapitalCost.setProtectionProduct(determindProtectionProtectionList(protectionProduct));
        return grossCapitalCost;
    }

    /**
     * Gets the total gross capital cost.
     *
     * @param grossCapitalCostOpt
     * @return Double
     */
    Double getGrossCapitalCostTotal(Optional<GrossCapitalCost> grossCapitalCostOpt) {
        Double totalGrossCapitalCost =
            grossCapitalCostOpt.map(GrossCapitalCost::getLineItems).map(i -> i.stream().mapToDouble(LineItem::getAmount).sum()).orElse(0D)
            + grossCapitalCostOpt.map(GrossCapitalCost::getAcquisitionFee).map(i -> Double.parseDouble(i)).orElse(0D)
            + grossCapitalCostOpt.map(GrossCapitalCost::getSellingPrice).orElse(0D)
            + grossCapitalCostOpt.map(GrossCapitalCost::getTradeBalance).map(i -> Double.parseDouble(i)).map(i -> i * -1).orElse(0D)
            + grossCapitalCostOpt.map(GrossCapitalCost::getAccessories).map(i -> i.stream().mapToDouble(LineItem::getAmount).sum()).orElse(0D)
            + grossCapitalCostOpt.map(GrossCapitalCost::getProtectionProduct).map(i -> i.stream().mapToDouble(LineItem::getAmount).sum()).orElse(0D);
        return totalGrossCapitalCost;
    }

    /**
     * Determines gross capital cost line items.
     *
     * @param deal
     * @return List<LineItem>
     */
    List<LineItem> determineGrossCapitalCostLineItems(CertificateView deal) {

        boolean hasValues = Optional.ofNullable(deal.getDealSheet()).map(DealSheet::getDealerFees).map(i -> !i.isEmpty()).orElse(false);
        if (deal.getSummaryDetails() != null && hasValues) {
            List<DealerFeeItem> list = deal.getDealSheet().getDealerFees();
            return list.stream()
                .filter(dealerFeeView -> Objects.nonNull(dealerFeeView.getIsInception()) && !dealerFeeView.getIsInception())
                .map(d -> new LineItem(d.getName(), d.getAmount(), d.getIsTaxable())).collect(Collectors.toList());
        }

        List<DealerFeeView> dealerFees = dealerFeesService.getLeaseDealerFeesByDealerId(deal.getDealerId(), deal.getStockType());
        List<DealerFeeView> not = dealerFeesService.getNotInceptionsFeesList(dealerFees, deal.getStockType());
        List<LineItem> lineItems = not.stream().map(d -> new LineItem(d.getName(), d.getAmount(), d.getIsTaxable())).collect(Collectors.toList());
        return lineItems;
    }

    /**
     * Determines the selling price.
     *
     * @param dealSheet
     * @param vehicle
     * @return Double
     */
    double determineSellingPrice(Optional<DealSheet> dealSheet, Optional<VehicleView> vehicle) {

        // retrieve from deal sheet if present
        if (dealSheet.isPresent()) {
            DealSheet dealSheetValue = dealSheet.get();

            if (dealSheetValue.getSalePriceByProgram() != null) {
                return dealSheetValue.getSalePriceByProgram();
            }

            if (dealSheetValue.getSalePriceByDealer() != null) {
                return dealSheetValue.getSalePriceByDealer();
            }

            if (dealSheetValue.getSalePrice() != null) {
                return dealSheetValue.getSalePrice();
            }
        }

        // retrieve from vehicle if present
        Double sellingPrice = vehicle.map(v -> v.getPrice()).map(Double::valueOf).orElse(0.0);
        return sellingPrice;
    }

    /**
     * Determines negative trade balance.
     *
     * @param dealSheet
     * @return Double
     */
    Double determineNegativeTradeBalance(Optional<DealSheet> dealSheet, Optional<UserVehicleQuoteView> userVehicleQuoteView, Optional<UserVehicleView> userVehicleView) {
        Double tradeValue = dealSheet.map(d -> d.getTradeValue()).orElse(userVehicleQuoteView.map(UserVehicleQuoteView::getAdjustedTradeValue).map(Integer::doubleValue).orElse(0.0));
        Double tradePayoff = dealSheet.map(d -> d.getTradePayoff()).orElse(userVehicleView.map(UserVehicleView::getBalance).map(i -> i * -1).orElse(0.0));
        Double calculatedValues = tradeValue + tradePayoff;
        Double negativeTradeBalance = Optional.of(calculatedValues).filter(i -> i < 0).orElse(0.0);
        return negativeTradeBalance;
    }

    /**
     * Populates the Inceptions.
     *
     * @param deal
     * @param quote
     * @return Inceptions
     */
    Inceptions getInceptions(CertificateView deal, Optional<QuoteView> quote, MonthlyPayment monthlyPayment) {
        Inceptions inceptions = new Inceptions();
        inceptions.setFirstMonthlyPayment(monthlyPayment.getFirstMonthlyPayment());
        inceptions.setLineItems(determineInceptionsLineItems(deal));
        inceptions.setTitleAndRegistration(quote.map(q -> q.getLicenseRegistration()).orElse(0.0));
        inceptions.setStaticLineItems(determineInceptionsStaticLineItemsByQuote(quote));
        return inceptions;
    }

    /**
     * Populates the Inceptions.
     *
     * @param deal
     * @param quote
     * @return Inceptions
     */
    Inceptions getInceptions(CertificateView deal, Quote quote) {
        Inceptions inceptions = new Inceptions();
        LeaseStepBreakDownDTO breakdown = quote.getStepCalculatedValues();
        inceptions.setFirstMonthlyPayment(breakdown.getFirstMonthPaymentWithTax().doubleValue());
        inceptions.setLineItems(determineInceptionsLineItems(deal));
        inceptions.setTitleAndRegistration(quote.getQuoteView().getLicenseRegistration());
        return inceptions;
    }

    /**
     * Populates the Inceptions.
     *
     * @param deal
     * @param paymentOpt
     * @return Inceptions
     */
    Inceptions getInceptionsPaas(CertificateView deal, Optional<PaasPayments> paymentOpt) {
        Inceptions inceptions = new Inceptions();
        inceptions.setFirstMonthlyPayment(paymentOpt.map(PaasPayments::getTotalMonthlyPayment).map(BigDecimal::doubleValue).orElse(0D));
        inceptions.setLineItems(determineInceptionsLineItems(deal));
        inceptions.setStaticLineItems(determineInceptionsStaticLineItems(paymentOpt));
        inceptions.setTitleAndRegistration(paymentOpt.map(PaasPayments::getLicenseAndRegistration).map(BigDecimal::doubleValue).orElse(0D));
        return inceptions;
    }

    /**
     * Returns the inceptions total.
     *
     * @param inceptionsOpt
     * @return Double
     */
    Double getInceptionsTotal(Optional<Inceptions> inceptionsOpt) {
        Double inceptionTotal = inceptionsOpt.map(Inceptions::getLineItems).map(i -> i.stream().mapToDouble(LineItem::getAmount).sum()).orElse(0D)
                + inceptionsOpt.map(Inceptions::getTitleAndRegistration).orElse(0D)
                + inceptionsOpt.map(Inceptions::getFirstMonthlyPayment).orElse(0D);
        return inceptionTotal;
    }

    /**
     * Determines the inceptions line items.
     *
     * @param deal
     * @return List<LineItem>
     */
    List<LineItem> determineInceptionsLineItems(CertificateView deal) {

        boolean hasValues = Optional.ofNullable(deal.getDealSheet()).map(DealSheet::getDealerFees).map(i -> !i.isEmpty()).orElse(false);
        if (deal.getSummaryDetails() != null && hasValues) {
            List<DealerFeeItem> list = deal.getDealSheet().getDealerFees();
            return list.stream()
                .filter(i -> i.getIsInception() == null ||  Boolean.TRUE.equals(i.getIsInception()))
                .map(d -> new LineItem(d.getName(), d.getAmount(), d.getIsTaxable())).collect(Collectors.toList());
        }

        // search Atlas dealer fees
        List<DealerFeeView> dealerFees = dealerFeesService.getLeaseDealerFeesByDealerId(deal.getDealerId(), deal.getStockType());
        List<DealerFeeView> not = dealerFeesService.getInceptionsFeesList(dealerFees, deal.getStockType());
        List<LineItem> lineItems = not.stream().map(d -> new LineItem(d.getName(), d.getAmount(), d.getIsTaxable())).collect(Collectors.toList());
        return lineItems;
    }

    /**
     * Determines the inceptions line items.
     *
     * @return List<LineItem>
     */
    List<LineItem> determineInceptionsStaticLineItemsByQuote(Optional<QuoteView> quote) {
        Double taxOnInception = quote.map(QuoteView::getPaasData).map(PaasData::getTaxOnInceptionFees).map(BigDecimal::doubleValue).orElse(0D);
        Double taxOnCapCost= quote.map(QuoteView::getPaasData).map(PaasData::getTaxOnTotalCapCostReduction).map(BigDecimal::doubleValue).orElse(0D);

        return List.of(LineItem.builder().name("Tax on Inception Fees").amount(taxOnInception).build(),
                LineItem.builder().name("Tax on Capital Cost").amount(taxOnCapCost).build());
    }

    /**
     * Determines the inceptions line items.
     *
     * @return List<LineItem>
     */
    List<LineItem> determineInceptionsStaticLineItems(Optional<PaasPayments> paymentsOpt) {
        List<LineItem> item = new ArrayList<>();
        item.add(LineItem.builder().name(INCEPTION_FEE_TAX).amount(paymentsOpt.map(PaasPayments::getTaxOnInceptionFees).map(BigDecimal::doubleValue).orElse(0D)).build());
        item.add(LineItem.builder().name(TOTAL_CAPT_COST_REDUCTION_TAX).amount(paymentsOpt.map(PaasPayments::getTaxOnTotalCapCostReduction).map(BigDecimal::doubleValue).orElse(0D)).build());
        return item;
    }

    /**
     * Populates the MonthlyPayment.
     *
     * @param quote
     * @return MonthlyPayment
     */
    MonthlyPayment getMonthlyPayment(Optional<QuoteView> quote, Optional<QuoteView> baseQuote) {
        MonthlyPayment monthlyPayment = new MonthlyPayment();

        if (quote.isPresent() && quote.get().isLease()) {
            Double baseMonthlyPayment = baseQuote.map(b -> b.getMonthlyPayment()).orElse(0.0);
            Double taxes = determineMonthlyTax(quote, baseMonthlyPayment).orElse(null);

            monthlyPayment.setFirstMonthlyPayment(baseMonthlyPayment);
            monthlyPayment.setMonthlyTax(taxes);
        } else {
            // finance quote already has the taxes included
            Double baseMonthlyPayment = quote.map(q -> q.getMonthlyPayment()).orElse(0.0);
            monthlyPayment.setFirstMonthlyPayment(baseMonthlyPayment);
        }
        return monthlyPayment;
    }

    /**
     * Populates the MonthlyPayment.
     *
     * @param quote
     * @return MonthlyPayment
     */
    MonthlyPayment getMonthlyPayment(Quote quote) {
        LeaseStepBreakDownDTO breakdown = quote.getStepCalculatedValues();
        MonthlyPayment monthlyPayment = new MonthlyPayment();
        monthlyPayment.setFirstMonthlyPayment(breakdown.getFirstMonthPayment().doubleValue());
        monthlyPayment.setMonthlyTax(breakdown.getTaxesOnFirstMonthPayment().doubleValue());
        return monthlyPayment;
    }

    /**
     * Populates the MonthlyPayment.
     *
     * @param paymentsOpt
     * @return MonthlyPayment
     */
    MonthlyPayment getMonthlyPaymentPaas(Optional<PaasPayments> paymentsOpt) {
        MonthlyPayment monthlyPayment = new MonthlyPayment();
        monthlyPayment.setFirstMonthlyPayment(paymentsOpt.map(PaasPayments::getPreTaxMonthlyPayment).map(BigDecimal::doubleValue).orElse(0D));
        monthlyPayment.setMonthlyTax(paymentsOpt.map(PaasPayments::getMonthlyTax).map(BigDecimal::doubleValue).orElse(0D));
        return monthlyPayment;
    }

    /**
     * Gets the monthly payment total.
     *
     * @param monthlyPaymentOpt
     * @return Double
     */
    Double getMonthlyPaymentTotal(Optional<MonthlyPayment> monthlyPaymentOpt) {
        Double firstMonthWithTax = monthlyPaymentOpt.map(MonthlyPayment::getFirstMonthlyPayment).orElse(0D)
            + monthlyPaymentOpt.map(MonthlyPayment::getMonthlyTax).orElse(0D);

        return firstMonthWithTax;
    }

    /**
     * Determines the monthly tax.
     *
     * @param quote
     * @return Double
     */
    Optional<Double> determineMonthlyTax(Optional<QuoteView> quote,  Double baseMonthlyPayment) {
        BigDecimal taxRate = quote.map(q -> q.getTaxes()).map(t -> t.getSalesTaxRate()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
        BigDecimal taxRatePct = taxRate.divide(BigDecimal.valueOf(100.0));
        double taxPayment = taxRatePct.multiply(BigDecimal.valueOf(baseMonthlyPayment)).doubleValue();
        return Optional.of(taxPayment);
    }

    /**
     * Populates the TradeAllowance.
     *
     * @param dealSheet
     * @param userVehicle
     * @return TradeAllowance
     */
    TradeAllowance getTradeAllowance(Optional<DealSheet> dealSheet, Optional<UserVehicleView> userVehicle, Optional<UserVehicleQuoteView> userVehicleQuoteView) {
        TradeAllowance tradeAllowance = new TradeAllowance();

        if (dealSheet.map(d -> d.getTradeValue()).isPresent()) {
            Double netTrade = determineNetTrade(dealSheet);
            tradeAllowance.setNetTrade(netTrade);
            tradeAllowance.setAllowance(dealSheet.map(d -> d.getTradeValue()).map(String::valueOf).orElse(null));
            tradeAllowance.setPayoff(dealSheet.map(d -> d.getTradePayoff()).map(i -> Math.abs(i)).orElse(null));
        }
        else if (userVehicle.isPresent() && !userVehicleQuoteView.map(UserVehicleQuoteView::isExpired).orElse(true)) {
            Integer tradeValue = userVehicleQuoteView.map(UserVehicleQuoteView::getAdjustedTradeValue).orElse(null);
            Double balance = userVehicle.map(UserVehicleView::getBalance).orElse(null);
            tradeAllowance.setNetTrade(determineNetTrade(tradeValue, balance));
            tradeAllowance.setAllowance(tradeValue != null ? tradeValue.toString() : null);
            tradeAllowance.setPayoff(balance);
        }

        Optional<StyleView> style = userVehicle.map(u -> u.getStyle());
        tradeAllowance.setAppraisalType(userVehicle.map(u -> u.getPayoffSource()).map(p -> p.getName()).orElse(null));
        tradeAllowance.setModel(style.map(s -> s.getModel()).map(m -> m.getName()).orElse(null));
        tradeAllowance.setTrim(style.map(s -> s.getTrim()).orElse(null));
        return tradeAllowance;
    }

    private Double determineNetTrade(Optional<DealSheet> dealSheet) {
        Double netTrade = dealSheet
            .filter(d -> d.getTradeValue() != null && d.getTradePayoff() != null)
            .map(d -> Math.abs(d.getTradeValue()) - Math.abs(d.getTradePayoff()))
            .orElse(null);
        return netTrade;
    }

    private Double determineNetTrade(Integer tradeValue, Double balance) {
       if (tradeValue != null && balance != null) {
         return  Math.abs(tradeValue) - Math.abs(balance);
       }
        return null;
    }

    /**
     * Returns the total trade allowance.
     *
     * @param tradeAllowance
     * @return Double
     */
    Double getTradeAllowanceTotal(TradeAllowance tradeAllowance) {
        Double totalTradeAllowance = Optional.ofNullable(tradeAllowance).map(TradeAllowance::getNetTrade).orElse(null);
        return totalTradeAllowance;
    }

    /**
     * Populates the Rebates.
     *
     * @param dealSheet
     * @return Rebates
     */
    Rebates getRebates(Optional<DealSheet> dealSheet, Optional<QuoteView> quoteView) {
        Rebates rebates = new Rebates();
        rebates.setLineItems(determineRebatesLineItems(dealSheet));
        rebates.setStaticLineItems(convertProgramRebatesToLineItems(quoteView.map(QuoteView::getProgramRebates).orElse(null)));
        return rebates;
    }
    /**
     * Populates the Rebates.
     *
     * @param dealSheet
     * @return Rebates
     */
    Rebates getCashRebates(Optional<DealSheet> dealSheet) {
        Rebates rebates = new Rebates();
        rebates.setLineItems(determineRebatesLineItems(dealSheet));
        rebates.setStaticLineItems(dealSheet.map(DealSheet::getDealerRebates).map(i -> i.stream().map(r -> LineItem.builder().name(r.getName()).amount(r.getAmount()).build()).collect(Collectors.toList())).orElse(List.of()));
        return rebates;
    }

    /**
     * Returns the total for all rebates.
     *
     * @param rebates
     * @return double
     */
    Double getRebatesTotal(Rebates rebates) {
        double total = rebates.getLineItems().stream().mapToDouble(LineItem::getAmount).sum();
        total += rebates.getStaticLineItems().stream().mapToDouble(LineItem::getAmount).sum();
        return total;
    }

    /**
     * Determines rebates line items.
     *
     * @param dealSheet
     * @return List<LineItem>
     */
    List<LineItem> determineRebatesLineItems(Optional<DealSheet> dealSheet) {
        List<RebateLineItem> rebates = dealSheet.map(d -> d.getCustomerRebates()).orElse(List.of());
        List<LineItem> lineItems = rebates.stream().map(r -> LineItem.builder().name(r.getName()).amount(r.getAmount()).build()).collect(Collectors.toList());
        return lineItems;
    }

    /**
     * Populates the TaxesAndFees.
     *
     */
    TaxesAndFees getTaxesAndFees() {
        TaxesAndFees taxesAndFees = new TaxesAndFees();
        taxesAndFees.setLineItems(List.of());
        return taxesAndFees;
    }

    /**
     * Populates the TaxesAndFees.
     *
     * @param dealSheet
     * @param quote
     * @return Inceptions
     */
    TaxesAndFees getTaxesAndFees(Optional<DealSheet> dealSheet, Optional<QuoteView> quote) {
        TaxesAndFees taxesAndFees = new TaxesAndFees();
        taxesAndFees.setSalesTax(quote.map(q -> q.getTaxes()).map(t -> t.getSalesTax()).orElse(0.0));
        taxesAndFees.setLineItems(determineTaxesAndFeesLineItems(dealSheet));
        double amount =  quote.map(q -> q.getLicenseRegistration()).orElse(0.0);
        taxesAndFees.setTitleAndRegistration(amount);
        return taxesAndFees;
    }

    TaxesAndFees getCashTaxesAndFees(CertificateView deal) {
        List<LineItem> lineItems;

        boolean hasValues = Optional.ofNullable(deal.getDealSheet()).map(DealSheet::getDealerFees).map(i -> !i.isEmpty()).orElse(false);
        if (deal.getSummaryDetails() != null && hasValues) {
            List<DealerFeeItem> list = deal.getDealSheet().getDealerFees();
            lineItems = list.stream().map(d -> new LineItem(d.getName(), d.getAmount(), d.getIsTaxable())).collect(Collectors.toList());
        } else {
            Collection<DealerFeeView> cashDealerFees = dealerFeesService.getCashDealerFeesByDealerId(deal.getDealerId(), deal.getStockType());
            lineItems = cashDealerFees.stream().map(r -> new LineItem(r.getName(), r.getAmount(), r.getIsTaxable())).collect(Collectors.toList());
        }

        Optional<PricesView> pricesOpt = Optional.ofNullable(deal.getPrices()).map(PricesView.PriceWrapper::getPrices);
        Double salesPrice = pricesOpt.map(PricesView::getSalesTax).orElse(null);
        Double titleAndRegistration = pricesOpt.map(PricesView::getTitleLicenseFee).orElse(null);

        TaxesAndFees taxesAndFees = TaxesAndFees.builder()
            .lineItems(lineItems)
            .salesTax(salesPrice)
            .titleAndRegistration(titleAndRegistration)
            .build();

        return taxesAndFees;
    }

    /**
     * Determines the taxes and fees line items.
     *
     * @param dealSheet
     * @return List<LineItem>
     */
    List<LineItem> determineTaxesAndFeesLineItems(Optional<DealSheet> dealSheet) {
        List<DealerFeeItem> feeItems = dealSheet.map(d -> d.getDealerFees()).orElse(List.of());
        List<LineItem> lineItems = feeItems.stream().map(f -> new LineItem(f.getName(), f.getAmount(), f.getIsTaxable())).collect(Collectors.toList());

        return lineItems;
    }

    /**
     * Saves the clone deal request into the dealer service.
     *
     * @param request
     * @return ClonedDealResponse
     */
    ClonedDealResponse saveCloneDeal(CloneDealRequest request) {
        String url = dealerServiceUrl + "/clone";
        ClonedDealResponse result = nissanWebClient.post(url, request, ClonedDealResponse.class, "SaveCloneDeal");
        return result;
    }

    public ClonedDealResponse upsertDeal(CloneDealRequest dealDeskingRequest) {
        String url = dealerServiceUrl + "/clone";
        ClonedDealResponse response = nissanWebClient.post(url, dealDeskingRequest, ClonedDealResponse.class, "UpdateCloneDeal");
        if (Objects.isNull(response)) {
            throw new DealNotFoundException("Cloned Deal Not Found for id " + dealDeskingRequest.getId());
        }
        return response;
    }

    public VehicleView retrieveVehicle(String dealerId, String stockNumber, String vin) {
        String url = buildRetrieveVehicleURL(dealerId, stockNumber, vin);
        VehicleView result = nissanWebClient.get(url, VehicleView.class, "DEAL_DESKING");
        if (Objects.isNull(result)) {
            determineErrorMessageAndThrowException(stockNumber, vin);
        }
        return result;
    }

    public VehicleUpdateModel buildVehicleUpdateModel(VehicleView vehicleView, String userId) {
        List<String> availableDealTypes = new ArrayList<>();
        if (Objects.nonNull(userId)) {
            final UserView userView = userClient.findById(userId);
            CampaignView campaignView = Optional.ofNullable(userView).map(UserView::getCampaign)
                .orElseThrow(com.carsaver.magellan.api.exception.NotFoundException::new);


            Optional<Integer> financierId = Optional.ofNullable(campaignView).map(CampaignView::getFinanceConfig).map(FinanceConfig::getEnabledFinancier);

            Optional<FinancierView> financierOpt = financierId.isEmpty() ? Optional.empty() : financierClient.findById(Long.valueOf(financierId.get().toString()));
            if (financierOpt.map(FinancierView::getConfig)
                .map(FinancierConfig::getPaymentConfig)
                .map(PaymentConfig::isLeaseEnabled).orElse(false)) {
                availableDealTypes.add("LEASE");
            }
            availableDealTypes.add("FINANCE");
        }

        if (Objects.isNull(vehicleView.getImageUrl())) {
            vehicleView.setImageUrl(new String[]{new String[]{getImageUrlFromEvoxClient(vehicleView)}[0]});
        }
        VehicleUpdateModel vehicleUpdateModel = VehicleUpdateModel.builder()
            .exteriorColor(defaultIfNull(vehicleView.getExteriorColor(), ""))
            .imageUrl(defaultIfNull(vehicleView.getImageUrl(), new String[]{""})[0])
            .interiorColor(defaultIfNull(vehicleView.getInteriorColor(), ""))
            .invoice(defaultIfNull(vehicleView.getInvoicePrice(), 0d).doubleValue())
            .make(defaultIfNull(vehicleView.getMake(), ""))
            .model(defaultIfNull(vehicleView.getModel(), ""))
            .msrp(defaultIfNull(vehicleView.getMsrp(), 0d).doubleValue())
            .sellingPrice(defaultIfNull(vehicleView.getPrice(), 0d).doubleValue())
            .stockNumber(defaultIfNull(vehicleView.getStockNumber(), ""))
            .trim(defaultIfNull(vehicleView.getTrim(), ""))
            .vehicleType(defaultIfNull(vehicleView.getStockType(), "").toString())
            .vin(defaultIfNull(vehicleView.getVin(), ""))
            .year(defaultIfNull(vehicleView.getYear(), 0))
            .inventoryId(vehicleView.getId())
            .availableDealTypes(availableDealTypes)
            .build();

        return vehicleUpdateModel;
    }

    private String getImageUrlFromEvoxClient(VehicleView car) {
        EvoxImageResult result = evoxImageClient.getByStyleId(car.getStyleId().toString(), car.getExteriorColor(), car.getExteriorColorCode(), car.getExteriorColorGeneric());
        if (result != null) {
            return result.getUrl();
        }
        return null;
    }

    public static <T> T defaultIfNull(T value, T defaultValue) {
        return Objects.isNull(value) ? defaultValue : value;
    }

    private String buildRetrieveVehicleURL(String dealerId, String stockNumber, String vin) {
        String url = inventoryServiceUrl.concat("/newOrUsed/retrieve/vehicle");
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(url);
        appendQueryParamIfExists(uriComponentsBuilder, DEALER_ID, dealerId);
        appendQueryParamIfExists(uriComponentsBuilder, STOCK_NUMBER, stockNumber);
        appendQueryParamIfExists(uriComponentsBuilder, VIN, vin);
        String uri = uriComponentsBuilder.build().toString();
        return uri;
    }

    private void appendQueryParamIfExists(UriComponentsBuilder uriComponentsBuilder, String key, String value) {
        if (Objects.nonNull(value)) {
            uriComponentsBuilder.queryParam(key, value);
        }
    }

    private void determineErrorMessageAndThrowException(String stockNumber, String vin) {
        String errorMessage = "vehicle not found";

        if (vin != null && stockNumber != null) {
            errorMessage = errorMessage + " for vin: " + vin + " and stock number: " + stockNumber;
        } else if (vin != null) {
            errorMessage = errorMessage + " for vin: " + vin;
        } else {
            errorMessage = errorMessage + " for stock number: " + stockNumber;
        }

        throw new NotFoundException(errorMessage);
    }

    Double getTotalDueAtSigning(DueAtSigning dueAtSigning) {
        Double consumerCash = Double.valueOf(Optional.ofNullable(dueAtSigning.getConsumerCash()).orElse("0.0"));
        Double positiveTradeEquity = Optional.ofNullable(dueAtSigning.getPositiveTradeEquity()).orElse(0.0);
        Double totalDueAtSigning = dueAtSigning.getLineItems().stream().mapToDouble(LineItem::getAmount).sum();
        Double result = consumerCash + positiveTradeEquity + totalDueAtSigning;
        return result;
    }

    Double getTotalInception(Inceptions inceptions,MonthlyPayment monthlyPayment) {
        Double inceptionTotal = inceptions.getLineItems().stream().mapToDouble(LineItem::getAmount).sum();
        Double staticTotal = inceptions.getStaticLineItems().stream().mapToDouble(LineItem::getAmount).sum();
        Double titleAndRegi = Optional.ofNullable(inceptions.getTitleAndRegistration()).orElse(0.0);
        Double totalDueAtSigning = Optional.ofNullable(monthlyPayment.getFirstMonthlyPayment()).orElse(0.0);
        Double monthlyTax = Optional.ofNullable(monthlyPayment.getMonthlyTax()).orElse(0.0);
        Double result = inceptionTotal + titleAndRegi + totalDueAtSigning + monthlyTax + staticTotal;
        return result;
    }

    Double getMonthlyPaymentTotal(MonthlyPayment monthlyPayment,String dealType)
    {
        double result;
        Double monthlyPmt = Optional.ofNullable(monthlyPayment.getFirstMonthlyPayment()).orElse(0.0);
        Double monthlyTax = Optional.ofNullable(monthlyPayment.getMonthlyTax()).orElse(0.0);
        if(dealType.equalsIgnoreCase("finance"))
        {
            result = monthlyPmt;
        }
        else
        {
            result = monthlyPmt + monthlyTax;
        }
        return result;
    }

    Double getTotalTradeAllowances(TradeAllowance tradeAllowances)
    {
        Double tradeAllowance = Double.valueOf(Optional.ofNullable(tradeAllowances.getAllowance()).orElse("0.0"));
        Double payoff = Optional.ofNullable(tradeAllowances.getPayoff()).orElse( 0.0);
        Double totalTradeAllowances = tradeAllowance - payoff;
        return totalTradeAllowances;
    }

    Double getTotalTaxesAndFees(TaxesAndFees taxesAndFees) {
        Double totalTaxesAndFees = taxesAndFees.getSalesTax();
        totalTaxesAndFees += taxesAndFees.getLineItems().stream().mapToDouble(LineItem::getAmount).sum();
        totalTaxesAndFees += taxesAndFees.getTitleAndRegistration();
        return totalTaxesAndFees;
    }

    List<CloneDealRequest.LineItem> convertProgramRebatesToLineItems(List<ProgramRebates> programRebatesList) {
        List<CloneDealRequest.LineItem> result = Optional.ofNullable(programRebatesList)
            .map(i -> i.stream()
                .filter(d -> d.getValue() != null)
                .map(j -> CloneDealRequest.LineItem.builder().amount(j.getValue().doubleValue()).name(j.getDescription()).build())
                .collect(Collectors.toList()))
            .orElse(List.of());
        return result;
    }

    public String findDealersRouteOneId(DealerView dealerView) {
        return environment.acceptsProfiles(Profiles.of("local", "beta"))
            ? "FH1FG"
            : dealerView.getRouteOneDealerId();
    }
}
