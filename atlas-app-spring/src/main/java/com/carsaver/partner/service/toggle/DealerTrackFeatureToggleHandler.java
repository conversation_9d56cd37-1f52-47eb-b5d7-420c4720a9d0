package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.model.protection_products.response.ErrorResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DealerTrackFeatureToggleHandler implements FeatureToggleHandler {

    private final FeatureSubscriptionsClient featureSubscriptionsClient;

    @Value("${features-subscription.lms-feature-id}")
    private String dealerTrackFeatureId;

    @Override
    public boolean supports(String configType) {
        return "dealer_track".equalsIgnoreCase(configType);
    }

    @Override
    public DealerProgram handleFeatureToggle(String dealerId, String programId, ToggleConfigRequest toggleConfigRequest) {
        FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
        request.setDealerId(dealerId);
        request.setEntityId(programId);
        request.setFeatureRId(dealerTrackFeatureId);
        request.setActive(toggleConfigRequest.getIsEnabled());
        request.setContext("dealerTrack");
        request.setUserId(toggleConfigRequest.getUserId());

        FeatureSubscriptionResponse response = featureSubscriptionsClient.saveFeatureSubscription(request);

        if (response == null || response.getActive() == null) {
            throw new InternalServerError(ErrorResponse.builder().errorMessage("Failed to update Dealer Track feature").build());
        }

        return DealerProgram.builder()
                .isDealerTrackEnabled(response.getActive())
                .build();
    }
}
