package com.carsaver.partner.service.warranty.exception;

import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;

public class PremiumsRequestValidationException extends MethodArgumentNotValidException {

    public PremiumsRequestValidationException(BindingResult bindingResult) {
        super(null, bindingResult);
    }

    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder("Validation failed");

        if (this.getBindingResult().getErrorCount() > 1) {
            sb.append(" with ").append(this.getBindingResult().getErrorCount()).append(" errors");
        }
        sb.append(": ");
        for (ObjectError error : this.getBindingResult().getAllErrors()) {
            sb.append("[").append(error).append("] ");
        }

        return sb.toString();
    }
}
