package com.carsaver.partner.converter;

import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.payoff.PayoffSource;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.partner.model.deal.TradeOfferModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * TODO - change implementation to ModelConverter
 */
@Component
public class UserVehicleViewToPayoffQuoteConverter implements Converter<UserVehicleView, TradeOfferModel.PayoffQuote> {

    @Autowired
    private FinancierClient financierClient;

    @Override
    public TradeOfferModel.PayoffQuote convert(UserVehicleView userVehicle) {
        TradeOfferModel.PayoffQuote payoffQuote = new TradeOfferModel.PayoffQuote();
        payoffQuote.setFinancierId(userVehicle.getFinancierId());
        payoffQuote.setMonthlyPayment(userVehicle.getMonthlyPayment());
        payoffQuote.setPaymentsRemaining(userVehicle.getRemainingPayments());

        Double payoffAmount = Optional.ofNullable(userVehicle.getPayOff()).map(Math::abs).orElse(0.00);

        payoffQuote.setPayoffAmount(payoffAmount);
        payoffQuote.setPayoffGoodThrough(userVehicle.getPayoffExpiration());
        payoffQuote.setFinanceType(userVehicle.getPurchaseType());

        String financierName = Optional.ofNullable(userVehicle.getFinancierId())
            .flatMap(financierClient::findById)
            .map(FinancierView::getName)
            .orElse(userVehicle.getFinancierName());

        payoffQuote.setFinanceCompany(financierName);

        String payoffMethod = Optional.ofNullable(userVehicle.getPayoffSource())
            .map(PayoffSource::getName)
            .map(s -> {
                switch (s) {
                    case "manual": return "Customer Provided";
                    case "nissan":
                    case "upgrade-campaign":
                    case "route-one": return "Verified By Lender";
                    case "soft-pull": return "Estimated";
                    case "dealer-verified": return "Manual";
                    default: return "Unknown";
                }
            }).orElse("Unknown");

        payoffQuote.setPayoffMethod(payoffMethod);

        if (PayoffSource.ROUTE_ONE.equals(userVehicle.getPayoffSource())) {
            payoffQuote.setPayoffReferenceId(userVehicle.getPayoffReferenceId());
        }

        return payoffQuote;
    }
}
