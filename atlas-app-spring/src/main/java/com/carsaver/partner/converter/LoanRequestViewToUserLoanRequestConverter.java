package com.carsaver.partner.converter;

import com.carsaver.magellan.api.finance.LoanDecisionService;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.magellan.model.finance.LoanResponseView;
import com.carsaver.partner.model.finance.FinanceDecision;
import com.carsaver.partner.model.mapper.FinanceDecisionMapper;
import com.carsaver.partner.model.user.UserLoanRequestDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * TODO - change implementation to ModelConverter
 */
@Component
public class LoanRequestViewToUserLoanRequestConverter implements Converter<LoanRequestView, UserLoanRequestDTO> {

    @Autowired
    private LoanDecisionService loanDecisionService;

    @Autowired
    private FinancierClient financierClient;

    @Autowired
    private FinanceDecisionMapper financeDecisionMapper;

    @Override
    public UserLoanRequestDTO convert(LoanRequestView loanRequest) {
        String type = loanRequest.isFinanceApp() ? "Finance" : "Lease";

        var builder = UserLoanRequestDTO.builder()
            .id(loanRequest.getId())
            .vehicle(UserLoanRequestDTO.Vehicle.from(loanRequest.getCertificate()))
            .appStatusResponse(UserLoanRequestDTO.AppStatusResponse.from(loanRequest.getResponse()))
            .requestedAmountFinanced(loanRequest.getPayload().getRequestedAmountFinanced())
            .vin(loanRequest.getVin())
            .appId(loanRequest.getAppId())
            .createdDate(loanRequest.getCreatedDate())
            .certificate(loanRequest.getCertificate())
            .type(type);

        List<LoanResponseView> loanResponses = Optional.ofNullable(loanRequest.getResponses()).orElseGet(Collections::emptyList);
        List<FinanceDecision> financeDecisionModels = loanResponses
            .stream()
            .map(loanResponse -> convertToFinanceDecisionModel(loanResponse))
            .flatMap(Optional::stream)
            .collect(Collectors.toList());

        String overallStatus = loanRequest.isComplete()
            ? determineOverallStatusFrom(financeDecisionModels)
            : "In Progress";

        return builder
            .overallStatus(overallStatus)
            .financeDecisions(financeDecisionModels)
            .build();
    }

    private Optional<FinanceDecision> convertToFinanceDecisionModel(LoanResponseView loanResponse) {
        FinancierView financier = ofNullable(loanResponse.getLenderId()).flatMap(financierClient::findByHorizonId).orElse(null);

        var financeDecisionModel = loanDecisionService.buildFinanceResponse(loanResponse, financier);

        return financeDecisionModel.map(decisionModel -> financeDecisionMapper.toModel(decisionModel));
    }

    private String determineOverallStatusFrom(List<FinanceDecision> financeDecisionModels) {
        boolean fullyApproved = false, conditionedApproval = false;

        for(FinanceDecision fdm: financeDecisionModels) {
            if(fdm.isFullyApproved()) {
                fullyApproved = true;
                break;
            } else if(fdm.isConditionedApproval()) {
                conditionedApproval = true;
            }
        }

        return fullyApproved
            ? "Approved"
            : conditionedApproval
                ? "Conditioned"
                : "Declined";
    }

}
