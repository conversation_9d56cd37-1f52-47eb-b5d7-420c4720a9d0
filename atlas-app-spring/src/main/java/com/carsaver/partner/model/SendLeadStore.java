package com.carsaver.partner.model;

import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.chrome.MakeView;
import com.carsaver.magellan.model.chrome.ModelView;
import com.carsaver.magellan.model.chrome.StyleView;
import lombok.Builder;
import lombok.Data;

import java.util.Optional;

@Data
@Builder
public class SendLeadStore {
    private Integer certificateId;
    private Dealer dealer;
    private Vehicle vehicle;

    public static SendLeadStore from(CertificateView certificate) {
        return SendLeadStore.builder()
            .dealer(Dealer.from(certificate.getDealer()))
            .vehicle(Vehicle.from(certificate))
            .build();
    }

    @Data
    @Builder
    public static class Dealer {
        private String id;
        private String name;

        public static Dealer from(DealerView dealer) {
            return Dealer.builder()
                .id(dealer.getId())
                .name(dealer.getName())
                .build();
        }
    }

    @Data
    @Builder
    public static class Vehicle {
        private Integer year;
        private String make;
        private String model;
        private String trim;

        public static Vehicle from(CertificateView certificate) {
            StyleView style = certificate.getStyle();
            Optional<StyleView> optStyle = Optional.ofNullable(style);

            Integer year = Optional.ofNullable(certificate.getVehicle()).map(VehicleView::getYear).orElseGet(() -> optStyle.map(StyleView::getYear).orElse(null));
            String make = Optional.ofNullable(certificate.getVehicle()).map(VehicleView::getMake).orElseGet(() -> optStyle.map(StyleView::getMake).map(MakeView::getName).orElse(null));
            String model = Optional.ofNullable(certificate.getVehicle()).map(VehicleView::getModel).orElseGet(() -> optStyle.map(StyleView::getModel).map(ModelView::getName).orElse(null));
            String trim = Optional.ofNullable(certificate.getVehicle()).map(VehicleView::getTrim).orElseGet(() -> optStyle.map(StyleView::getTrim).orElse(null));

            return Vehicle.builder()
                .year(year)
                .make(make)
                .model(model)
                .trim(trim)
                .build();
        }
    }

}
