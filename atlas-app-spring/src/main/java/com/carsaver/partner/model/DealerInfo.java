package com.carsaver.partner.model;


import com.carsaver.core.SalesTxSource;
import com.carsaver.magellan.model.DealerGroupView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.dealer.CrmSystemView;
import com.carsaver.magellan.model.dealer.DmsSystemView;
import com.carsaver.magellan.model.dealer.MenuProviderView;
import com.carsaver.magellan.model.dealer.PaymentConfig;
import com.carsaver.magellan.model.preferences.DealerPreferences;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Optional;

@Data
@Builder
public class DealerInfo  {
    private String id;
    private String dealerId;
    private String nnaDealerId;
    private String name;
    private String address1;
    private String city;
    private String state;
    private String zipCode;
    private String dealerGroupName;
    private String phoneNumber;
    private String websiteUrl;
    private String newInventoryUrl;
    private String usedInventoryUrl;
    private String timeZone;
    private Double financeMarkupPercent;
    private Double leaseMarkupPercent;
    private String dmsName;
    private String crmProviderName;
    private String salesMatchingSource;
    private String financeMenuProvider;
    private boolean enrolledInWarrantyProgram;
    private boolean spanishEnabled;
    private boolean freeDelivery;
    private Integer deliveryDistance;
    private List<DealerView.Period> hoursOfOperation;
    private DealerPreferences preferences;

    private static final String ECOMMERCE_URL_FORMAT = "https://shopathome.carsaver.com?dealerId=%s";

    public static DealerInfo from(DealerView dealer){

        return builder()
            .id(dealer.getId())
            .nnaDealerId(dealer.getNnaDealerId())
            .dealerId(dealer.getDealerId())
            .name(dealer.getName())
            .address1(dealer.getAddress1())
            .city(dealer.getCity())
            .state(dealer.getState())
            .zipCode(dealer.getZipCode())
            .dealerGroupName(dealer.getDealerGroup())
            .phoneNumber(dealer.getPhoneNumber())
            .websiteUrl(dealer.getWebsiteUrl())
            .newInventoryUrl(dealer.getNewInventoryUrl())
            .usedInventoryUrl(dealer.getUsedInventoryUrl())
            .timeZone(dealer.getTimeZone())
            .financeMarkupPercent(dealer.getPaymentConfig())
            .leaseMarkupPercent(dealer.getPaymentConfig())
            .dmsName(dealer.getDmsSystem())
            .crmProviderName(dealer.getCrmSystem())
            .salesMatchingSource(dealer.getSalesTxSource())
            .financeMenuProvider(dealer.getMenuProvider())
            .enrolledInWarrantyProgram(dealer.getEnrolledInWarrantyProgram())
            .spanishEnabled(dealer.getSpanishEnabled())
            .freeDelivery(dealer.getDeliveryEnabled())
            .deliveryDistance(dealer.getDeliveryDistance())
            .hoursOfOperation(dealer.getHours())
            .preferences(dealer.getPreferences())
            .build();
    }

    public String getEcommerceInventoryUrl(){
        return String.format(ECOMMERCE_URL_FORMAT, this.dealerId);
    }

    public static class DealerInfoBuilder {
        public DealerInfoBuilder dealerGroupName(DealerGroupView dealerGroup){
            this.dealerGroupName = Optional.ofNullable(dealerGroup).map(DealerGroupView::getName).orElse(null);
            return this;
        }

        public DealerInfoBuilder financeMarkupPercent(PaymentConfig paymentConfig){
            this.financeMarkupPercent = Optional.ofNullable(paymentConfig).map(PaymentConfig::getFinanceMarkupPercent).orElse(null);
            return this;
        }

        public DealerInfoBuilder leaseMarkupPercent(PaymentConfig paymentConfig){
            this.leaseMarkupPercent = Optional.ofNullable(paymentConfig).map(PaymentConfig::getLeaseMarkupPercent).orElse(null);
            return this;
        }

        public DealerInfoBuilder dmsName(DmsSystemView dmsSystem){
            this.dmsName = Optional.ofNullable(dmsSystem).map(DmsSystemView::getName).orElse(null);
            return this;
        }

        public DealerInfoBuilder crmProviderName(CrmSystemView crmSystem){
            this.crmProviderName = Optional.ofNullable(crmSystem).map(CrmSystemView::getName).orElse(null);
            return this;
        }

        public DealerInfoBuilder salesMatchingSource(SalesTxSource salesTxSource){
            this.salesMatchingSource = Optional.ofNullable(salesTxSource).map(SalesTxSource::getName).orElse(null);
            return this;
        }

        public DealerInfoBuilder financeMenuProvider(MenuProviderView menuProvider){
            this.financeMenuProvider = Optional.ofNullable(menuProvider).map(MenuProviderView::getName).orElse(null);
            return this;
        }
    }

}

