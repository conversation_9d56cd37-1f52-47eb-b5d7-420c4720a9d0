package com.carsaver.partner.salesstages.model.v2;

import com.carsaver.partner.elasticsearch.criteria.CustomersSearchCriteria;
import com.carsaver.search.annotation.CompoundQuery;
import com.carsaver.search.query.BoolQueryOccurrence;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class IncludeProgramSubscriptionStageCustomerSearchCriteria extends CustomersSearchCriteria {

    @CompoundQuery(boolQuery = BoolQueryOccurrence.FILTER)
    private List<ProgramSubscriptionStagePair> programSubscriptionStagePair;
}
