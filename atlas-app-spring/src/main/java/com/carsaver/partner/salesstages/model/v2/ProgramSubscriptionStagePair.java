package com.carsaver.partner.salesstages.model.v2;

import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.BoolQueryOccurrence;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgramSubscriptionStagePair extends AbstractSearchCriteria {

    @TermQuery(
            path = "programSubscriptionStages",
            field = "dealerId",
            boolQuery = BoolQueryOccurrence.MUST,
            nested = true
    )
    private String programSubscriptionStagesDealerIdToMatch;

    @TermQuery(
        path = "programSubscriptionStages",
        field = "programId",
        boolQuery = BoolQueryOccurrence.MUST,
        nested = true
    )
    private List<String> programSubscriptionStagesProgramIdToMatch;

    @TermQuery(
            path = "programSubscriptionStages",
            field = "stageName",
            boolQuery = BoolQueryOccurrence.MUST,
            nested = true
    )
    private List<String> programSubscriptionStagesStageNameToMatch;
}
