package com.carsaver.partner.salesstages.model.v2;

import com.carsaver.partner.elasticsearch.criteria.CustomersSearchCriteria;
import com.carsaver.search.annotation.BoolConfig;
import com.carsaver.search.annotation.BoolQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@AllArgsConstructor
@BoolConfig(minimumShouldMatch = 1)
public class DealerUserCustomerSearchCriteriaByStage extends CustomersSearchCriteria {

    @BoolQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private final IncludeProgramSubscriptionStageCustomerSearchCriteria includeProgramSubscriptionStage;
    @BoolQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private final NoFoundInProgramSubscriptionsStageCustomersSearchCriteria noFoundInProgramSubscriptionsStage;
    @TermQuery(field = "dealerLinks.dealerId", scope = SearchScope.QUERY)
    private List<String> dealerLinks;

}
