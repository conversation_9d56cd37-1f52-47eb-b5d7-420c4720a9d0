package com.carsaver.partner.support;

import com.carsaver.partner.model.AtlasContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.annotation.RequestScope;

import javax.servlet.http.HttpSession;

@Component
@RequestScope
public class AtlasContextProvider {

    @Autowired
    private HttpSession session;

    public AtlasContext getUpgradeContext() {
        AtlasContext atlasContext = (AtlasContext) session.getAttribute(AtlasContext.KEY);
        if(atlasContext == null) {
            atlasContext = new AtlasContext();
            session.setAttribute(AtlasContext.KEY, atlasContext);
        }

        return atlasContext;
    }

    public void setUpgradeContext(AtlasContext upgradeContext) {
        session.setAttribute(AtlasContext.KEY, upgradeContext);
    }
}
