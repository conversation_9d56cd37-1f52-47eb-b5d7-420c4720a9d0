package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.magellan.model.lead.SaleStatus;
import com.carsaver.search.annotation.*;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class VehicleSaleSearchCriteria extends AbstractSearchCriteria {

    @TermQuery(field = "status", scope = SearchScope.POST_FILTER)
    private SaleStatus statuses;

    @TermQuery(field = "user.id", scope = SearchScope.POST_FILTER)
    private String userId;

    @TermQuery(field = "dealer.id", scope = SearchScope.POST_FILTER)
    private List<String> dealerIds;
}
