package com.carsaver.partner.elasticsearch.criteria;


import com.carsaver.search.annotation.TermQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor()
public class UserDealerLinkProgramPair extends UserDealerProgramPair {
    @TermQuery(field = "dealerAcls")
    private String dealerId;
}
