package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class KpiSearchCriteria extends AbstractSearchCriteria {

    // TODO should rename this to something field b/c it isn't really createdDate we are always filtering on
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @RangeQuery
    private ZonedDateRange createdDate;

    @TermQuery(field = "source.hostname", scope = SearchScope.POST_FILTER)
    private List<String> userSourceHosts;

    @TermQuery(field = "dealer.id")
    private List<String> dealerIds;

    @TermQuery(field = "type", scope = SearchScope.POST_FILTER)
    private List<String> types;
}
