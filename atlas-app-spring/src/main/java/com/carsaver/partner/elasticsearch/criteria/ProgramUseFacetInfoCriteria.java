package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.search.annotation.BoolQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProgramUseFacetInfoCriteria extends CustomersSearchCriteria {

}
