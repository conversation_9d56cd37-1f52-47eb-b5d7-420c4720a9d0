package com.carsaver.partner.configuration.theming;

import com.carsaver.configuration.v2.api.theme.ThemeResponse;
import com.carsaver.configuration.v2.api.theme.ThemingModel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring")
public interface ThemingResponseMapper {

    @Mappings({
        @Mapping(target = "navigationBarActive", source = "topNavigationBar.active"),
        @Mapping(target = "navigationBarBackgroundColorVal", source = "topNavigationBar.backgroundColor"),
        @Mapping(target = "navigationBarTextColorVal", source = "topNavigationBar.textColor"),

        @Mapping(target = "accentValuesActive", source = "accentColor.active"),
        @Mapping(target = "accentValuesColorVal", source = "accentColor.color"),

        @Mapping(target = "firstButtonActive", source = "primaryButton.active"),
        @Mapping(target = "firstButtonBackgroundColorVal", source = "primaryButton.backgroundColor"),
        @Mapping(target = "firstButtonBorderColorVal", source = "primaryButton.borderColor"),
        @Mapping(target = "firstButtonTextColorVal", source = "primaryButton.textColor"),
        @Mapping(target = "firstButtonHighlightRolloverBackgroundColorVal", source = "primaryButton.highlightRolloverBackgroundColor"),
        @Mapping(target = "firstButtonHighlightRolloverBorderColorVal", source = "primaryButton.highlightRolloverBorderColor"),
        @Mapping(target = "firstButtonHighlightRolloverTextColorVal", source = "primaryButton.highlightRolloverTextColor"),

        @Mapping(target = "secondButtonActive", source = "secondaryButton.active"),
        @Mapping(target = "secondButtonBackgroundColorVal", source = "secondaryButton.backgroundColor"),
        @Mapping(target = "secondButtonBorderColorVal", source = "secondaryButton.borderColor"),
        @Mapping(target = "secondButtonTextColorVal", source = "secondaryButton.textColor"),
        @Mapping(target = "secondButtonHighlightRolloverBackgroundColorVal", source = "secondaryButton.highlightRolloverBackgroundColor"),
        @Mapping(target = "secondButtonHighlightRolloverBorderColorVal", source = "secondaryButton.highlightRolloverBorderColor"),
        @Mapping(target = "secondButtonHighlightRolloverTextColorVal", source = "secondaryButton.highlightRolloverTextColor"),

        @Mapping(target = "thirdButtonActive", source = "tertiaryButton.active"),
        @Mapping(target = "thirdButtonBackgroundColorVal", source = "tertiaryButton.backgroundColor"),
        @Mapping(target = "thirdButtonBorderColorVal", source = "tertiaryButton.borderColor"),
        @Mapping(target = "thirdButtonTextColorVal", source = "tertiaryButton.textColor"),
        @Mapping(target = "thirdButtonHighlightRolloverBackgroundColorVal", source = "tertiaryButton.highlightRolloverBackgroundColor"),
        @Mapping(target = "thirdButtonHighlightRolloverBorderColorVal", source = "tertiaryButton.highlightRolloverBorderColor"),
        @Mapping(target = "thirdButtonHighlightRolloverTextColorVal", source = "tertiaryButton.highlightRolloverTextColor"),

    })
    ThemingModel toModel(ThemeResponse response);


    @Mappings({
        @Mapping(source = "navigationBarActive", target = "topNavigationBar.active"),
        @Mapping(source = "navigationBarBackgroundColorVal", target = "topNavigationBar.backgroundColor"),
        @Mapping(source = "navigationBarTextColorVal", target = "topNavigationBar.textColor"),

        @Mapping(source = "accentValuesActive", target = "accentColor.active"),
        @Mapping(source = "accentValuesColorVal", target = "accentColor.color"),

        @Mapping(source = "firstButtonActive", target = "primaryButton.active"),
        @Mapping(source = "firstButtonBackgroundColorVal", target = "primaryButton.backgroundColor"),
        @Mapping(source = "firstButtonBorderColorVal", target = "primaryButton.borderColor"),
        @Mapping(source = "firstButtonTextColorVal", target = "primaryButton.textColor"),
        @Mapping(source = "firstButtonHighlightRolloverBackgroundColorVal", target = "primaryButton.highlightRolloverBackgroundColor"),
        @Mapping(source = "firstButtonHighlightRolloverBorderColorVal", target = "primaryButton.highlightRolloverBorderColor"),
        @Mapping(source = "firstButtonHighlightRolloverTextColorVal", target = "primaryButton.highlightRolloverTextColor"),

        @Mapping(source = "secondButtonActive", target = "secondaryButton.active"),
        @Mapping(source = "secondButtonBackgroundColorVal", target = "secondaryButton.backgroundColor"),
        @Mapping(source = "secondButtonBorderColorVal", target = "secondaryButton.borderColor"),
        @Mapping(source = "secondButtonTextColorVal", target = "secondaryButton.textColor"),
        @Mapping(source = "secondButtonHighlightRolloverBackgroundColorVal", target = "secondaryButton.highlightRolloverBackgroundColor"),
        @Mapping(source = "secondButtonHighlightRolloverBorderColorVal", target = "secondaryButton.highlightRolloverBorderColor"),
        @Mapping(source = "secondButtonHighlightRolloverTextColorVal", target = "secondaryButton.highlightRolloverTextColor"),

        @Mapping(source = "thirdButtonActive", target = "tertiaryButton.active"),
        @Mapping(source = "thirdButtonBackgroundColorVal", target = "tertiaryButton.backgroundColor"),
        @Mapping(source = "thirdButtonBorderColorVal", target = "tertiaryButton.borderColor"),
        @Mapping(source = "thirdButtonTextColorVal", target = "tertiaryButton.textColor"),
        @Mapping(source = "thirdButtonHighlightRolloverBackgroundColorVal", target = "tertiaryButton.highlightRolloverBackgroundColor"),
        @Mapping(source = "thirdButtonHighlightRolloverBorderColorVal", target = "tertiaryButton.highlightRolloverBorderColor"),
        @Mapping(source = "thirdButtonHighlightRolloverTextColorVal", target = "tertiaryButton.highlightRolloverTextColor")
    })
    ThemeRequest toRequest(ThemingModel dto);
}
