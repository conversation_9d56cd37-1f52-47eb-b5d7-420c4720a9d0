package com.carsaver.partner.configuration.ctaconfig.mappers

import com.carsaver.partner.configuration.ctaconfig.CtaConfigRequest
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.util.*
import kotlin.reflect.KMutableProperty0

const val BASE_KEY = "websiteConfigurations/ctaButtons/customizePlugins/location"

class CtaConfigPayloadMapper {

    companion object {
        val mapper = ObjectMapper()

        fun mapToConfig(payload: String): CtaConfigRequest {
            val payloadMap = mapper.readValue<Map<String, String>>(payload)
            return mapToConfig(payloadMap)
        }

        fun mapToConfig(payload: Map<String, String?>): CtaConfigRequest {
            val vdp = getPage(payload, "/vehicleDetailsPage")
            val srp = getPage(payload, "/listingsPage")
            val integrationMethod = payload["integrationMethod"]
            val displayType = payload["displayType"]
            val ctaConfig = CtaConfigRequest()

            if (vdp != null) {
                ctaConfig.vdp = vdp
            }

            if (srp != null) {
                ctaConfig.listings = srp
            }

            if (integrationMethod != null) {
                ctaConfig.integrationMethod = integrationMethod
            }

            if (displayType != null) {
                ctaConfig.displayType = displayType
            }

            return ctaConfig
        }

        private fun getPage(payload: Map<String, String?>, pageLocation: String): Optional<CtaConfigRequest.CtaPage>? {
            val page = payload.filterKeys { it.startsWith("$BASE_KEY$pageLocation") }
            if (page.isEmpty()) {
                return null
            }

            val formattingOptions = getFormattingOptions("$BASE_KEY$pageLocation/formattingOptions", page)
            val primaryButton = getButton("$BASE_KEY$pageLocation", "/primaryButton", page)
            val secondButton = getButton("$BASE_KEY$pageLocation", "/secondButton", page)
            val thirdButton = getButton("$BASE_KEY$pageLocation", "/thirdButton", page)
            val fourthButton = getButton("$BASE_KEY$pageLocation", "/fourthButton", page)

            return Optional.of(
                CtaConfigRequest.CtaPage(
                    formattingOptions = formattingOptions,
                    primaryButton = primaryButton,
                    secondButton = secondButton,
                    thirdButton = thirdButton,
                    fourthButton = fourthButton
                )
            )
        }

        private fun getFormattingOptions(
            path: String,
            page: Map<String, String?>
        ): Optional<CtaConfigRequest.FormattingOptions>? {
            val formattingOptionsMap = page.filterKeys { it.startsWith(path) }
            if (formattingOptionsMap.isEmpty()) {
                return null
            }

            val formattingOptions = CtaConfigRequest.FormattingOptions()
            setAttributeWidth(path, "/width", formattingOptionsMap, formattingOptions::width)
            setNullableAttribute(path, "/height", formattingOptionsMap, formattingOptions::height)
            setAttribute(path, "/font/font-family", formattingOptionsMap, formattingOptions::font)
            setAttribute(path, "/font/font-size", formattingOptionsMap, formattingOptions::fontSize)
            setAttribute(path, "/font/font-weight", formattingOptionsMap, formattingOptions::fontWeight)
            setAttribute(path, "/font/align-content", formattingOptionsMap, formattingOptions::textAlign)
            setNullableAttribute(path, "/padding", formattingOptionsMap, formattingOptions::padding)
            setNullableAttribute(path, "/margin", formattingOptionsMap, formattingOptions::margin)
            setNullableAttribute(path, "/radius", formattingOptionsMap, formattingOptions::borderRadius)

            return Optional.of(formattingOptions)
        }

        private fun getButton(
            path: String,
            buttonKey: String,
            page: Map<String, String?>
        ): Optional<CtaConfigRequest.ButtonOptions>? {
            val buttonMap = page.filterKeys { it.startsWith("$path$buttonKey") }
            if (buttonMap.isEmpty()) {
                return null
            }
            val button = CtaConfigRequest.ButtonOptions()
            setAttribute("$path$buttonKey", "/link-destination", buttonMap, button::destination)
            setAttribute("$path$buttonKey", "/display", buttonMap, button::active)

            setNullableAttribute("$path$buttonKey", "/text-color", buttonMap, button::color)
            setNullableAttribute("$path$buttonKey", "/hover-text-color", buttonMap, button::hoverColor)
            setNullableAttribute("$path$buttonKey", "/hover-background-color", buttonMap, button::hoverBackgroundColor)
            setNullableAttribute("$path$buttonKey", "/background-color", buttonMap, button::backgroundColor)
            setNullableAttribute("$path$buttonKey", "/button-text", buttonMap, button::name)
            setNullableAttribute("$path$buttonKey", "/button-text", buttonMap, button::imageName)
            setAttributeRenderType("$path$buttonKey", "/enable-image-cta", buttonMap, button::renderType)

            setNullableAttribute("$path$buttonKey", "/image-display-name", buttonMap, button::imageDisplayName)
            setNullableAttribute("$path$buttonKey", "/image-background-color", buttonMap, button::imageBackgroundColor)

            setNullableAttribute("$path$buttonKey", "/image", buttonMap, button::imageFileName)
            setNullableAttribute("$path$buttonKey", "/image-url", buttonMap, button::imageUrl)
            setNullableAttribute("$path$buttonKey", "/image-name", buttonMap, button::imageName)
            setNullableAttribute("$path$buttonKey", "/image-size", buttonMap, button::imageSize)

            return Optional.of(button)
        }

        private fun setAttribute(
            path: String,
            attributeName: String,
            page: Map<String, String?>,
            setter: KMutableProperty0<Optional<String>?>,
        ) {
            when {
                page.containsKey(path + attributeName) -> {
                    setter.set(Optional.ofNullable(page[path + attributeName]))
                }
            }
        }

        private fun setAttributeRenderType(
            path: String,
            attributeName: String,
            page: Map<String, String?>,
            setter: KMutableProperty0<Optional<String>?>,
        ) {
            if (page.containsKey(path + attributeName)) {
                val value = Optional.ofNullable(page[path + attributeName]).orElse("false")
                val boolValue = value?.equals("true", ignoreCase = true) ?: false

                setter.set(Optional.of(CtaMapperUtils.booleanToRenderType(boolValue)))
            }
        }

        private fun setAttributeWidth(
            path: String,
            attributeName: String,
            page: Map<String, String?>,
            setter: KMutableProperty0<Optional<String>?>,
        ) {
            val key = path + attributeName
            if (page.containsKey(key)) {
                var value = page[key]
                if(value.isNullOrBlank() || value == "null") {
                    value = "100%"
                }
                setter.set(Optional.of(value))
            }
        }

        private fun setNullableAttribute(
            path: String,
            attributeName: String,
            page: Map<String, String?>,
            setter: KMutableProperty0<Optional<String>?>,
        ) {
            val key = path + attributeName
            if (page.containsKey(key)) {
                val value = page[key]
                if(value.isNullOrBlank() || value == "null") {
                    setter.set(Optional.empty())
                } else {
                    setter.set(Optional.of(value))
                }
            }
        }
    }
}
