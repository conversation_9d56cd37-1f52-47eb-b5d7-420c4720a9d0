package com.carsaver.partner.configuration.ctaconfig.mappers

import com.carsaver.partner.configuration.ctaconfig.CtaConfigRequest
import com.carsaver.partner.configuration.ctaconfig.CtaConfigRequest.CtaPage
import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigFileUtils.Companion.overrideFileNameAndSanitize
import org.springframework.web.multipart.MultipartFile
import java.util.*

class CtaConfigFileMapper {

    companion object {

        /**
         * Processes uploaded image files for VDP (Vehicle Details Page) and VLP (Vehicle Listings Page) CTA buttons.
         *
         * This function:
         * 1. Validates that exactly 4 files are provided for VDP and at least 4 files for VLP
         * 2. Renames all files with a standardized format: "{dealerId}-{page}-cta-{index}.{extension}"
         * 3. Associates each file with its corresponding button in the CTA configuration
         * 4. Sets the imageFileName property on each button with the new filename
         * 5. Override the original file with the overrodeFile
         *
         * Note: if the page or button does not exist in the CTA configuration, it will be created.
         *
         * @param dealerId The dealer identifier used to prefix filenames
         * @param ctaConfig The CTA configuration object to be updated with file information
         * @param vdpFiles List of up to 4 files for Vehicle Details Page buttons
         * @param vlpFiles List of up to 4 files for Vehicle Listings Page buttons
         * @return A flattened list of all non-null MultipartFile objects after processing
         * @throws IllegalArgumentException If the number of provided files doesn't match expectations
         */
        fun injectFiles(
            dealerId: String,
            ctaConfig: CtaConfigRequest,
            vdpFiles: MutableList<MultipartFile?>,
            vlpFiles: MutableList<MultipartFile?>
        ): List<MultipartFile> {
            if(vdpFiles.size != 4) {
                throw IllegalArgumentException("Bad request we expect 4 files for vdp")
            }

            if(vlpFiles.size < 4) {
                throw IllegalArgumentException("Bad request we expect 4 files for vlp")
            }

            processPageButtons(ctaConfig, vdpFiles, isVdp = true, dealerId = dealerId)
            processPageButtons(ctaConfig, vlpFiles, isVdp = false, dealerId = dealerId)

            return listOfNotNull(vdpFiles, vlpFiles).flatten().filterNotNull()
        }

        private fun processPageButtons(ctaConfig: CtaConfigRequest, files: MutableList<MultipartFile?>, isVdp: Boolean, dealerId: String? = "") {
            val fileNamePrefix = isVdp
                .let { if (it) "vdp" else "vlp" }

            val buttonSelectors = listOf<(CtaPage) -> Optional<CtaConfigRequest.ButtonOptions>?>(
                { page -> page.primaryButton },
                { page -> page.secondButton },
                { page -> page.thirdButton },
                { page -> page.fourthButton }
            )

            val buttonSetters = listOf<(CtaPage, Optional<CtaConfigRequest.ButtonOptions>) -> Unit>(
                { page, btn -> page.primaryButton = btn },
                { page, btn -> page.secondButton = btn },
                { page, btn -> page.thirdButton = btn },
                { page, btn -> page.fourthButton = btn }
            )

            for (i in files.indices) {
                val multipartFile = files[i]

                if (multipartFile != null) {
                    getButtonReference(
                        ctaConfig,
                        buttonSelectors[i],
                        buttonSetters[i],
                        isVdp
                    ).ifPresent { button ->
                        //Save original name for display purposes
                        button.imageName = multipartFile.originalFilename?.let { Optional.of(it) }

                        //Use overrodeFile for BE processes, like saving to S3 and DB
                        val overrodeFile = overrideFileNameAndSanitize(multipartFile, i, dealerId!!, fileNamePrefix)
                        //Override original file with 'fileWithNewName'
                        files[i] = overrodeFile
                        button.imageFileName = overrodeFile.originalFilename?.let { Optional.of(it) }
                        button.imageSize = Optional.of(overrodeFile.size.toString())

                        //If file is provided, set the imageUrl to null
                        button.imageUrl = null
                    }
                }
            }
        }

        private fun getButtonReference(
            ctaConfig: CtaConfigRequest,
            buttonSelector: (CtaPage) -> Optional<CtaConfigRequest.ButtonOptions>?,
            buttonSetter: (CtaPage, Optional<CtaConfigRequest.ButtonOptions>) -> Unit,
            isVdp: Boolean
        ): Optional<CtaConfigRequest.ButtonOptions> {
            val pageOptional: Optional<CtaPage> = if (isVdp) {
                if (ctaConfig.vdp == null) {
                    Optional.of(CtaPage())
                } else {
                    ctaConfig.vdp!!
                }.also { ctaConfig.vdp = it }
            } else {
                if (ctaConfig.listings == null) {
                    Optional.of(CtaPage())
                } else {
                    ctaConfig.listings!!
                }.also { ctaConfig.listings = it }
            }

            val ctaPage = pageOptional.orElseThrow()!!
            val button: Optional<CtaConfigRequest.ButtonOptions> = if (buttonSelector(ctaPage) == null) {
                Optional.of(CtaConfigRequest.ButtonOptions())
            } else {
                buttonSelector(ctaPage)!!
            }

            button.also { buttonSetter(ctaPage, it) }

            return button
        }
    }
}
