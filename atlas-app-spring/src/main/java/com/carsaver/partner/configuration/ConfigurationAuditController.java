package com.carsaver.partner.configuration;

import com.carsaver.partner.model.configuration.QrCodeTransformAuditResponse;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@AllArgsConstructor
@RequestMapping("/api/configuration")
@RestController
public class ConfigurationAuditController {

    private ConfigurationAuditService configurationAuditService;

    @GetMapping("qrcode/audit/log")
    public ResponseEntity<QrCodeTransformAuditResponse> getQRCodeAuditLog(
        @RequestParam String dealerId, @RequestParam int page, @RequestParam int size, @RequestParam String pageName
    ) {
        var response = configurationAuditService.fetchPagedQRCodeAuditLogs(dealerId, page, size, pageName);
        return ResponseEntity.ok(response);
    }
}
