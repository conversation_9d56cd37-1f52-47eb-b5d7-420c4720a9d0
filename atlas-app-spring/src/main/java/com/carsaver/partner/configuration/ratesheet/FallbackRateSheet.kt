package com.carsaver.partner.configuration.ratesheet

data class FallbackRateSheet(
    val enableFallbackRates: Boolean? = null,
    val creditTierOne: CreditTier? = null,
    val creditTierTwo: CreditTier? = null,
    val creditTierThree: CreditTier? = null,
    val creditTierFour: CreditTier? = null,
    val terms: List<Term>? = null
) {
    data class CreditTier(
        val min: Int? = null,
        val max: Int? = null
    )

    data class Term(
        val term: String? = null,
        val enable: Boolean? = null,
        val order: Int? = null,
        val rates: List<Rate>? = null
    )

    data class Rate(
        val order: Int? = null,
        var stockTypeNew: Boolean? = null,
        var ageRange: AgeRange? = null,
        val tier1Rate: Double? = null,
        val tier2Rate: Double? = null,
        val tier3Rate: Double? = null,
        val tier4Rate: Double? = null
    )

    data class AgeRange(
        var min: String? = null,
        var max: String? = null
    )
}
