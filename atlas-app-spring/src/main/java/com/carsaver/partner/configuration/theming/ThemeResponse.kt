package com.carsaver.configuration.v2.api.theme



data class ThemeResponse (
     var topNavigationBar: TopNavigationBarResponse? = null,
     var buttonAndSectionHeaders: ButtonAndSectionHeadersResponse? = null,
     var accentColor: AccentColorResponse? = null,
     var primaryButton: CtaButtonResponse? = null,
     var secondaryButton: CtaButtonResponse? = null,
     var tertiaryButton: CtaButtonResponse? = null,
)
data class AccentColorResponse (
     var active: Boolean? = null,
     var color: String? = null,
)
class TopNavigationBarResponse (
     var active: Boolean? = null,
     var backgroundColor: String? = null,
     var textColor: String? = null,
     var cornerRadius: String? = null,
)

data class ButtonAndSectionHeadersResponse (
     var active: Boolean? = null,
      var cornerRadius: String? = null,
      var type: String? = null,
)

data class CtaButtonResponse (
     var active: Boolean? = null,
     var backgroundColor: String? = null,
     var borderColor: String? = null,
     var textColor: String? = null,
     var highlightRolloverBackgroundColor: String? = null,
     var highlightRolloverBorderColor: String? = null,
     var highlightRolloverTextColor: String? = null,
)
