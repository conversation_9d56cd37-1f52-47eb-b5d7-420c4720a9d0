package com.carsaver.partner.configuration.mydealpage

data class MyDealPageRequest (
    var paymentOptions: PaymentOptionsRequest? = null,
    var rebatesAndIncentives: RebatesAndIncentivesRequest? = null,
    var tradeIn: TradeInRequest? = null,
    var protectionProducts: ProtectionProductsRequest? = null,
    var accessories: AccessoriesRequest? = null,
    var financing: FinancingRequest? = null,
    var contractingAndDelivery: ContractingAndDeliveryRequest? = null,
    var uploadDocuments: UploadDocumentsRequest? = null,
    var appointment: AppointmentRequest? = null,
    var insuranceQuotes: InsuranceQuotesRequest? = null,
    var dealGuide: DealGuideRequest? = null,
)

data class PaymentOptionsRequest (
    var sectionTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null,
    var paymentMethodsDisplayed: List<String>? = null,
)

data class RebatesAndIncentivesRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null,
    var rebateCategories: List<String>? = null,
)

data class TradeInRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class ProtectionProductsRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class AccessoriesRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class FinancingRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class ContractingAndDeliveryRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class UploadDocumentsRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class AppointmentRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class InsuranceQuotesRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class CommonSectionRequest (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class DealGuideRequest (
    var secondaryCtaButton: CtaButton? = null,
    var tertiaryCtaButton: CtaButton? = null,
)

data class CtaButton (
    var active: Boolean? = null,
    var buttonText: String? = null,
    var linkDestination: String? = null,
)
