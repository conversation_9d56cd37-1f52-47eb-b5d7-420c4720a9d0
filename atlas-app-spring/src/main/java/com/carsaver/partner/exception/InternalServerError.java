package com.carsaver.partner.exception;

import com.carsaver.partner.model.protection_products.response.ErrorResponse;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class InternalServerError extends RuntimeException {
    public InternalServerError(ErrorResponse message) {
        super(message.getDeveloperMessage());
    }
}
