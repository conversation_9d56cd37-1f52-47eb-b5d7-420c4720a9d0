package com.carsaver.partner.repository;

import org.springframework.stereotype.Repository;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.ScanRequest;
import software.amazon.awssdk.services.dynamodb.paginators.ScanIterable;

import java.util.Objects;

@Repository
public class DynamoDbRepository {

    private DynamoDbClient dynamoDbClient;

    public DynamoDbRepository(DynamoDbClient dynamoDbClient) {
        this.dynamoDbClient = dynamoDbClient;
    }

    public ScanIterable getScanResponses(ScanRequest scanRequest) {
        return Objects.requireNonNullElse(dynamoDbClient.scanPaginator(scanRequest), new ScanIterable(dynamoDbClient, scanRequest));
    }
}
