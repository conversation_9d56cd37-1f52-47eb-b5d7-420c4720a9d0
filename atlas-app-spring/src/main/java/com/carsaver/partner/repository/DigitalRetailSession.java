package com.carsaver.partner.repository;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@DynamoDbBean
public class DigitalRetailSession {

    @NotBlank
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private String userId;

    @Valid
    private Long startTimestamp;

    private Long currentTimestamp;

    @Getter(onMethod_ = {@DynamoDbAttribute("dealerIds")})
    private Set<String> dealerIds;

    private Map<String,SessionDealerTracking> sessionDealerTracking;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @DynamoDbBean
    public static class SessionDealerTracking {
        private Long lastUsed;
    }
}
