package com.carsaver.partner.http;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * HttpExceptionResponse
 *
 * Response returned when an http request was unsuccessful.
 *
 * <AUTHOR>
 */
@Getter
public class HttpExceptionResponse {

    // the request
    private final HttpRequest request;

    // the response
    private final HttpResponse response;

    // the exception message
    private final String message;

    // the exception stack trace
    private final List<String> stackTrace;

    @JsonIgnore
    // the final status to return
    private final HttpStatus status;

    // constructor
    HttpExceptionResponse(HttpException exception) {
        this.request = exception.getRequest();
        this.response = exception.getResponse().orElse(null);
        this.message = exception.getMessage();
        this.stackTrace = Stream.of(exception.getStackTrace()).map(s -> s.toString()).collect(Collectors.toList());
        this.status = response == null ? HttpStatus.INTERNAL_SERVER_ERROR : response.getStatus();

        // remove the auth headers to prevent logging the token
        request.getHeaders().remove(HttpHeaders.AUTHORIZATION);

        if(response != null) {
            response.getHeaders().remove(HttpHeaders.AUTHORIZATION);
        }
    }

}
