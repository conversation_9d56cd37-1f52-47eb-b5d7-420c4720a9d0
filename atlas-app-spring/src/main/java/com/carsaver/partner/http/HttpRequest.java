package com.carsaver.partner.http;

import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * HttpRequest
 * <p>
 * Encapsulates an http request through fluent builder methods.
 * This is designed to be used with any http client behind the scenes, but having simple
 * access to all the fields that make up the request is very helpful for logging and exception handling.
 *
 * <AUTHOR>
 */
@Getter
public class HttpRequest {

    // static factory methods for GET requests
    public static HttpRequest get(String url) {
        return new HttpRequest(HttpMethod.GET, url, (Object[]) null);
    }

    public static HttpRequest get(String url, Object... pathParameters) {
        return new HttpRequest(HttpMethod.GET, url, pathParameters);
    }

    public static HttpRequest get(String url, String base) {
        return HttpRequest.get(url + base);
    }

    public static HttpRequest get(String url, String base, Object... pathParameters) {
        return HttpRequest.get(url + base, pathParameters);
    }

    // static factory methods for PUT requests
    public static HttpRequest put(String url) {
        return new HttpRequest(HttpMethod.PUT, url, (Object[]) null);
    }

    public static HttpRequest put(String url, Object... pathParameters) {
        return new HttpRequest(HttpMethod.PUT, url, pathParameters);
    }

    public static HttpRequest put(String url, String base) {
        return HttpRequest.put(url + base);
    }

    public static HttpRequest put(String url, String base, Object... pathParameters) {
        return HttpRequest.put(url + base, pathParameters);
    }

    // static factory methods for POST requests
    public static HttpRequest post(String url) {
        return new HttpRequest(HttpMethod.POST, url, (Object[]) null);
    }

    public static HttpRequest post(String url, Object... pathParameters) {
        return new HttpRequest(HttpMethod.POST, url, pathParameters);
    }

    public static HttpRequest post(String url, String base) {
        return HttpRequest.post(url + base);
    }

    public static HttpRequest post(String url, String base, Object... pathParameters) {
        return HttpRequest.post(url + base, pathParameters);
    }

    // static factory methods for PATCH requests
    public static HttpRequest patch(String url) {
        return new HttpRequest(HttpMethod.PATCH, url, (Object[]) null);
    }

    public static HttpRequest patch(String url, Object... pathParameters) {
        return new HttpRequest(HttpMethod.PATCH, url, pathParameters);
    }

    public static HttpRequest patch(String url, String base) {
        return HttpRequest.patch(url + base);
    }

    public static HttpRequest patch(String url, String base, Object... pathParameters) {
        return HttpRequest.patch(url + base, pathParameters);
    }

    // static factory methods for DELETE requests
    public static HttpRequest delete(String url) {
        return new HttpRequest(HttpMethod.DELETE, url, (Object[]) null);
    }

    public static HttpRequest delete(String url, Object... pathParameters) {
        return new HttpRequest(HttpMethod.DELETE, url, pathParameters);
    }

    public static HttpRequest delete(String url, String base) {
        return HttpRequest.delete(url + base);
    }

    public static HttpRequest delete(String url, String base, Object... pathParameters) {
        return HttpRequest.delete(url + base, pathParameters);
    }

    // the base url and path, without query parameters
    private final String baseUrl;

    // the content of the request
    private Map<String, HttpContent> contentMap;

    // the http method
    private final HttpMethod method;

    // the map of all headers
    private final Map<String, String> headers = new LinkedHashMap<>();

    // the map of all query parameters
    private final Map<String, List<String>> queryParams = new LinkedHashMap<>();

    // constructor
    private HttpRequest(HttpMethod method, String baseUrl, Object... pathParams) {
        this.method = method;
        this.baseUrl = String.format(baseUrl, encodePathParams(pathParams));
    }

    /**
     * Encodes path params before injecting them into the base url.
     *
     * @param pathParams
     * @return Object[]
     */
    private Object[] encodePathParams(Object... pathParams) {
        if (pathParams == null) {
            return null;
        }

        return Arrays.stream(pathParams).map(param -> urlEncode(String.valueOf(param))).toArray();
    }

    /**
     * Sets a query parameter.
     *
     * @param key
     * @param value
     * @return HttpRequest
     */
    public HttpRequest queryParam(String key, Object value) {
        if (value != null) {
            this.queryParams.put(key, List.of(String.valueOf(value)));
        }

        return this;
    }

    public HttpRequest queryParam(String key, List<String> value) {
        if (value != null) {
            this.queryParams.put(key, value);
        }

        return this;
    }

    /**
     * Sets multiple query parameters.
     *
     * @param queryParams
     * @return HttpRequest
     */
    public HttpRequest queryParams(Map<String, List<String>> queryParams) {
        this.queryParams.putAll(queryParams);
        return this;
    }

    /**
     * Sets query parameters from a pageable request.
     *
     * @param pageable
     * @return HttpRequest
     */
    public HttpRequest pageable(Pageable pageable) {
        queryParam("page", pageable.getPageNumber());
        queryParam("size", pageable.getPageSize());

        // construct sort params
        String sort = pageable.getSort().stream()
            .map(o -> o.getProperty() + "," + o.getDirection())
            .collect(Collectors.joining(";"));

        if (StringUtils.isNotBlank(sort)) {
            queryParam("sort", pageable.getPageSize());
        }

        return this;
    }

    /**
     * Sets a header.
     *
     * @param key
     * @param value
     * @return HttpRequest
     */
    public HttpRequest header(String key, String value) {
        this.headers.put(key, value);
        return this;
    }

    /**
     * Sets multiple headers.
     *
     * @param headers
     * @return HttpRequest
     */
    public HttpRequest headers(Map<String, String> headers) {
        this.headers.putAll(headers);
        return this;
    }

    /**
     * Sets a bearer token in the Authorization header.
     *
     * @param token
     * @return HttpRequest
     */
    public HttpRequest bearerToken(String token) {
        return header(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", token));
    }

    /**
     * Sets basic authorization in the Authorization header.
     *
     * @param username
     * @param password
     * @return HttpRequest
     */
    public HttpRequest basicAuth(String username, String password) {
        String value = username + ":" + password;
        String auth = Base64.getEncoder().encodeToString(value.getBytes());
        return header(HttpHeaders.AUTHORIZATION, String.format("Basic %s", auth));
    }

    /**
     * Sets the body content as text.
     *
     * @param key
     * @param value
     * @return HttpRequest
     */
    public HttpRequest ofText(String text) {
        HttpContent content = HttpContent.ofText(text);
        this.contentMap = Map.of(content.getType().getMimeType(), content);
        return this;
    }

    /**
     * Sets the body content as json.
     *
     * @param json
     * @return HttpRequest
     */
    public HttpRequest ofJson(String json) {
        HttpContent content = HttpContent.ofJson(json);
        this.contentMap = Map.of(content.getType().getMimeType(), content);
        return this;
    }

    /**
     * Sets the body content as json.
     *
     * @param object
     * @return HttpRequest
     */
    public HttpRequest ofJson(Object object) {
        HttpContent content = HttpContent.ofJson(object);
        this.contentMap = Map.of(content.getType().getMimeType(), content);
        return this;
    }

    /**
     * Sets the body content as form parameters.
     *
     * @param formParams
     * @return HttpRequest
     */
    public HttpRequest ofForm(Map<String, String> formParams) {
        HttpContent content = HttpContent.ofForm(formParams);
        this.contentMap = Map.of(content.getType().getMimeType(), content);
        return this;
    }

    /**
     * Sets the body content as a file.
     *
     * @param file
     * @return HttpRequest
     */
    public HttpRequest ofFile(MultipartFile file) {
        HttpContent content = HttpContent.ofFile(file);
        this.contentMap = Map.of(content.getType().getMimeType(), content);
        return this;
    }

    /**
     * Sets the body content as a mix of content types.
     *
     * @param contentMap
     * @return HttpRequest
     */
    public HttpRequest ofMixed(Map<String, HttpContent> contentMap) {
        this.contentMap = contentMap;
        return this;
    }

    /**
     * Returns true if the content is mixed.
     *
     * @return boolean
     */
    boolean isMixed() {
        return contentMap != null && contentMap.size() > 1;
    }

    /**
     * Gets the full url with query parameters, if any were provided.
     *
     * @return String
     */
    String getUrl() {
        return queryParams.isEmpty() ? baseUrl : queryParams.entrySet().stream().flatMap(
                e -> e.getValue().stream().map(single -> encodeQueryParam(e.getKey(), single)))
            .collect(Collectors.joining("&", baseUrl + "?", ""));
    }

    /**
     * Encodes a query parameter.
     *
     * @param entry
     * @return String
     */
    @SneakyThrows
    private String encodeQueryParam(String key, String value) {
        return key + "=" + urlEncode(value);
    }

    /**
     * Encodes a value using the url encoder.
     *
     * @param entry
     * @return value
     */
    @SneakyThrows
    private String urlEncode(String value) {
        return URLEncoder.encode(value, StandardCharsets.UTF_8);
    }

}
