package com.carsaver.partner.util;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.UpgradeStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CampaignConfigUtility {

    @Autowired
    UserClient userClient;

    public boolean isLeasePayoffEnabled(String userId) {
        UserView userView = userClient.findById(userId);
        if (userView != null && userView.getSource() != null) {
            CampaignView campaignView = userView.getCampaign();
            return Optional.ofNullable(campaignView)
                .map(CampaignView::getUpgradeStrategy)
                .map(UpgradeStrategy::getLeasePayoffCalculationEnabled)
                .orElse(false);
        }
        return false;
    }
}
