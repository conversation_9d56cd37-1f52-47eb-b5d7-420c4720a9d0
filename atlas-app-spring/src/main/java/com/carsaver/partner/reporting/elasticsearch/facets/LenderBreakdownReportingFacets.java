package com.carsaver.partner.reporting.elasticsearch.facets;

import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LenderBreakdownReportingFacets implements DocFacets {
    @Aggregate(field = "loanResponses.lenderName", uniqueField = "user.id")
    private List<String> loanResponses;
}
