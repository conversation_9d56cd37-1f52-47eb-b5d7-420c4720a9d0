package com.carsaver.partner.reporting.service;

import com.carsaver.partner.reporting.model.ActiveUsers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActiveUserService {

    @Autowired
    private GoogleAnalyticsReportingService googleAnalyticsReportingService;

    public List<ActiveUsers> getActiveUsers(List<String> dealerIds, String programId) {
        return List.of(ActiveUsers.builder()
            .todayCount(googleAnalyticsReportingService.getTodayActiveUsers(dealerIds, programId))
            .last30DayCount(googleAnalyticsReportingService.getThirtyDayActiveUsers(dealerIds, programId))
            .last60DayCount(googleAnalyticsReportingService.getSixtyDayActiveUsers(dealerIds, programId))
            .last90DayCount(googleAnalyticsReportingService.getNinetyDayActiveUsers(dealerIds, programId))
            .build());
    }
}
