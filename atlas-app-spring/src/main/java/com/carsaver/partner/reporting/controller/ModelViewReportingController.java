package com.carsaver.partner.reporting.controller;

import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.elasticsearch.facets.LeadDocReportingFacets;
import com.carsaver.partner.reporting.elasticsearch.facets.VehicleSaleDocReportingFacets;
import com.carsaver.partner.reporting.elasticsearch.service.LeadReportingService;
import com.carsaver.partner.reporting.elasticsearch.service.ModelViewReportingService;
import com.carsaver.partner.reporting.elasticsearch.service.VehicleSaleReportingService;
import com.carsaver.partner.reporting.model.ReportingRequest;
import com.carsaver.partner.reporting.model.VehicleModelMetric;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.model.ZonedDateRange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/reporting/model_view/{programId}")
public class ModelViewReportingController {

    @Autowired
    private ModelViewReportingService modelViewReportingService;

    @Autowired
    private LeadReportingService leadReportingService;

    @Autowired
    private VehicleSaleReportingService vehicleSaleReportingService;

    @Autowired
    private DateRangeHelper dateRangeHelper;

    @PostMapping("/model_metrics")
    public List<VehicleModelMetric> getVehicleModelMetrics(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var searchDateRange = dateRangeHelper.isDateRangeValidOrPastYear(reportingRequest.getDateRange());
        Map<String, VehicleModelMetric> metrics = new HashMap<>();

        syncLeadsMetrics(metrics, programId, reportingRequest, searchDateRange);
        syncAvgSalePricedMetrics(metrics, programId, reportingRequest, searchDateRange);
        syncAvgTradeValueMetrics(metrics, programId, reportingRequest, searchDateRange);
        syncAvgCreditScoreMetrics(metrics, programId, reportingRequest, searchDateRange);
        syncBuyAtHomeSalesMetrics(metrics, programId, reportingRequest, searchDateRange);
        syncCloseRateMetrics(metrics);

        List<VehicleModelMetric> mergedMetrics = new ArrayList<>();
        metrics.forEach((s, vehicleModelMetric) -> mergedMetrics.add(vehicleModelMetric));

        return mergedMetrics;
    }

    private void syncLeadsMetrics(Map<String, VehicleModelMetric> metrics, String programId, ReportingRequest reportingRequest, ZonedDateRange searchDateRange) {
        var leadsResult = leadReportingService.getLeadDocReportingFacet(programId, reportingRequest.getDealerIds(), LeadDocReportingFacets::getModels, searchDateRange);
        var leadsByModel = (List<TermFacet>) leadsResult.getResults();

        leadsByModel.forEach((facet) -> {
            var model = facet.getId();
            var count = facet.getUniqueCount();
            Optional.ofNullable(metrics.get(model))
                    .ifPresentOrElse(vehicleModelMetric -> {
                        vehicleModelMetric.setLeadsWithVOI(Optional.ofNullable(count).map(Long::intValue).orElse(0));
                    },
                    () -> {
                        var metric = VehicleModelMetric
                                .builder()
                                .modelName(model)
                                .leadsWithVOI(Optional.ofNullable(count).map(Long::intValue).orElse(0))
                                .build();
                        metrics.put(model,metric);
                    });
        });
    }

    private void syncAvgSalePricedMetrics(Map<String, VehicleModelMetric> metrics, String programId, ReportingRequest reportingRequest, ZonedDateRange searchDateRange) {
        var avgSalePricedResult = modelViewReportingService.getAvgSalePriceByModel(programId, reportingRequest.getDealerIds(), searchDateRange);
        var avgSalePricedByModel = (List<TermFacet>) avgSalePricedResult.getResults();

        avgSalePricedByModel.forEach((facet) -> {
            var model = facet.getId();
            var count = facet.getUniqueCount();
            Optional.ofNullable(metrics.get(model))
                    .ifPresentOrElse(vehicleModelMetric -> {
                        vehicleModelMetric.setAvgSalePrice(Optional.ofNullable(count).orElse(0L));
                    },
                    () -> {
                        var metric = VehicleModelMetric
                                .builder()
                                .modelName(model)
                                .avgSalePrice(Optional.ofNullable(count).orElse(0L))
                                .build();
                        metrics.put(model,metric);
                    });
        });
    }

    private void syncAvgTradeValueMetrics(Map<String, VehicleModelMetric> metrics, String programId, ReportingRequest reportingRequest, ZonedDateRange searchDateRange) {
        var avgPricedResult = modelViewReportingService.getAvgTradeValueByModel(programId, reportingRequest.getDealerIds(), searchDateRange);
        var avgPricedByModel = (List<TermFacet>) avgPricedResult.getResults();

        avgPricedByModel.forEach((facet) -> {
            var model = facet.getId();
            var count = facet.getUniqueCount();
            Optional.ofNullable(metrics.get(model))
                    .ifPresentOrElse(vehicleModelMetric -> {
                        vehicleModelMetric.setAvgTradeValue(Optional.ofNullable(count).orElse(0L));
                    },
                    () -> {
                        var metric = VehicleModelMetric
                                .builder()
                                .modelName(model)
                                .avgTradeValue(Optional.ofNullable(count).orElse(0L))
                                .build();
                        metrics.put(model,metric);
                    });
        });
    }

    private void syncAvgCreditScoreMetrics(Map<String, VehicleModelMetric> metrics, String programId, ReportingRequest reportingRequest, ZonedDateRange searchDateRange) {
        var avgPricedResult = modelViewReportingService.getAvgCreditScoreByModel(programId, reportingRequest.getDealerIds(), searchDateRange);
        var avgPricedByModel = (List<TermFacet>) avgPricedResult.getResults();

        avgPricedByModel.forEach((facet) -> {
            var model = facet.getId();
            var count = facet.getUniqueCount();
            Optional.ofNullable(metrics.get(model))
                    .ifPresentOrElse(vehicleModelMetric -> {
                        vehicleModelMetric.setAvgCreditScore(Optional.ofNullable(count).map(Long::intValue).orElse(0));
                    },
                    () -> {
                        var metric = VehicleModelMetric
                                .builder()
                                .modelName(model)
                                .avgCreditScore(Optional.ofNullable(count).map(Long::intValue).orElse(0))
                                .build();
                        metrics.put(model,metric);
                    });
        });
    }

    private void syncBuyAtHomeSalesMetrics(Map<String, VehicleModelMetric> metrics, String programId, ReportingRequest reportingRequest, ZonedDateRange searchDateRange){

        var salesResult = vehicleSaleReportingService.getVehicleSaleDocReportingFacet(programId, reportingRequest.getDealerIds(), VehicleSaleDocReportingFacets::getSalesCount, searchDateRange);
        var salesByModel = (List<TermFacet>) salesResult.getResults();
        var salesTotal = salesByModel.stream().mapToDouble(TermFacet::getCount).sum();

        salesByModel.forEach((facet) -> {
            var model = facet.getId();
            var count = facet.getCount();
            double percentage = new BigDecimal(count * 100).divide(BigDecimal.valueOf(salesTotal), 2, RoundingMode.HALF_UP).doubleValue();
            Optional.ofNullable(metrics.get(model))
                    .ifPresentOrElse(vehicleModelMetric -> {
                                vehicleModelMetric.setBuyAtHomeSales(Optional.ofNullable(count).map(Long::intValue).orElse(0));
                                vehicleModelMetric.setShareOfSales(percentage);
                            },
                            () -> {
                                var metric = VehicleModelMetric
                                        .builder()
                                        .modelName(model)
                                        .buyAtHomeSales(Optional.ofNullable(count).map(Long::intValue).orElse(0))
                                        .shareOfSales(percentage)
                                        .build();
                                metrics.put(model,metric);
                            });
        });
    }

    private void syncCloseRateMetrics(Map<String, VehicleModelMetric> metrics){
        metrics.forEach((s, vehicleModelMetric) -> {
            if (vehicleModelMetric.getLeadsWithVOI() <= 0) {
                return;
            }

            double percentage = new BigDecimal(vehicleModelMetric.getBuyAtHomeSales() * 100)
                    .divide(BigDecimal.valueOf(vehicleModelMetric.getLeadsWithVOI()), 2, RoundingMode.HALF_UP)
                    .doubleValue();

            vehicleModelMetric.setCloseRate(percentage);
        });
    }
}
