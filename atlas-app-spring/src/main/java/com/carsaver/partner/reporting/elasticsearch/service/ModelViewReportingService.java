package com.carsaver.partner.reporting.elasticsearch.service;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.model.reporting.DealsModelViewDoc;
import com.carsaver.partner.reporting.elasticsearch.criteria.ReportDealModelViewSearchCriteria;
import com.carsaver.partner.reporting.elasticsearch.facets.DealModelViewDocReportingFacets;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.FacetParser;
import de.cronn.reflection.util.PropertyUtils;
import de.cronn.reflection.util.TypedPropertyGetter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ModelViewReportingService extends CriteriaSearchHandler<DealsModelViewDoc> {


    @Autowired
    public ModelViewReportingService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    public FacetInfoResult getModelViewDocReportingFacet(String programId, List<String> dealerIds, TypedPropertyGetter<DealModelViewDocReportingFacets, ?> propertyGetter, ZonedDateRange dateRange) {
        var criteria = new ReportDealModelViewSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);
        criteria.setCreatedDate(dateRange);

        // type safe way to get the field name we want
        String facetName = PropertyUtils.getPropertyName(DealModelViewDocReportingFacets.class, propertyGetter);
        return this.facets(criteria, DealModelViewDocReportingFacets.class, facetName);
    }

    public FacetInfoResult getAvgSalePriceByModel(String programId, List<String> dealerIds, ZonedDateRange searchDateRange) {
        return getModelViewDocReportingFacet(programId, dealerIds, DealModelViewDocReportingFacets::getAvgSalePrice, searchDateRange );
    }

    public FacetInfoResult getAvgTradeValueByModel(String programId, List<String> dealerIds, ZonedDateRange searchDateRange) {
        return getModelViewDocReportingFacet(programId, dealerIds, DealModelViewDocReportingFacets::getAvgTradeValue, searchDateRange );
    }

    public FacetInfoResult getAvgCreditScoreByModel(String programId, List<String> dealerIds, ZonedDateRange searchDateRange) {
        return getModelViewDocReportingFacet(programId, dealerIds, DealModelViewDocReportingFacets::getAvgCreditScore, searchDateRange );
    }

    @Override
    protected String[] getSearchIndex() {
        return new String[]{"deals-model-view"};
    }
}
