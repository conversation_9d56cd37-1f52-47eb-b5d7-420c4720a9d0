package com.carsaver.partner.reporting.elasticsearch.criteria;

import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ReportModuleUsageSearchCriteria extends AbstractSearchCriteria {

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @RangeQuery
    private ZonedDateRange createdDate;

    @TermQuery(field = "dealerId")
    private List<String> dealerIds;

    @TermQuery(field = "programId")
    private String programId;
}
