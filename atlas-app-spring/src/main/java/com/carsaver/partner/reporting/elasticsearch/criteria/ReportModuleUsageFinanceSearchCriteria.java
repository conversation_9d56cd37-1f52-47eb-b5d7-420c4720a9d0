package com.carsaver.partner.reporting.elasticsearch.criteria;

import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ReportModuleUsageFinanceSearchCriteria extends AbstractSearchCriteria {

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @RangeQuery
    private ZonedDateRange createdDate;

    @TermQuery(field = "dealer.id")
    private List<String> dealerIds;

    @TermQuery(field = "user.program.id")
    private String programId;
}
