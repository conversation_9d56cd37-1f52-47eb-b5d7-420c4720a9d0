package com.carsaver.partner.reporting.controller;

import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.elasticsearch.facets.DealDocReportingFacets;
import com.carsaver.partner.reporting.elasticsearch.service.DealReportingService;
import com.carsaver.partner.reporting.model.AverageSaleOverTimeResponse;
import com.carsaver.partner.reporting.model.DealsOverTimeResponse;
import com.carsaver.partner.reporting.model.ReportingRequest;
import com.carsaver.search.support.FacetInfoResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/reporting")
public class DealReportingController {

    @Autowired
    private DealReportingService dealReportingService;

    @Autowired
    private DateRangeHelper dateRangeHelper;


    @PostMapping("/{programId}/deals/count")
    public DealsOverTimeResponse getDealsCount(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var dealsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getAllTimeRange();

        return dealReportingService.getCountByMonthWithinDateRange(programId, reportingRequest.getDealerIds(), DealDocReportingFacets::getDailyDealsCount, dealsDateRange);
    }

    @PostMapping("/{programId}/deals/salesAverage")
    public AverageSaleOverTimeResponse getDealsSalesAverage(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var dealsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getAllTimeRange();

        return dealReportingService.getSalesAverageByMonthWithinDateRange(programId, reportingRequest.getDealerIds(), dealsDateRange);
    }

    @PostMapping("/{programId}/deals/byDealType")
    public FacetInfoResult getDealsCountByDealType(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var dealsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getAllTimeRange();

        return dealReportingService.getCountByDealTypeWithinDateRange(programId, reportingRequest.getDealerIds(), DealDocReportingFacets::getDealType, dealsDateRange);
    }

    @PostMapping("/{programId}/deals/byTerm")
    public FacetInfoResult getDealsCountByTerm(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var dealsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getAllTimeRange();

        return dealReportingService.getCountByDealTermWithinDateRange(programId, reportingRequest.getDealerIds(), DealDocReportingFacets::getDealTerms, dealsDateRange);
    }

    @PostMapping("/{programId}/deals/byStockType")
    public FacetInfoResult getDealsCountByStockType(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var dealsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getAllTimeRange();

        return dealReportingService.getCountByDealStockTypeWithinDateRange(programId, reportingRequest.getDealerIds(), DealDocReportingFacets::getDealStockType, dealsDateRange);
    }

}
