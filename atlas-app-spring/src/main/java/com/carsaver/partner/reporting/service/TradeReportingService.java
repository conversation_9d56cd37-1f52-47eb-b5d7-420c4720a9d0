package com.carsaver.partner.reporting.service;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticFacetResponse;
import com.carsaver.elasticsearch.model.CertificateDoc;
import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.elasticsearch.facets.TradeReportingFacets;
import com.carsaver.partner.reporting.elasticsearch.criteria.TradeReportingSearchCriteria;
import com.carsaver.partner.reporting.model.ReportingRequest;
import com.carsaver.partner.reporting.model.TradeReport;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.FacetParser;
import org.apache.commons.math3.util.Precision;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Comparator;

import static java.util.stream.Collectors.toList;

@Service
public class TradeReportingService extends CriteriaSearchHandler<CertificateDoc> {
    @Autowired
    private DateRangeHelper dateRangeHelper;

    public TradeReportingService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    public TradeReport getTradeReport(String programId, ReportingRequest reportingRequest) {
        var dateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange()) ? reportingRequest.getDateRange() : dateRangeHelper.getAllTimeRange();

        var searchCriteria = TradeReportingSearchCriteria.builder()
            .createdDate(dateRange)
            .dealerIds(reportingRequest.getDealerIds())
            .programId(programId)
            .build();

        ElasticFacetResponse<TradeReportingFacets> response = searchWithResponse(searchCriteria, TradeReportingFacets.class);

        long totalCount = response.getSearchResponse().getHits().getTotalHits().value;
        long tradeCount = response.getFacets().getMakes().stream().mapToLong(TermFacet::getCount).sum();

        return TradeReport.builder()
            .tradeCount(tradeCount)
            .averageTradeValue(tradeCount > 0 ? response.getFacets().getAverageTradeValue().longValue() : 0)
            .tradePercentage(tradeCount > 0 ? calculatePercentage(tradeCount, totalCount) : 0)
            .tradeMakes(response
                .getFacets()
                .getMakes()
                .stream()
                .map(termFacet -> TradeReport.TradeMakes
                    .builder()
                    .make(termFacet.getName())
                    .count(termFacet.getCount())
                    .build())
                .sorted(Comparator.comparing(TradeReport.TradeMakes::getCount).reversed())
                .collect(toList()))
            .build();
    }

    @Override
    protected String[] getSearchIndex() {
        return new String[]{"certificates"};
    }

    private double calculatePercentage(double value, double total) {
        double percentage = Precision.round(value * 100 / total, 2);
        return Double.isNaN(percentage) ? 0 : percentage;
    }
}
