package com.carsaver.partner.reporting.controller;

import com.carsaver.partner.reporting.model.LenderBreakdownReport;
import com.carsaver.partner.reporting.model.ReportingRequest;
import com.carsaver.partner.reporting.service.LenderBreakdownReportingService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/reporting")
public class LenderBreakdownReportingController {

    private final LenderBreakdownReportingService lenderBreakdownReportingService;

    @PostMapping("/{programId}/lender-breakdown")
    public LenderBreakdownReport getLenderBreakdown(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        return lenderBreakdownReportingService.getLenderBreakdown(programId, reportingRequest);
    }
}
