package com.carsaver.partner.prequalification

import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import kotlin.streams.toList

@Component
@Profile("!e2e")
open class PreQualHistoryRepo(
    val repo: DynamoDbTable<PreQualHistoryDynamoRecord>
) {

    private val logger = LoggerFactory.getLogger(PreQualHistoryRepo::class.java)

    fun fetchHistoryRecordsByCustomerId(customerId: String): List<PreQualHistoryDynamoRecord> {
        val queryResults = repo.query(
            QueryConditional.keyEqualTo(
                Key.builder()
                    .partitionValue(customerId)
                    .build()
            )
        )
        return queryResults.items()?.stream()?.toList() ?: emptyList()
    }
}
