package com.carsaver.partner.extension

import com.carsaver.elasticsearch.ElasticProperties
import com.carsaver.elasticsearch.model.UserAndProspectDoc
import com.carsaver.partner.client.mapper.JsonMapper
import com.carsaver.partner.web.api.user.elasticsearch.DealerUserElasticSearchService
import com.fasterxml.jackson.core.type.TypeReference
import org.apache.http.HttpHost
import org.apache.http.client.CredentialsProvider
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.elasticsearch.action.search.SearchRequest
import org.elasticsearch.action.search.SearchResponse
import org.elasticsearch.client.RequestOptions
import org.elasticsearch.client.RestClient
import org.elasticsearch.client.RestHighLevelClient
import org.elasticsearch.index.query.QueryBuilder
import org.elasticsearch.index.query.QueryBuilders
import org.elasticsearch.search.builder.SearchSourceBuilder
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component


const val USERS_AND_PROSPECTS_INDEX_NAME: String = "users-and-prospects"

@Component
open class ChromeExtensionUsersIndexClient(
    @Qualifier("es-gibson-props") elasticProperties: ElasticProperties
) {

    private val client: RestHighLevelClient

    init {
        val lowLevelClientBuilder = RestClient.builder(
            *arrayOf(
                HttpHost(
                    elasticProperties.host,
                    elasticProperties.port,
                    elasticProperties.scheme
                )
            )
        )
        elasticProperties.credentials
            .ifPresent { credentials: CredentialsProvider? ->
                lowLevelClientBuilder.setHttpClientConfigCallback { builder: HttpAsyncClientBuilder ->
                    builder.setDefaultCredentialsProvider(
                        credentials
                    ).setKeepAliveStrategy(DefaultConnectionKeepAliveStrategy.INSTANCE)
                }
            }
        this.client = RestHighLevelClient(lowLevelClientBuilder)
    }

    open fun matchForDealer(
        request: MatchDetailsForDealerRequest
    ): List<UserAndProspectDoc> {
        val shouldClauses = mutableListOf<QueryBuilder>()

        if (!request.emails.isNullOrEmpty()) {
            shouldClauses.add(QueryBuilders.termsQuery("email", request.emails))
        }

        if (!request.phoneNumbers.isNullOrEmpty()) {
            shouldClauses.add(QueryBuilders.termsQuery("phoneNumber", request.phoneNumbers))
        }

        // Add match for each (firstName, lastName) pair
        val namePairs = request.names ?: emptyList()
        for (pair in namePairs) {
            val nameMatch = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("firstName", pair.firstName))
                .must(QueryBuilders.termQuery("lastName", pair.lastName))
            shouldClauses.add(nameMatch)
        }

        val innerShouldQuery = QueryBuilders.boolQuery()
        shouldClauses.forEach { innerShouldQuery.should(it) }
        innerShouldQuery.minimumShouldMatch(1)

        val boolQuery = QueryBuilders.boolQuery()
            .must(
                QueryBuilders.boolQuery()
                    .should(innerShouldQuery)
                    .minimumShouldMatch(1)
            )
            .must(QueryBuilders.termsQuery("dealerAcls", request.dealerId))
            .must(QueryBuilders.wildcardQuery("source.channel.keyword", "digital-retail"))

        val sourceBuilder = SearchSourceBuilder()
            .query(boolQuery)
            .size(10000)
            .fetchSource(DealerUserElasticSearchService.INCLUDES, null)

        val request = SearchRequest(USERS_AND_PROSPECTS_INDEX_NAME).source(sourceBuilder)
        val response: SearchResponse = client.search(request, RequestOptions.DEFAULT)

        val results = response.hits.map {
            JsonMapper.fromJson(it.sourceAsString, object : TypeReference<UserAndProspectDoc>() {})
        }

        return results
    }

}
