package com.carsaver.partner.extension

import com.carsaver.magellan.client.BasicUserAssociationClient
import com.carsaver.magellan.client.ProgramSubscriptionClient
import com.carsaver.magellan.model.AdfLeadComment
import com.carsaver.partner.config.rest.ApiException
import com.carsaver.partner.config.rest.CurrentUserInfo
import com.carsaver.partner.config.rest.UserType
import com.carsaver.partner.model.ProgramModel
import com.carsaver.partner.model.deal.CustomerDealSummaryRequest
import com.carsaver.partner.model.deal.TradeOfferModel
import com.carsaver.partner.model.retail.CustomerDealSummaryResponse
import com.carsaver.partner.model.user.UserTradeQuote
import com.carsaver.partner.reporting.service.ProgramService
import com.carsaver.partner.service.CustomerDealSummaryService
import com.carsaver.partner.service.CustomerTagService
import com.carsaver.partner.service.OnlineUsersService
import com.carsaver.partner.service.UserVehicleService
import com.carsaver.partner.service.crm.CrmService
import com.carsaver.partner.service.trade.DealerTradeQuoteService
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service

const val CUSTOMER_EDIT_PERMISSION = 21

@Service
open class ChromeExtensionService(
    private val onlineUsersService: OnlineUsersService,
    private val usersIndexClient: ChromeExtensionUsersIndexClient,
    private val basicUserAssociationClient: BasicUserAssociationClient,
    private val programService: ProgramService,
    private val programSubscriptionClient: ProgramSubscriptionClient,
    private val customerDealSummaryService: CustomerDealSummaryService,
    private val dealerTradeQuoteService: DealerTradeQuoteService,
    private val userVehicleService: UserVehicleService,
    private val customerTagService: CustomerTagService,
    private val crmService: CrmService
) {

    open fun matchForDealer(
        request: MatchDetailsForDealerRequest,
        currentUser: CurrentUserInfo
    ): List<ChromeExtensionModel> = runBlocking {
        checkPermissions(currentUser, request.dealerId)

        val users = usersIndexClient.matchForDealer(request)
        users.map { user ->
            async(IO) {
                val lastLoginDetails = onlineUsersService.buildCustomerLastLoginDetails(user)
                val tags = customerTagService.fetchCustomerTags(user.id, listOf(request.dealerId))
                ChromeExtensionModel(user, lastLoginDetails, tags)
            }
        }.awaitAll()
    }

    open fun getDealSummaryV2(
        dealerId: String,
        currentUser: CurrentUserInfo,
        userView: com.carsaver.magellan.model.UserView
    ): CustomerDealSummaryResponse {

        checkPermissions(currentUser, dealerId)

        return customerDealSummaryService.getDealSummaryV2(
            userView, CustomerDealSummaryRequest(listOf(dealerId))
        )
    }

    private fun checkPermissions(
        currentUser: CurrentUserInfo,
        dealerId: String?,
        editPermissionRequired: Boolean = false
    ) {
        if (currentUser.type != UserType.ADMIN && currentUser.type != UserType.PROGRAM) {
            val permission = basicUserAssociationClient.getBasicUserAssociation(dealerId, currentUser.callingUserId)
                .orElseThrow { throw ApiException.forbidden("Cannot represent dealerId provided.") }

            if (editPermissionRequired) {
                permission.permissions?.find { it == CUSTOMER_EDIT_PERMISSION }
                    ?: throw ApiException.forbidden("Unable to edit for dealerId.")
            }
        } else if (currentUser.type == UserType.PROGRAM) {
            val programs: List<ProgramModel> = programService.getPrograms(currentUser.callingUserId)
            val subscriptions = programSubscriptionClient.findByDealer(dealerId)
                .filter { it.isLive }

            subscriptions.find { subscription ->
                programs.any { program ->
                    subscription.programId == program.id
                }
            } ?: throw ApiException.forbidden("Cannot represent dealerId provided.")
        }
    }

    open fun addTradeQuote(
        dealerId: String,
        userTradeQuote: UserTradeQuote,
        currentUser: CurrentUserInfo
    ): TradeOfferModel {

        checkPermissions(currentUser, dealerId, true)

        val upsertDealerQuote = dealerTradeQuoteService.upsertDealerQuote(dealerId, userTradeQuote)

        return userVehicleService.getTradeOffer(
            upsertDealerQuote.userVehicleId,
            dealerId
        )
    }

    open fun sendToCrm(
        dealerId: String,
        userId: String,
        adfLeadComment: AdfLeadComment?,
        currentUser: CurrentUserInfo
    ) {
        checkPermissions(
            currentUser = currentUser,
            dealerId = dealerId,
            editPermissionRequired = true
        )

        crmService.sendToCrm(dealerId, userId, adfLeadComment)
    }

}
