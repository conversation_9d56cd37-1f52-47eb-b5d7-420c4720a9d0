package com.carsaver.partner.extension

import com.carsaver.magellan.model.AdfLeadComment
import com.carsaver.magellan.model.UserView
import com.carsaver.partner.config.rest.CurrentUser
import com.carsaver.partner.config.rest.CurrentUserInfo
import com.carsaver.partner.config.rest.ServiceApiMethod
import com.carsaver.partner.model.deal.TradeOfferModel
import com.carsaver.partner.model.retail.CustomerDealSummaryResponse
import com.carsaver.partner.model.user.UserTradeQuote
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@Validated
@CrossOrigin(origins = ["*"])
@RestController
open class ChromeExtensionController(
    private val service: ChromeExtensionService,
) {

    @ServiceApiMethod
    @PostMapping("/api/service/dealer-searches")
    open fun response(
        @Valid @RequestBody request: MatchDetailsForDealerRequest,

        @CurrentUser currentUser: CurrentUserInfo
    ): List<ChromeExtensionModel> {

        return service.matchForDealer(request, currentUser)
    }

    @ServiceApiMethod
    @GetMapping("/api/service/{user}/deal-summary")
    open fun getCustomerDealSummary(
        @PathVariable user: UserView,
        @RequestParam dealerId: String,
        @CurrentUser currentUser: CurrentUserInfo
    ): CustomerDealSummaryResponse {
        return service.getDealSummaryV2(dealerId, currentUser, user)
    }

    @ServiceApiMethod
    @PostMapping("/api/service/dealer/{dealerId}/vehicle/quote")
    open fun addTradeQuote(
        @PathVariable dealerId: String,
        @RequestBody userTradeQuote: UserTradeQuote,
        @CurrentUser currentUser: CurrentUserInfo
    ): ResponseEntity<TradeOfferModel> {
        return ResponseEntity.ok(service.addTradeQuote(dealerId, userTradeQuote, currentUser))
    }

    @ServiceApiMethod
    @PostMapping("/api/service/send-to-crm")
    open fun sendToCrm(
        @RequestParam userId: String,
        @RequestParam dealerId: String,
        @RequestBody(required = false) adfLeadComment: AdfLeadComment?,
        @CurrentUser currentUser: CurrentUserInfo
    ): ResponseEntity<Any> {
        service.sendToCrm(
            currentUser = currentUser,
            dealerId = dealerId, userId = userId,
            adfLeadComment = adfLeadComment
        )
        return ResponseEntity.noContent().build()
    }


}
