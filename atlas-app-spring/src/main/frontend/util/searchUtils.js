import dot from "dot-object";
import _ from "lodash";
import parse from "url-parse";
import api from "./api";
import loader from "./loader";
import Vue from "vue";
import { Payload } from "vuex-pathify";
import lodashGet from "lodash/get";
import { compressUrl } from "Util/reduceUrlForExport";

const defaultSearchOpts = {
    resetToFirstPage: true,
};

const sortableFacets = ["vehicleOfInterestMakes", "vehicleOfInterestModels", "vehicleOfInterestYears"];

export const QueryModeEnum = {
    PAGE: 1,
    SCROLL: 2,
    STATS: 3,
};

export const FACET_TYPE = Object.freeze({
    SINGLE: Symbol("single"),
    MULTIPLE: Symbol("multiple"),
});

const normalizeFilters = (objFilters) => {
    let normFilters = {};

    if (typeof objFilters === "object") {
        _.forEach(objFilters, (value, key) => {
            if (Object.prototype.hasOwnProperty.call(objFilters, key)) {
                if (value) {
                    /*
                     * Map transforms only number values to number type
                     * ex: '123' => 123
                     *     'abc' => 'abc'
                     */
                    const valueArray = _.split(value, ",").map((v) => {
                        return isNaN(v) ? v : Number(v);
                    });
                    if (valueArray.length > 1) {
                        objFilters[key] = valueArray;
                    } else {
                        if (key == "miles.start") {
                            objFilters.miles = {
                                start: objFilters[key],
                            };
                        } else if (key == "miles.end") {
                            objFilters.miles.end = objFilters[key];
                        } else if (isRangedFilter(key)) {
                            const term = key.split(".")[0];
                            const postfix = key.split(".")[1];

                            objFilters[term] = {
                                ...objFilters[term],
                                [postfix]: value,
                            };
                        } else {
                            objFilters[key] = value;
                        }
                    }
                }
            }
        });
        normFilters = {
            ...objFilters,
        };
    } else {
        console.trace("Error expecting type object instead received: ", objFilters);
        return objFilters;
    }

    normFilters = pruneDuplicateObjectKeys(normFilters, rangeFilterKeys);
    return normFilters;
};

const resetToPage = ({ commit, state }, page) => {
    const newPageable = {};
    Object.assign(newPageable, state.pageable, {
        page: page,
    });

    // NOTE: updating to first page will trigger a new search
    commit("SET_PAGEABLE", newPageable);
};

export const filterNameTransforms = {
    withinMaturityDate: "Finance Maturity Date",
    tradePayments: "Current Payment",
    tradeTerms: "Financing Term",
    tradePaymentType: "Trade Type",
};

export const rangedFilters = Object.freeze([
    { key: "tradeTerms", prefix: null, suffix: "Mo" },
    { key: "tradePayments", prefix: "$", suffix: null },
    { key: "remainingPayments", prefix: "$", suffix: null },
    { key: "tradeEquity", prefix: "$", suffix: null },
    { key: "tradeValues", prefix: "$", suffix: null },
    { key: "tradeMileages", prefix: "", suffix: null },
]);

const rangeFilterKeys = rangedFilters.map((filter) => filter.key);

/**
 * Converts a snake_case string to Title Case.
 * @param {string} text - The snake_case string to be converted.
 * @returns {string} - The converted Title Case string.
 */
export const snakeToTitleCase = (text) => {
    return text
        .toLowerCase()
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.substring(1))
        .join(" ");
};

export const convertToTitleCase = (str) => {
    // Replace non-alphanumeric delimiters with spaces and split into words
    return str
        .replace(/([a-z])([A-Z])/g, "$1 $2") // Handle camelCase
        .replace(/[_\-]+/g, " ") // Replace underscores and hyphens with spaces
        .toLowerCase() // Convert entire string to lowercase
        .split(" ") // Split by spaces
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
        .join(" "); // Join words with a space
};

export const isRangedFilter = (key) => {
    return key && rangeFilterKeys.some((term) => key?.includes(term));
};

const pruneDuplicateObjectKeys = (obj, keys) => {
    const result = {};
    const termsSet = new Set(keys);

    Object.keys(obj).forEach((key) => {
        // Check if the key is one of the terms to keep as-is
        if (termsSet.has(key)) {
            result[key] = obj[key];
            return;
        }

        // Check if the key is a pattern we need to remove
        const [base, suffix] = key.split(".");
        if (!suffix || !termsSet.has(base)) {
            result[key] = obj[key];
        }
    });

    return result;
};

/**
 * We remove any empty array and null objects from the graph. Will also convert nested var
 */
const filtersToQuery = (filters) => {
    // split up the array filters from other object filters
    const arrayFilters = _.omitBy(filters, (value) => !_.isArray(value) || (_.isArray(value) && _.isEmpty(value)));
    const nonArrayFilters = _.omitBy(filters, (value) => _.isArray(value) || _.isNil(value));
    // use dot to convert object graphs to some.property and then filter out nil values again
    const nonArrayDotFilters = _.omitBy(dot.dot(nonArrayFilters), (value) => _.isNil(value));

    return { ...arrayFilters, ...nonArrayDotFilters };
};

const searchMethodsToQuery = (searchMethods) => {
    const negativeSearchMethods = [];

    _.each(searchMethods, (searchMethod, facetName) => {
        if (searchMethod === "NEGATIVE") {
            negativeSearchMethods.push(facetName);
        }
    });

    if (_.size(negativeSearchMethods) === 0) {
        return null;
    }

    return { negativeSearchMethods };
};

/**
 * This will parse from the querystring the sort and page values and return them while parsing the page value to an int
 * @param defaultPageable The default pageable values to use when trying to parse them from the querystring
 */
const parsePageableFromUrl = (defaultPageable) => {
    const url = parse(window.location.href, true);
    const newPageable = {};
    const queryPageable = _.pick(queryToFilters(defaultPageable, url.query), ["page", "sort"]);
    Object.assign(newPageable, defaultPageable, queryPageable);
    newPageable.page = parseInt(newPageable.page);
    return newPageable;
};

const parseSearchMethodsFromUrl = () => {
    const url = parse(window.location.href, true);
    const negativeSearchMethods = _.get(url.query, "negativeSearchMethods");
    if (_.isNil(negativeSearchMethods)) {
        return {};
    }

    const negativeSearchMethodsArr = _.split(negativeSearchMethods, ",");

    const searchMethods = {};

    _.each(negativeSearchMethodsArr, (facetName) => {
        searchMethods[facetName] = "NEGATIVE";
    });

    return searchMethods;
};

/**
 * This will parse from the query string any of the defined defaultFilters passed in
 * @param filters The set of filters that will be parsed from the querystring
 */
const parseFiltersFromUrl = (filters) => {
    const url = parse(window.location.href, true);
    const newFilters = {};
    const queryFilters = queryToFilters(filters, url.query, ["page", "sort", "reset", "negativeSearchMethods"]);
    Object.assign(newFilters, filters, queryFilters);
    return normalizeFilters(newFilters);
};

const pushWindowHistory = (title, filters, pageable, searchMethods) => {
    const url = parse(window.location.href, true);
    const currentUrl = url.toString().substr(url.toString().indexOf(url.pathname));
    const cleanFilters = filtersToQuery(filters);
    const negativeSearchMethods = searchMethodsToQuery(searchMethods);

    url.query = {
        ...cleanFilters,
        ...negativeSearchMethods,
        ...pageable,
        reset: true,
    };
    const newUrl = url.toString().substr(url.toString().indexOf(url.pathname));
    if (currentUrl !== newUrl) {
        const data = {
            filters: cleanFilters,
            pageable: pageable,
        };

        if (_.size(negativeSearchMethods) > 0) {
            data.negativeSearchMethods = negativeSearchMethods;
        }

        window.history.pushState(data, title, newUrl);
    }
};

const queryToFilters = (filters, query, excludeParams = []) => {
    const newQuery = {};

    function splitArrayItems(value, queryItem, filterValue) {
        if (value !== "") {
            newQuery[queryItem] = _.split(value, ",");
        } else {
            newQuery[queryItem] = filterValue;
        }
    }

    function handleStringValue(value, queryItem) {
        if (value === "null") {
            newQuery[queryItem] = null;
        } else {
            newQuery[queryItem] = value;
        }
    }

    _.forEach(query, (value, queryItem) => {
        if (!_.includes(excludeParams, queryItem)) {
            const filterValue = _.get(filters, queryItem, null);
            if (_.isArray(filterValue)) {
                splitArrayItems(value, queryItem, filterValue);
            } else {
                if (value !== "") {
                    handleStringValue(value, queryItem);
                } else {
                    newQuery[queryItem] = filterValue;
                }
            }
        }
    });

    return newQuery;
};

const clearAllFilters = (filters) => {
    const newQuery = {};
    let normNewQuery = {};

    if (!_.isNil(filters)) {
        _.forEach(filters, (value, filterItem) => {
            //Persist dealerIds and programIds filter
            if (filterItem === "dealerIds" || filterItem === "programIds" || filterItem === "programs") {
                newQuery[filterItem] = value;
            }
        });
    }

    normNewQuery = normalizeFilters(newQuery);

    return normNewQuery;
};

// @param Object originalFilters {}
// @param Array newFilterSet is an array of filter objects with an optional searchMethod property [{},{},...]
// @return returns the updated filters object {}
const updateFilters = (originalFilters, newFilterSet) => {
    const results = { ...originalFilters };

    newFilterSet.forEach((value) => {
        results[value.filterName] = value.facetId;

        if (_.get(value, "searchMethod")) {
            Vue.set(store.searchMethods, value.filterName, value.searchMethod);
        }
    });

    return results;
};

const apiSearch = (uri, commit, state) => {
    const pageSize = _.get(state.pageable, "size", 20);

    const body = { ...state.filters };
    body.searchMethods = state.searchMethods;
    body.includes = _.get(state, "fetchSource.includes", null);
    body.excludes = _.get(state, "fetchSource.excludes", null);

    let apiPostUri;
    switch (state.queryMode) {
        case QueryModeEnum.SCROLL:
            apiPostUri = `${uri}/scroll`;
            break;
        case QueryModeEnum.STATS:
            apiPostUri = `${uri}/stats`;
            break;
        default:
            apiPostUri = `${uri}/search?page=${state.pageable.page}&sort=${state.pageable.sort}&size=${pageSize}`;
    }

    return api
        .post(apiPostUri, body)
        .then((response) => {
            if (state.initialLoad) {
                commit("SET_INITIAL_LOAD", false);
            }
            commit("SET_SEARCH_LOADER", {
                data: response.data.content,
                loader: loader.successful(),
            });
            commit("SET_PAGE_METADATA", response.data.pageMetadata);

            const newFacets = {};
            Object.assign(newFacets, state.facets, response.data.facets);
            commit("SET_FACETS", newFacets);

            const totalPages = _.get(response.data, "pageMetadata.totalPages", 1);
            if (state.pageable.page > totalPages) {
                const newPageable = {};
                Object.assign(newPageable, state.pageable, {
                    page: totalPages,
                });

                commit("SET_PAGEABLE", newPageable);
            }

            return response;
        })
        .catch((error) => {
            if (state.initialLoad) {
                commit("SET_INITIAL_LOAD", false);
            }

            commit("SET_SEARCH_LOADER", {
                data: [],
                loader: loader.error(error),
            });
        });
};

const reloadFacets = (uri, commit, state) => {
    for (const facet in state.facets) {
        const facetLoaded = _.get(state.facets[facet], "loader.isComplete", false);
        const facetData = _.get(state.facets[facet], "data", []);
        if (facetLoaded) {
            commit("SET_FACET", {
                facetName: facet,
                loader: loader.started(),
                data: facetData,
            });

            const body = { ...state.filters };
            body.searchMethods = state.searchMethods;

            api.post(`${uri}?id=${facet}`, body).then((response) => {
                commit("SET_FACET", {
                    facetName: facet,
                    loader: loader.successful(),
                    data: response.data.results,
                });
            });
        }
    }
};

const loadFacet = (uri, commit, state, facetName) => {
    commit("SET_FACET", {
        facetName: facetName,
        loader: loader.started(),
        data: [],
    });

    const body = { ...state.filters };
    body.searchMethods = state.searchMethods;

    api.post(`${uri}?id=${facetName}`, body).then((response) => {
        commit("SET_FACET", {
            facetName: facetName,
            loader: loader.successful(),
            data: response.data.results,
        });
    });
};

const setFacet = (state, { facetName, loader, data }) => {
    const facetInfo = {
        loader: loader,
        data: data,
    };
    if (_.isNil(state.facets)) {
        Vue.set(state, "facets", {});
    }
    Vue.set(state.facets, facetName, facetInfo);
};
const setFilterNames = (state, { filterNames, loader, data }) => {
    const filterNamesData = {
        loader: loader,
        data: data,
    };
    if (_.isNil(state.filterNames)) {
        Vue.set(state, "filterNames", {});
    }
    Vue.set(state.filterNames, filterNames, filterNamesData);
};

const mutations = () => {
    return {
        SET_FILTER_NAMES: setFilterNames,
        SET_FACET: setFacet,
        SET_FILTERS: (state, value) => {
            if (value instanceof Payload) {
                Vue.set(state.filters, value.path, value.value);
            } else {
                state.filters = value;
            }
        },
        ADD_POSITIVE_FILTER(store, { filterName, facetId, facetType = FACET_TYPE.SINGLE }) {
            if (_.get(store.searchMethods, "filterName") === "NEGATIVE") {
                // If switching search method, clear filters first
                Vue.set(store.filters, filterName, []);
            }

            Vue.set(store.searchMethods, filterName, "POSITIVE");
            if (facetType === FACET_TYPE.MULTIPLE) {
                if (_.isArray(store.filters[filterName])) {
                    store.filters[filterName].push(facetId);
                } else if (!_.isNil(store.filters[filterName])) {
                    Vue.set(store.filters, filterName, [store.filters[filterName], facetId]);
                } else {
                    Vue.set(store.filters, filterName, [facetId]);
                }
            } else {
                Vue.set(store.filters, filterName, facetId);
            }
        },
        ADD_NEGATIVE_FILTER(store, { filterName, facetId, facetType = FACET_TYPE.SINGLE }) {
            if (_.get(store.searchMethods, "filterName") === "POSITIVE") {
                // If switching search method, clear filters first
                Vue.set(store.filters, filterName, []);
            }

            Vue.set(store.searchMethods, filterName, "NEGATIVE");
            if (facetType === FACET_TYPE.MULTIPLE) {
                if (_.isArray(store.filters[filterName])) {
                    store.filters[filterName].push(facetId);
                } else if (!_.isNil(store.filters[filterName])) {
                    Vue.set(store.filters, filterName, [store.filters[filterName], facetId]);
                } else {
                    Vue.set(store.filters, filterName, [facetId]);
                }
            } else {
                Vue.set(store.filters, filterName, facetId);
            }
        },
        UPDATE_FILTER(store, { filterName, updatedValue }) {
            Vue.set(store.filters, filterName, updatedValue);
        },
        REMOVE_FILTER(store, { filterName, facetId }) {
            Vue.set(store.filters, filterName, _.without(store.filters[filterName], facetId));

            if (store.filters[filterName].length === 0) {
                Vue.set(store.filters, filterName, null);
                Vue.set(store.searchMethods, filterName, "NEGATIVE");
            }
        },
        CLEAR_FILTER(store, filterName) {
            Vue.delete(store.filters, filterName);
            Vue.delete(store.searchMethods, filterName);
            if (filterName === "miles") {
                Vue.delete(store.filters, "miles.start");
                Vue.delete(store.filters, "miles.end");
            }
        },
        SET_SEARCH_LOADER_STARTED(store) {
            Vue.set(store.searchLoader, "loader", loader.started());
        },
        SET_PAGE(store, newPage) {
            Vue.set(store.pageable, "page", newPage);
        },
        SET_SORT(store, newSort) {
            Vue.set(store.pageable, "sort", newSort);
        },
    };
};

const doSearch = ({ commit, state }, opts, uriRoot, title) => {
    const _opts = Object.assign({}, defaultSearchOpts, opts);
    const currentPage = _.get(state.pageable, "page", 1);

    if (_opts.resetToFirstPage === true && currentPage > 1) {
        // resetting the page should result in a actions.doPageLoad invocation
        // via the *List.vue component's pageable.page watch function
        resetToPage({ commit, state }, 1);
    }

    commit("SET_SEARCH_LOADER_STARTED");

    const pushHistoryEnabled = _.get(state, "pushHistoryEnabled", true);
    if (pushHistoryEnabled) {
        pushWindowHistory(title, state.filters, state.pageable, state.searchMethods);
    }

    const apiPath = _.isNil(state.searchUri) ? uriRoot : state.searchUri;
    apiSearch(`${apiPath}`, commit, state);
    reloadFacets(`${apiPath}/facet_info`, commit, state);
};

const isFilterLoadedInSession = (state, filterName, filters) => {
    const isFilterInSession = (filterList, filters) => {
        if (_.isArray(filters)) {
            let filterExists = true;
            _.forEach(filters, (value) => {
                if (!filterList.some((f) => _.toString(f.id) === _.toString(value))) {
                    filterExists = false;
                    return false;
                }
            });
            return filterExists;
        } else {
            return filterList.some((f) => _.toString(f.id) === _.toString(filters));
        }
    };

    return (
        !_.isNil(state.filterNames) &&
        !_.isNil(state.filterNames[filterName]) &&
        state.filterNames[filterName].loader.isComplete === true &&
        isFilterInSession(state.filterNames[filterName].data, filters[filterName])
    );
};

const actions = (uriRoot, title) => {
    return {
        setSearchUri({ commit, state }, searchUri) {
            commit("SET_SEARCH_URI", searchUri);
        },
        pushHistoryEnabled({ commit, state }, enableFlag) {
            commit("SET_PUSH_HISTORY_ENABLED", enableFlag);
        },
        loadFacetInfo({ commit, state }, facetName) {
            const apiPath = _.isNil(state.searchUri) ? uriRoot : state.searchUri;
            loadFacet(`${apiPath}/facet_info`, commit, state, facetName);
        },
        loadFilterInfo({ state, dispatch }) {
            if ("object" === typeof state.filters && Object.keys(state.filters).length > 0) {
                _.forEach(state.filters, (value, key) => {
                    if (Object.prototype.hasOwnProperty.call(state.filters, key)) {
                        if (value) {
                            let payload = {};
                            const valueArray = _.split(value, ",");
                            if (valueArray.length > 1) {
                                payload[key] = valueArray;
                            } else {
                                payload[key] = value;
                            }

                            if (state?.pills[key]?.enabled === false) {
                                return;
                            }

                            if (!isFilterLoadedInSession(state, key, payload)) {
                                dispatch("loadFilter", { filterNames: key, payload });
                            }
                        }
                    }
                });
            }
        },
        loadFilter({ commit, state }, { filterNames, payload }) {
            filterNames = state?.pills[filterNames]?.facet ? state?.pills[filterNames]?.facet : filterNames;

            const apiPath = _.isNil(state.searchUri) ? uriRoot : state.searchUri;
            const uri = `${apiPath}/facets/${filterNames}/filter_info`;
            commit("SET_FILTER_NAMES", {
                filterNames: filterNames,
                loader: loader.started(),
                data: [],
            });

            const body = { ...payload };

            api.post(`${uri}`, body).then((response) => {
                commit("SET_FILTER_NAMES", {
                    filterNames: filterNames,
                    loader: loader.successful(),
                    data: response.data.results,
                });
            });
        },
        queryModeScroll({ commit }) {
            commit("SET_QUERY_MODE", QueryModeEnum.SCROLL);
        },
        queryModePage({ commit }) {
            commit("SET_QUERY_MODE", QueryModeEnum.PAGE);
        },
        /**
         * invokes doSearch for whatever the current sort is, will start current search over from first page of results
         */
        doSort({ commit, state }) {
            doSearch({ commit, state }, { resetToFirstPage: true }, uriRoot, title);
        },
        /**
         *
         * @param opts - opts defaults -> resetToFirstPage: true
         * intended to serve as the handler for explicit user initiated searches, i.e. clicking the 'Search' button
         * by default will start current search over from first page of results
         */
        doSearch({ commit, state }, opts) {
            doSearch({ commit, state }, opts, uriRoot, title);
        },
        /**
         * invokes doSearch for whatever the current page is
         */
        doPageLoad({ commit, state }) {
            doSearch({ commit, state }, { resetToFirstPage: false }, uriRoot, title);
        },
        loadMetadata({ commit, state }) {
            const body = { ...state.filters };
            body.searchMethods = state.searchMethods;

            const apiPath = _.isNil(state.searchUri) ? uriRoot : state.searchUri;
            return api.post(`${apiPath}/metadata`, body).then((response) => {
                commit("SET_PAGE_METADATA", response.data);
            });
        },
        doExport({ state }, reportName = "report.csv") {
            const url = parse(window.location.pathname + "/" + reportName, true);
            const cleanFilters = filtersToQuery(state.filters);
            const negativeSearchColumns = _.chain(state.searchMethods)
                .map((method, column) => ({
                    method,
                    column,
                }))
                .filter(["method", "NEGATIVE"])
                .map("column")
                .value();

            url.query = {
                ...cleanFilters,
                negativeSearchColumns,
                columns: state.exportFields,
                labels: state.exportLabels,
                sort: state.pageable.sort,
            };

            window.location = url.toString();
        },
        doExportReduced({ state }, reportName = "report.csv") {
            const url = parse(window.location.pathname + "/" + reportName, true);
            const cleanFilters = filtersToQuery(state.filters);
            const negativeSearchColumns = _.chain(state.searchMethods)
                .map((method, column) => ({
                    method,
                    column,
                }))
                .filter(["method", "NEGATIVE"])
                .map("column")
                .value();
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            url.query = {
                ...cleanFilters,
                negativeSearchColumns,
                columns: state.exportFields,
                labels: state.exportLabels,
                sort: state.pageable.sort,
                timezone: timezone,
            };

            window.location = compressUrl(url.toString());
        },
        clearFilters({ commit, state, dispatch }) {
            commit("SET_SEARCH_METHODS", {});
            commit("SET_FILTERS", clearAllFilters(state.filters));

            dispatch("doSearch");
        },
        addPositiveFilter(
            { commit, state, dispatch },
            { filterName, facetId, facetType = FACET_TYPE.SINGLE, clear = false }
        ) {
            if (clear) {
                commit("CLEAR_FILTER", filterName);
            }

            commit("ADD_POSITIVE_FILTER", { filterName, facetId, facetType });

            dispatch("doSearch");
        },
        removePositiveFilter({ commit, state, dispatch }, { filterName, facetId }) {
            commit("REMOVE_POSITIVE_FILTER", { filterName, facetId });

            dispatch("doSearch");
        },
        addNegativeFilter({ commit, dispatch }, { filterName, facetId, facetType = FACET_TYPE.SINGLE }) {
            commit("ADD_NEGATIVE_FILTER", { filterName, facetId, facetType });

            dispatch("doSearch");
        },
        updateFilter({ commit, dispatch }, { filterName, updatedValue }) {
            commit("UPDATE_FILTER", { filterName, updatedValue });

            dispatch("doSearch");
        },
        removeFilter({ commit, dispatch }, { filterName, facetId }) {
            commit("REMOVE_FILTER", { filterName, facetId });

            dispatch("doSearch");
        },
        clearFilter({ commit, dispatch }, filterName) {
            commit("CLEAR_FILTER", filterName);

            dispatch("doSearch");
        },
        changePage({ commit, dispatch }, newPage) {
            commit("SET_PAGE", newPage);

            dispatch("doPageLoad");
        },
        updateSort({ commit, dispatch }, sort) {
            commit("SET_SORT", sort);

            dispatch("doSort");
        },
    };
};

const getters = () => {
    return {
        getFiltersByName: (state) => (filterName) => {
            return state.filters[filterName];
        },
        getFacetsByName: (state) => (facetName) => {
            const f = _.get(state.facets[facetName], "data");

            if (f) {
                if (_.isNil(f)) {
                    return [];
                } else if (_.isArray(f)) {
                    if (sortableFacets.includes(facetName)) {
                        return _.sortBy(f, [(facet) => facet.name?.toLowerCase()]);
                    }
                    return f;
                } else {
                    const available = [];
                    _.each(f, (modelList, makeName) => {
                        _.each(modelList, (model) => {
                            available.push({
                                id: model.id,
                                name: model.name,
                                count: model.count,
                                makeName,
                            });
                        });
                    });

                    return _.sortBy(available, [(facet) => facet.name?.toLowerCase()]);
                }
            }

            return [];
        },
        getFacetsRawValueByName: (state) => (facetName) => {
            return lodashGet(state.facets[facetName], "data", null);
        },
        getSortBy: (state) => {
            const sortSplit = state.pageable?.sort?.split(",");
            return sortSplit[0];
        },
        getSortDesc: (state) => {
            const sortSplit = state.pageable?.sort?.split(",");
            return sortSplit[1] === "desc";
        },
    };
};

const initialState = () => {
    return {
        searchMethods: parseSearchMethodsFromUrl(),
        fetchSource: {
            includes: null,
            excludes: null,
        },
        queryMode: QueryModeEnum.PAGE,
        exportFields: [],
        exportLabels: [],
    };
};

export default {
    mutations,
    actions,
    getters,
    state: initialState,
    filtersToQuery,
    queryToFilters,
    parsePageableFromUrl,
    parseFiltersFromUrl,
    pushWindowHistory,
    apiSearch,
    setFacet,
    loadFacet,
    reloadFacets,
    parseSearchMethodsFromUrl,
    normalizeFilters,
    updateFilters,
};
