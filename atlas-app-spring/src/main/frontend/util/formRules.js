import { object } from "io-ts";

/**
 * Validates an email address according to the following rules mentioned in PM-5601(86a40zqwz):
 * 1. The local part (before the '@') allows uppercase and lowercase letters (A-Z, a-z), digits (0-9),
 *    and special characters (!#$%&'*+/=?^_`{|}~-). Dot (.) is allowed as long as it is not the first
 *    or last character and does not appear consecutively.
 * 2. The domain part (after the '@') allows uppercase and lowercase letters (A-Z, a-z), digits (0-9),
 *    and hyphens (-). Dot (.) is used to separate domain labels.
 * 3. Each domain label (part between dots) can be up to 63 characters long.
 * 4. The entire domain name must not exceed 253 characters.
 *
 * The function returns `true` if the email is valid and `false` otherwise.
 *
 * @param {string} email - The email address to validate.
 * @returns {boolean} - `true` if the email is valid, `false` otherwise.
 *
 * @Example
 * validateEmail('<EMAIL>'); // true
 * validateEmail('<EMAIL>'); // false (consecutive dots)
 * validateEmail('<EMAIL>'); // false (dot at the end of the local part)
 * validateEmail('<EMAIL>'); // false (dot at the beginning of the domain part)
 * validateEmail('<EMAIL>'); // false (hyphen at the beginning of the domain part)
 */
const validateEmail = (email) => {
    const trimmedEmail = email.trim();
    const emailRegex =
        /^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;

    if (!emailRegex.test(trimmedEmail)) {
        return false;
    }

    const [localPart, domainPart] = trimmedEmail.split("@");

    // Check total domain length (up to 253 characters)
    if (domainPart.length > 253) {
        return false;
    }

    // Check each domain label length (up to 63 characters)
    const domainLabels = domainPart.split(".");
    for (const label of domainLabels) {
        if (label.length > 63) {
            return false;
        }
    }

    return true;
};

const charDefaultLimit = (v) => (!v ? true : v.length <= 256 || "Max character limit is 256");

// * BEGIN: RULES
export const required =
    (field = "") =>
    (v) => {
        return !!v || v === false || `${field || "Field"} is required`;
    };

export const email = (v) => /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(v) || "E-mail must be valid";

export const strictEmail = (v) => !v || validateEmail(v) || "E-mail must be valid";

export const tel = (v) => !v || /^\d{10}$/.test(v) || "Must be a 10 digit phone number";

export const vin = (v) =>
    !v || (/^[a-zA-Z0-9]*$/.test(v) && v.trim().length === 17) || "VIN must be exactly 17 alphanumeric characters";

export const date = (v) => !v || /^\d{4}-\d{2}-\d{2}$/.test(v) || "Enter a valid date in format YYYY/MM/DD";

export const zipCode = (v) => !v || /^\d{5}$/.test(v) || "Zip code must be 5 digits";

export const validAmount = (v) => !v || /^[0-9]+(\.[0-9]{1,2})?$/.test(v) || "Only monetary digits are valid";

export const numericOnly = (v) => !isNaN(v) || "Input is not a number";

export const maxValue = (maxlimit) => (v) => !v || v <= maxlimit || `Value should be less than ${maxlimit}`;

export const characters =
    ({
        allowSpaces = false,
        allowHyphens = false,
        allowUnderscore = false,
        allowApostrophes = false,
        allowPeriods = false,
    } = {}) =>
    (v) => {
        if (!v) return true;
        const limitValidation = charDefaultLimit(v);
        if (limitValidation !== true) return limitValidation;
        let allowedChars = "a-zA-Z";
        if (allowSpaces) allowedChars += "\\s";
        if (allowHyphens) allowedChars += "-";
        if (allowUnderscore) allowedChars += "_";
        if (allowApostrophes) allowedChars += "'";
        if (allowPeriods) allowedChars += ".";

        const alphaRegex = new RegExp(`^[${allowedChars}]+$`);
        return (
            alphaRegex.test(v) ||
            `Only letters${allowHyphens ? ", hyphens" : ""}
            ${allowUnderscore ? ", underscore" : ""}${allowSpaces ? ", and spaces" : ""}${
                allowApostrophes ? ", apostrophes" : ""
            }${allowPeriods ? ", periods" : ""}  are allowed`
        );
    };

export const characterLimit = (v) => charDefaultLimit(v);

export const maxLength = (max) => (v) => !v || v.length <= max || `Max character limit is ${max}`;

export const currency = (v) => !v || /^\$?(\d{1,3})(,\d{3})*(\.\d{2})?$/.test(v) || "Enter a valid currency amount";

export const emailOrPhone = (v) => {
    return !v || validateEmail(v) || /^\d{10}$/.test(v) || "Enter a valid email or phone number";
};
// * END: RULES

const formRules = {
    required,
    email,
    strictEmail,
    tel,
    date,
    zipCode,
    validAmount,
    characters,
    vin,
    characterLimit,
    maxLength,
    numericOnly,
    maxValue,
    currency,
    emailOrPhone,
};

export default formRules;
