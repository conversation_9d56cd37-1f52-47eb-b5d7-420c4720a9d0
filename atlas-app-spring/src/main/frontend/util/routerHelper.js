import { kebabCase } from "lodash";
import pathToRegexp from "path-to-regexp";
import URL from "url-parse";
import VueGtm from "@carsaver/vue-gtm";
import Vue from "vue";

function flattenPaths(routes) {
    const paths = [];
    _.each(routes, (r1) => {
        if (_.get(r1, "children", []).length > 0) {
            _.each(r1.children, (r2) => {
                paths.push(_.get(r2, "path"));
            });
        } else {
            paths.push(_.get(r1, "path"));
        }
    });

    return paths;
}

/***
 * Redirect browser location with protection from cyclical redirection.
 *
 * @param toUrl the url to which to redirect the browser
 * @param handleFailure callback to execute if cyclical redirection is detected
 */
function safeRedirect(toUrl, handleFailure) {
    const currentUrl = new URL(document.location.href, true);
    const currentUrlString = currentUrl.toString();
    const toUrlString = toUrl.toString();
    const redirectSameAsCurrent = currentUrlString === toUrlString;

    if (!redirectSameAsCurrent) {
        document.location.href = toUrlString;
    } else {
        const reason = `Prevented redirect from ${currentUrlString} to ${toUrlString} to prevent cyclical redirection`;
        console.error(reason);
        handleFailure();
    }
}

export const routerOptions = {
    // changing to a new page will scroll to the top
    scrollBehavior: (to, from, savedPosition) => {
        if (to.hash) return { selector: to.hash };
        if (savedPosition) return savedPosition;

        return { x: 0, y: 0 };
    },
};

export function configureRouter(router) {
    const environment = _.get(window, "_APP_CONFIG.env[0]", "local") || "local";

    const containers = [];
    let debug = true;
    if (environment === "prod") {
        containers.push({
            id: "GTM-MGDJTH2",
            queryParams: {
                gtm_auth: "W4IDvRJFS6LH3AtEpnELJw",
                gtm_preview: "env-1",
                gtm_cookies_win: "x",
            },
        });
        debug = false;
    } else {
        containers.push({
            id: "GTM-MGDJTH2",
            queryParams: {
                gtm_auth: "jF6ckWE7Bp-1kxGDk8agMw",
                gtm_preview: "env-3",
                gtm_cookies_win: "x",
            },
        });
    }

    Vue.use(VueGtm, {
        id: containers,
        enabled: true,
        debug,
        loadScript: true,
        vueRouter: router,
    });

    router.beforeEach((to, from, next) => {
        const flattenedPaths = flattenPaths(_.get(router, "options.routes", []));

        const routeRegex = _.mapValues(
            _.filter(flattenedPaths, (p) => p !== "*"),
            (r) => pathToRegexp(r, [], {})
        );

        const results = _.filter(routeRegex, (r) => {
            const regexRes = r.exec(to.path);
            return regexRes !== null && regexRes.length >= 1;
        });

        // If the route is not within this entrypoint's "routes", do a document.location page change.
        if (results.length > 0) {
            next();
        } else {
            const redirectUrl = new URL(to.path, true);
            redirectUrl.query = to.query;

            const redirectToNotFoundPage = () => (document.location.href = "/not-found");
            safeRedirect(redirectUrl, redirectToNotFoundPage);
        }
    });
}

export function route(module, name, component, path = "", options = {}) {
    component = Object(component) === component ? component : { default: name.replace(" ", "") };

    const components = {};

    for (const [key, value] of Object.entries(component)) {
        components[key] = () =>
            import(
                /* webpackChunkName: "views-[request]" */
                `Modules/${module}/views/${value}`
            );
    }

    return {
        name,
        components,
        path,
        ...{
            props: {
                default: true,
            },
            ...options,
        },
    };
}

export function layout(layout = "Default", children, path = "") {
    const dir = kebabCase(layout);

    return {
        children,
        component: () =>
            import(
                /* webpackChunkName: "layout-[request]" */
                `@/layouts/${dir}/index`
            ),
        path,
    };
}
