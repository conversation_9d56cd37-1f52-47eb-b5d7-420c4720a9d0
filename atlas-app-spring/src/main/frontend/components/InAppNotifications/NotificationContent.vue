<template>
    <component :is="notificationComponent" :notification="notification" @click="$emit('click', $event)" />
</template>

<script>
import { getNotificationComponent } from "Components/InAppNotifications/constants";
import DealershipNotification from "Components/InAppNotifications/events/DealershipNotification";
import WaterServiceNotification from "Components/InAppNotifications/events/WaterServiceNotification";
import CoffeeServiceNotification from "Components/InAppNotifications/events/CoffeeServiceNotification";
import DefaultUserNotification from "Components/InAppNotifications/events/DefaultUserNotification.vue";

export default {
    name: "NotificationMessage",
    components: {
        DealershipNotification,
        WaterServiceNotification,
        CoffeeServiceNotification,
        DefaultUserNotification,
    },
    props: {
        notification: {
            type: Object,
            required: true,
        },
    },
    computed: {
        notificationComponent() {
            switch (this.notification.eventName) {
                case "clientAtDealership":
                    return getNotificationComponent(this.notification.beverage);
                default:
                    return "DefaultUserNotification";
            }
        },
    },
};
</script>
