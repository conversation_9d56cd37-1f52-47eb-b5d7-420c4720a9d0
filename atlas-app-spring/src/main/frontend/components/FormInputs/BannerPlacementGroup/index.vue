<template>
    <div class="banner-placement-group">
        <v-card
            v-for="(placement, index) in formDataPlacements"
            :key="placement.id || `${index}-${placement.name}`"
            outlined
            class="banner-placement-card pa-6 mb-4"
            :class="{ 'disabled-card': isPlacementDisabled(placement) }"
        >
            <v-row>
                <v-col col="8">
                    <h3 class="banner-title mb-4">{{ placement.name }}</h3>
                </v-col>
                <v-col col="4" class="d-flex justify-end">
                    <button v-if="!placement.id" class="remove-placement-btn" @click="removePlacement(index)">
                        <v-icon size="18">mdi-close-box</v-icon>
                        CANCEL
                    </button>
                </v-col>
            </v-row>
            <v-row>
                <v-col md="4">
                    <!-- Enabled Toggle -->
                    <div class="enabled-section mb-4">
                        <BaseToggle
                            v-model="placement.enabled"
                            label="Enabled"
                            :disabled="isPlacementToggleDisabled()"
                            @input="updateFormData"
                        />
                    </div>
                </v-col>

                <v-col class="d-flex justify-end">
                    <div class="banner-icon">
                        <v-icon size="48" :color="isPlacementDisabled(placement) ? 'grey lighten-2' : 'primary'"
                            >mdi-image-outline</v-icon
                        >
                    </div>
                    <div class="banner-info">
                        <div class="banner-name">
                            {{ placement.selectedBanner ? placement.selectedBanner.name : "[Banner Name]" }}
                        </div>
                        <div class="banner-size">
                            {{ placement.selectedBanner ? placement.selectedBanner.size : "[Banner Size]" }}
                        </div>
                    </div>
                </v-col>
                <v-col class="d-flex justify-end">
                    <v-btn
                        outlined
                        size="large"
                        class="select-banner-btn"
                        :disabled="isPlacementDisabled(placement)"
                        @click="selectBanner"
                    >
                        SELECT BANNER
                    </v-btn>
                </v-col>
            </v-row>

            <!-- Placement Name (Full Width) -->
            <v-row>
                <v-col class="placement-name-section">
                    <v-text-field
                        v-model="placement.name"
                        label="Placement Name"
                        placeholder="New SRP"
                        outlined
                        dense
                        hide-details
                        :disabled="isPlacementDisabled(placement)"
                        @input="updateFormData"
                    />
                    <p class="optional-text">Optional</p>
                </v-col>
            </v-row>

            <!-- Content Row: Banner Preview + Script -->
            <v-row>
                <!-- Banner Script (Right) -->
                <v-col>
                    <div class="banner-script-section">
                        <v-text-field
                            ref="bannerScriptTextarea"
                            v-model="placement.script"
                            label="Banner Script"
                            placeholder='<cs-banner-element data-id="[Banner-id-here]"></cs-banner-element>'
                            outlined
                            dense
                            disabled
                            readonly
                            hide-details
                            class=""
                            :class="{ 'disabled-textarea': isPlacementDisabled(placement) }"
                            @input="updateFormData"
                        />
                        <p class="script-description mb-0">
                            This only needs to be placed into your website once. Once you select the Update button, your
                            changes will be reflected on your website.
                        </p>
                    </div>
                </v-col>
            </v-row>

            <!-- Action Buttons -->
            <v-row>
                <v-col class="action-buttons">
                    <v-btn
                        color="primary"
                        large
                        class="white--text copy-script-btn"
                        :disabled="isPlacementDisabled(placement) || !placement.script"
                        @click="copyScript(placement.script)"
                    >
                        <v-icon left size="18">mdi-content-copy</v-icon>
                        COPY SCRIPT
                    </v-btn>
                </v-col>
            </v-row>
        </v-card>
        <v-card outlined class="add-placement-card pa-6 d-flex justify-space-between align-center">
            <div class="add-description">Add Banner Placement</div>
            <v-btn large outlined class="add-placement-btn" @click="addPlacement">
                <v-icon left size="18">mdi-plus</v-icon>
                ADD PLACEMENT
            </v-btn>
        </v-card>
    </div>
</template>

<script>
import { defineComponent } from "vue";
import BaseToggle from "Components/FormInputs/BaseToggle.vue";
import { call, get, sync } from "vuex-pathify";

export default defineComponent({
    name: "BannerPlacementGroup",
    components: {
        BaseToggle,
    },
    props: {
        id: {
            type: String,
            required: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        availableBanners: {
            type: Array,
            default: () => [],
        },
        placements: {
            type: Array,
            default: () => [],
            validator: (placements) => {
                return placements.every(
                    (placement) => placement && typeof placement === "object" && typeof placement.id === "string"
                );
            },
        },
    },
    data() {
        return {
            newPlacementDefaults: {
                enabled: true,
                name: "Placement",
                selectedBanner: {},
                script: null,
            },
        };
    },
    computed: {
        formDefaults: get("pageConfigs/form@defaults"),
        loader: get("pageConfigs/loader"),
        formDataPlacements: sync("pageConfigs/<EMAIL>"),
        isComplete() {
            return this.loader.isComplete;
        },
        defaultPlacements() {
            return this.formDefaults?.placements;
        },
    },
    watch: {
        value: {
            handler(newValue) {
                if (newValue) {
                    this.formData = { ...this.formData, ...newValue };
                }
            },
            immediate: true,
            deep: true,
        },
        isComplete: {
            handler(newValue) {
                if (newValue) {
                    this.mergePlacementsWithDefaults();
                }
            },
            immediate: true,
        },
    },
    methods: {
        updateFormDataStore: call("pageConfigs/updateFormData"),
        isPlacementDisabled(placement) {
            return this.disabled || !placement.enabled;
        },
        isPlacementToggleDisabled() {
            return this.disabled;
        },
        mergePlacementsWithDefaults() {
            const defaultPlacements = this.defaultPlacements || [];
            const propPlacements = this.placements || [];

            // Create a map to track all unique placement IDs and their sources
            const placementMap = new Map();

            // First, add all prop placements
            propPlacements.forEach((placement) => {
                if (placement && placement.id) {
                    placementMap.set(placement.id, placement);
                }
            });

            // Then, add all default placements (this will override prop placements with same ID)
            defaultPlacements.forEach((placement) => {
                if (placement && placement.id) {
                    placementMap.set(placement.id, placement);
                }
            });

            // Convert map values back to array and update formDataPlacements
            this.formDataPlacements = Array.from(placementMap.values());
        },
        addPlacement() {
            // Create a new placement with default values (ID and script will be generated by backend)
            const newPlacement = {
                ...this.newPlacementDefaults,
                name: `Placement ${this.formDataPlacements.length + 1}`,
            };

            // Add the new placement to the formDataPlacements array
            this.formDataPlacements.push(newPlacement);

            // Update the form data in the store
            this.updateFormData();
        },

        removePlacement(index) {
            if (typeof index !== "number") return;
            if (index < 0 || index >= this.formDataPlacements.length) return;

            // Remove the placement at the specified index
            this.formDataPlacements.splice(index, 1);

            // Sync changes to the store
            this.updateFormData();
        },

        updateFormData() {
            // Update the store with the current placements data
            this.updateFormDataStore({ key: "placements", value: this.formDataPlacements });
        },
        selectBanner() {
            // TODO: Implement banner selection logic
            // This could open a modal or dropdown with available banners
            console.log("Select banner clicked");
        },
        copyScript(script) {
            if (script) {
                // Try modern clipboard API first
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard
                        .writeText(script)
                        .then(() => {
                            this.showCopySuccess();
                        })
                        .catch((err) => {
                            console.error("Failed to copy script with clipboard API:", err);
                            this.fallbackCopyScript();
                        });
                } else {
                    // Fallback for older browsers
                    this.fallbackCopyScript(script);
                }
            }
        },
        fallbackCopyScript(script) {
            try {
                // Create a temporary textarea element
                const textarea = document.createElement("textarea");
                textarea.value = script;
                textarea.style.position = "fixed";
                textarea.style.opacity = "0";
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand("copy");
                document.body.removeChild(textarea);
                this.showCopySuccess();
            } catch (err) {
                console.error("Failed to copy script with fallback method:", err);
                // If all else fails, try to select the textarea content
                this.selectTextareaContent();
            }
        },
        selectTextareaContent() {
            // Select the content in the textarea so user can manually copy
            if (this.$refs.bannerScriptTextarea && this.$refs.bannerScriptTextarea.$refs.input) {
                const textarea = this.$refs.bannerScriptTextarea.$refs.input;
                textarea.select();
                textarea.setSelectionRange(0, 99999); // For mobile devices
            }
        },
        showCopySuccess() {
            // TODO: Implement proper success notification
            // For now, just log to console
            console.log("Script copied to clipboard successfully");

            // You could add a toast notification here
            // this.$toast.success("Script copied to clipboard!");
        },
    },
});
</script>

<style lang="scss" scoped>
.banner-placement-group {
    padding: 0;

    .banner-image-container {
        display: flex;
        justify-content: end;
    }

    .banner-title {
        font-size: px2rem(14);
        line-height: 1.4;
        margin-bottom: 16px;
    }

    // Cancel button styling
    .remove-placement-btn {
        border: none;
        padding: 0;
        background: transparent;
        box-shadow: none;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        font-size: px2rem(10);
        font-weight: bold;
        cursor: pointer;
    }

    .remove-placement-btn ::v-deep .v-icon {
        color: #d32f2f !important; // red icon
    }

    // Disabled card styling
    .disabled-card {
        opacity: 0.6;

        // Exclude the enabled toggle from disabled styling
        .enabled-section {
            opacity: 1;
            pointer-events: auto;
        }

        // Apply disabled styling to other elements
        .placement-name-section,
        .content-row,
        .action-buttons {
            pointer-events: none;

            ::v-deep .v-input,
            ::v-deep .v-btn {
                opacity: 0.7;
            }

            ::v-deep .v-input--is-disabled {
                opacity: 0.5;
            }
        }
    }

    // Enabled section styling
    .enabled-section {
        margin-bottom: 24px;

        ::v-deep .base-toggle {
            .v-input--switch {
                margin-top: 0;
                padding-top: 0;
            }
        }
    }

    // Placement name section (full width)
    .placement-name-section {
        .optional-text {
            font-size: 12px;
            color: #666;
            margin: 4px 0 0 0;
            line-height: 1.2;
        }
    }

    // Content row layout (banner preview + script side by side)
    .content-row {
        display: flex;
        gap: 24px;
        margin-bottom: 24px;

        @media (max-width: 768px) {
            flex-direction: column;
            gap: 16px;
        }
    }

    // Banner column (left side)
    .banner-column {
        flex: 0 0 320px;

        @media (max-width: 768px) {
            flex: 1;
        }

        .banner-preview-container {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 20px;
            background-color: #fafafa;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 80px;

            .banner-preview-content {
                display: flex;
                align-items: center;
                flex: 1;

                .banner-icon {
                    margin-right: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 48px;
                    height: 48px;
                }

                .banner-info {
                    .banner-name {
                        font-size: 14px;
                        color: #666;
                        line-height: 1.3;
                        margin-bottom: 2px;
                    }

                    .banner-size {
                        font-size: 12px;
                        color: #999;
                        line-height: 1.2;
                    }
                }
            }

            .select-banner-btn {
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-left: 16px;
                flex-shrink: 0;
            }
        }

        // Disabled styling for banner selection area
        .disabled-card & {
            .banner-preview-container {
                background-color: #f5f5f5;
                border-color: #e0e0e0;

                .banner-info {
                    .banner-name,
                    .banner-size {
                        color: #999 !important;
                    }
                }

                .banner-icon {
                    opacity: 0.5;
                }
            }
        }
    }

    // Script column (right side)
    .script-column {
        flex: 1;

        .banner-script-section {
            .banner-script-textarea {
                // Make the disabled textarea still selectable for copy/paste
                ::v-deep .v-input__control .v-input__slot textarea {
                    user-select: text !important;
                    -webkit-user-select: text !important;
                    -moz-user-select: text !important;
                    -ms-user-select: text !important;
                    cursor: text !important;
                    color: rgba(0, 0, 0, 0.87) !important;
                    -webkit-text-fill-color: rgba(0, 0, 0, 0.87) !important;
                }
            }

            // Additional disabled styling for textarea
            &.disabled-textarea {
                ::v-deep .v-input--is-disabled .v-input__slot textarea {
                    color: rgba(0, 0, 0, 0.38) !important;
                    -webkit-text-fill-color: rgba(0, 0, 0, 0.38) !important;
                }
            }

            .script-description {
                font-size: 12px;
                color: #666;
                margin: 8px 0 0 0;
                line-height: 1.4;
            }
        }
    }

    // Action buttons
    .action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .copy-script-btn {
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            ::v-deep .v-btn__content {
                .v-icon {
                    margin-right: 6px;
                }
            }
        }

        .update-btn {
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 100px;
        }
    }

    // Add placement card styling
    .add-description {
        font-size: px2rem(14);
        font-weight: 600;
    }
}
</style>
