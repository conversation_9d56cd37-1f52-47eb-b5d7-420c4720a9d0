<template>
    <div>
        <v-divider>inset</v-divider>
        <v-row class="py-4">
            <v-col cols="auto" class="body-2">Boost Retention</v-col>
            <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                <v-switch v-model="isBoostRetentionEnabled" :disabled="!isAdminUser"></v-switch>
            </div>
        </v-row>
    </div>
</template>

<script>
import { call, get } from "vuex-pathify";
import lodashGet from "lodash/get";

export default {
    name: "BoostRetentionToggle",
    props: {
        program: {
            type: Object,
            required: true,
        },
    },
    computed: {
        userId: get("loggedInUser/userId"),
        programId() {
            return lodashGet(this.program, "programId", null);
        },
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
        isBoostRetentionEnabled: {
            get() {
                let isBoostRetentionEnabled = lodashGet(this.program, "isBoostRetentionEnabled", false);
                return isBoostRetentionEnabled;
            },
            set(value) {
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "boost_retention",
                    isEnabled: value,
                    userId: this.userId,
                });
            },
        },
    },
    methods: {
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
    },
};
</script>

<style lang="scss" scoped>
.v-input--selection-controls {
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 0;
    height: 20px;
}
</style>
