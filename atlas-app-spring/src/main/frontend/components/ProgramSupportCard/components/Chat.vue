<template>
    <v-container class="pt-0">
        <v-row class="pt-4">
            <v-col cols="6" class="body-2 pl-0"> Live Chat - {{ selectedChatName }} </v-col>
            <v-col cols="6" class="body-2 pr-0">
                <div class="d-flex justify-end align-center">
                    <a v-if="editMode" class="mr-4" @click="editChatConfig">Edit</a>
                    <v-switch v-model="isChatEnabled" class="mt-0 pt-0" hide-details />
                </div>
            </v-col>
        </v-row>
        <v-form v-show="showChatOptions" ref="dealerChatForm" v-model="valid" lazy-validation>
            <v-row v-if="isOttoChatbotSplitFeatureEnabled">
                <v-col cols="12">
                    <OttoChatbotToggle v-model="form.isOttoChatbotEnabled" :program="program" />
                </v-col>
            </v-row>
            <v-row class="mt-0">
                <v-col cols="5">
                    <v-radio-group v-model="selectedChatOption" class="mt-0 pt-0">
                        <v-radio value="reputation" label="Reputation.com Chat" />
                        <v-radio value="dealership" label="Dealership Chat" />
                        <v-radio v-if="isOttoChatbotSplitFeatureEnabled" value="none" label="None" />
                    </v-radio-group>
                </v-col>
                <v-col cols="4">
                    <a :href="`mailto:${chatSupportEmail}`">{{ chatSupportEmail }}</a>
                </v-col>
                <v-col cols="3">
                    <a :href="`tel:${chatSupportPhone}`">{{ chatSupportPhone | phoneFormatter }}</a>
                </v-col>
            </v-row>

            <div :class="[isOttoChatbotSplitFeatureEnabled ? 'chat-form-box-with-otto' : 'chat-form-box']">
                <v-divider></v-divider>

                <v-expand-transition>
                    <div v-show="selectedChatOption === 'dealership'">
                        <v-row class="row mt-2">
                            <v-col class="pb-0" cols="6">
                                <v-select
                                    ref="chatOptions"
                                    v-model="form.provider.name"
                                    :items="chatOptions"
                                    label="Chat Provider"
                                    dense
                                    outlined
                                    :rules="selectedChatOption === 'dealership' ? validationRules.name : []"
                                    @input="checkIfOtherProviderFieldShouldBeCleared"
                                />
                            </v-col>
                            <v-col v-if="form.provider.name === 'Other'" class="pb-0" cols="6">
                                <v-text-field
                                    ref="otherProvider"
                                    v-model="form.provider.otherProvider"
                                    label="Enter Provider Name"
                                    dense
                                    outlined
                                    :rules="selectedChatOption === 'dealership' ? validationRules.otherProvider : []"
                                />
                            </v-col>
                            <v-col class="pb-0" cols="12">
                                <v-text-field
                                    ref="elementId"
                                    v-model="form.provider.elementId"
                                    label="Enter Element ID"
                                    dense
                                    outlined
                                    hint="Enter ID to be applied to div element on the page, Not Required*"
                                    :rules="[rules.characterLimit]"
                                />
                            </v-col>
                            <v-col cols="12">
                                <v-textarea
                                    ref="chatScript"
                                    v-model="form.script"
                                    label="Enter your Chat Code Snippet Here"
                                    outlined
                                    :rules="selectedChatOption === 'dealership' ? validationRules.chatScript : []"
                                    hint="Paste your Dealership Chat code snippet into the input box above"
                                    persistent-hint
                                    counter="1000"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </v-expand-transition>

                <v-alert v-if="alertMessage" text type="success" class="mb-0 mt-2">
                    Your changes have been successfully saved
                </v-alert>

                <div class="d-flex justify-end align-end pb-2 pt-4 pr-0 pl-2">
                    <v-btn :disabled="dealerChatConfigLoader.isLoading" @click="cancel">Cancel</v-btn>
                    <v-btn
                        :disabled="!valid"
                        color="primary"
                        class="ml-2"
                        :loading="dealerChatConfigLoader.isLoading"
                        @click="save"
                    >
                        Save
                    </v-btn>
                </div>
            </div>
        </v-form>
    </v-container>
</template>

<script>
import lodashGet from "lodash/get";
import { call, get } from "vuex-pathify";
import { characterLimit } from "Util/formRules";
import { sanitize } from "Util/sanitize";
import OttoChatbotToggle from "Components/ProgramSupportCard/components/OttoChatbotToggle.vue";

export default {
    name: "Chat",
    components: { OttoChatbotToggle },
    props: {
        program: {
            type: Object,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            editMode: lodashGet(this.program, "isChatEnabled", false),
            selectedChatOption: null,
            chatOptions: [
                "ActiveEngage",
                "CarChat24",
                "CarNow",
                "Contact At Once!",
                "Engage to Sell",
                "Gubagoo",
                "LiveChat",
                "Podium",
                "Roadster",
                "Other",
            ],
            chatSupportEmail: "<EMAIL>",
            chatSupportPhone: "**********",
            form: {
                isReputationChat: false,
                isDealerChat: false,
                isOttoChatbotEnabled: false,
                provider: {
                    name: null,
                    otherProvider: null,
                    elementId: null,
                },
                script: null,
            },
            rules: { characterLimit },
            validationRules: {
                name: [(v) => !!v || "Chat Provider is required"],
                chatScript: [
                    (v) => !!v || "Chat Code Snippet is required",
                    (v) => (v && v.length <= 1000) || "Maximum 1000 characters",
                ],
                otherProvider: [
                    (v) => !!v || "Other Provider is required",
                    (v) => (v && v.length <= 20) || "Maximum 20 characters",
                ],
            },
            valid: false,
        };
    },

    computed: {
        alertMessage: get("dealerStore/dealerChatConfig@alertMessage"),
        showChatOptions: get("dealerStore/dealerChatConfig@showChatOptions"),
        featureFlags: get("loggedInUser/featureFlags"),
        userId: get("loggedInUser/userId"),
        dealerChatConfigLoader: get("dealerStore/dealerChatConfig@loader"),
        programId() {
            let id = lodashGet(this.program, "programId", null);
            return id;
        },
        isChatEnabled: {
            get() {
                let isChatEnabled = lodashGet(this.program, "isChatEnabled", false);
                return isChatEnabled;
            },
            set(value) {
                this.editMode = value;
                if (value === false) {
                    this.toggleDealerChatOptions(false);
                    this.form.isOttoChatbotEnabled = false;
                    this.form.isReputationChat = false;
                    this.form.isDealerChat = false;
                    this.toggleDealerProgramFeature({
                        programId: this.programId,
                        configType: "otto_chatbot",
                        isEnabled: false,
                        userId: this.userId,
                    });
                }
                this.updateFeatureToggle(value);
            },
        },
        dealerChatConfig() {
            const dealerChatConfig = lodashGet(this.program, "dealerChatConfig", null);

            return dealerChatConfig;
        },
        selectedChatName() {
            const isReputationChat = lodashGet(this.dealerChatConfig, "isReputationChat", false);
            const selectedEnabledChatName = lodashGet(this.dealerChatConfig, "provider.name", "None");
            const otherProviderName = lodashGet(this.dealerChatConfig, "provider.otherProvider", null);
            const provider = selectedEnabledChatName === "Other" ? otherProviderName : selectedEnabledChatName;
            const result = isReputationChat ? "Reputation.com" : provider;

            return result;
        },
        isOttoChatbotSplitFeatureEnabled() {
            return lodashGet(this.featureFlags, "ENABLE_OTTO", false);
        },
    },
    watch: {
        selectedChatOption(newVal) {
            if (newVal === "dealership") {
                this.form.isDealerChat = true;
                this.form.isReputationChat = false;

                if (this.dealerChatConfig.provider) {
                    this.form.provider.name = this.dealerChatConfig.provider?.name;
                    this.form.provider.otherProvider = this.dealerChatConfig.provider?.otherProvider;
                    this.form.provider.elementId = this.dealerChatConfig.provider?.elementId;
                }

                if (this.dealerChatConfig.script) {
                    this.form.script = this.decodeScript(this.dealerChatConfig.script);
                }
            }

            if (newVal === "reputation") {
                this.form.isDealerChat = false;
                this.form.isReputationChat = true;
                this.resetDealerChatForm();
            }

            if (newVal === "none") {
                this.form.isDealerChat = false;
                this.form.isReputationChat = false;
                this.resetDealerChatForm();
            }
        },
    },
    created() {
        this.initializeFormData();
    },
    methods: {
        updateToggleDealerChatFeature: call("dealerStore/updateToggleDealerChatFeature"),
        updateDealerChatConfig: call("dealerStore/updateDealerChatConfig"),
        toggleDealerChatOptions: call("dealerStore/toggleDealerChatOptions"),
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
        cancel() {
            if (this.isChatEnabled) {
                this.editMode = true;
            }
            this.toggleDealerChatOptions(false);
            this.initializeFormData();
        },
        updateFeatureToggle(enabled) {
            if (this.$refs.dealerChatForm.validate()) {
                const dealerChatConfigRequest = {
                    // if dealerChatConfig is null, then it is a new chat config
                    id: this.dealerChatConfig != null ? this.dealerChatConfig.id : null,
                    dealerId: this.dealerChatConfig != null ? this.dealerChatConfig.dealerId : this.dealerId,
                    programId: this.programId,
                    chatEnabled: enabled,
                    isReputationChat: this.form.isReputationChat,
                    isDealerChat: this.form.isDealerChat,
                    provider: {
                        name: this.form.provider.name,
                        otherProvider: this.form.provider.otherProvider,
                        elementId: this.form.provider.elementId,
                    },
                    script: this.form.script ? btoa(this.form.script) : null,
                };

                this.updateToggleDealerChatFeature(dealerChatConfigRequest);
            }
        },
        save() {
            if (this.$refs.dealerChatForm.validate()) {
                const dealerChatConfigRequest = {
                    id: this.dealerChatConfig != null ? this.dealerChatConfig.id : null,
                    dealerId: this.dealerChatConfig != null ? this.dealerChatConfig.dealerId : this.dealerId,
                    programId: this.programId,
                    chatEnabled: true,
                    isReputationChat: this.form.isReputationChat,
                    isDealerChat: this.form.isDealerChat,
                    provider: {
                        name: this.form.provider.name,
                        otherProvider: this.form.provider.otherProvider,
                        elementId: this.form.provider.elementId,
                    },
                    script: this.form.script ? btoa(this.form.script) : null,
                };

                this.updateDealerChatConfig(sanitize(dealerChatConfigRequest, { exclude: ["script"], deep: true }));

                // Save OTTO chatbot toggle state
                if (this.isOttoChatbotSplitFeatureEnabled) {
                    this.toggleDealerProgramFeature({
                        programId: this.programId,
                        configType: "otto_chatbot",
                        isEnabled: this.form.isOttoChatbotEnabled,
                        userId: this.userId,
                    });
                }
            }
        },
        initializeFormData() {
            const dealerChatConfig = this.dealerChatConfig;
            if (dealerChatConfig) {
                this.selectedChatOption = "none";
                if (dealerChatConfig.isReputationChat) {
                    this.selectedChatOption = "reputation";
                } else if (dealerChatConfig.isDealerChat) {
                    this.selectedChatOption = "dealership";
                }
                this.form.isReputationChat = dealerChatConfig.isReputationChat;
                this.form.isDealerChat = dealerChatConfig.isDealerChat;
                this.form.provider.name = dealerChatConfig.provider?.name;
                this.form.provider.otherProvider = dealerChatConfig.provider?.otherProvider;
                this.form.provider.elementId = dealerChatConfig.provider?.elementId;
                this.form.script = this.decodeScript(dealerChatConfig.script);
            }
            this.form.isOttoChatbotEnabled = lodashGet(this.program, "isOttoChatbotEnabled", false);
        },
        resetDealerChatForm() {
            this.$refs.chatOptions.reset();
            this.$refs.chatScript.reset();
            // this ref only exists if the "other" option was selected
            if (this.$refs.otherProvider) {
                this.$refs.otherProvider.reset();
            }
        },
        editChatConfig() {
            this.toggleDealerChatOptions(true);
        },
        checkIfOtherProviderFieldShouldBeCleared() {
            if (this.form.provider.name !== "Other" && this.form.provider.otherProvider) {
                this.form.provider.otherProvider = null;
            }
        },
        decodeScript(encodedScript) {
            const decodedScript = atob(encodedScript);
            return decodedScript;
        },
    },
};
</script>
<style scoped>
.chat-form-box {
    margin-top: 55px;
}
.chat-form-box-with-otto {
    margin-top: 80px;
}
</style>
