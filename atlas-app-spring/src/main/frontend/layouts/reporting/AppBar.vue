<template>
    <v-app-bar app color="secondary" dark class="reporting-app-bar">
        <v-app-bar-nav-icon class="hidden-md-and-up" @click="open = !open" />
        <default-drawer-toggle class="hidden-sm-and-down" />

        <v-toolbar-title>Atlas</v-toolbar-title>

        <v-spacer />

        <action-menu :show-dealer-selector="false" />
    </v-app-bar>
</template>
<script>
import ActionMenu from "@/modules/Navigation/components/ActionMenu";
import DefaultDrawerToggle from "@/layouts/default/DrawerToggle";
import { sync } from "vuex-pathify";
export default {
    components: { ActionMenu, DefaultDrawerToggle },
    computed: {
        open: sync("drawer/open"),
    },
};
</script>
<style lang="scss">
.reporting-app-bar.v-app-bar.v-app-bar--fixed {
    z-index: 14 !important;
}
</style>
