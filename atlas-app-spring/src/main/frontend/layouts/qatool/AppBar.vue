<template>
    <v-app-bar app color="secondary" dark>
        <v-app-bar-nav-icon class="hidden-md-and-up" @click="open = !open" />
        <default-drawer-toggle class="hidden-sm-and-down" />

        <v-toolbar-title>Atlas</v-toolbar-title>
        <v-spacer />
        <v-btn icon href="/logout"><v-icon>mdi-logout-variant</v-icon> </v-btn>
    </v-app-bar>
</template>
<script>
import { sync } from "vuex-pathify";
import DefaultDrawerToggle from "@/layouts/default/DrawerToggle";

export default {
    name: "AppBar",
    components: { DefaultDrawerToggle },

    computed: {
        open: sync("drawer/open"),
    },
};
</script>
