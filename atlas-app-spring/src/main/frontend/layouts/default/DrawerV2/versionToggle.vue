<template>
    <div v-if="enabled" class="version-toggle px-4 mb-4 w-100">
        <v-btn-toggle v-model="menuVersionSelected" class="d-flex justify-center">
            <v-btn :value="v1">
                <v-icon v-if="v1Selected">mdi-circle</v-icon>
                <v-icon v-else>mdi-circle-outline</v-icon>
                V 1.0
            </v-btn>

            <v-btn :value="v2">
                <v-icon v-if="v2Selected">mdi-circle</v-icon>
                <v-icon v-else>mdi-circle-outline</v-icon>
                V 2.0
            </v-btn>
        </v-btn-toggle>
        <toggle-btn v-model="debugMode" class="d-flex justify-center"> Debug Mode: {{ debugMode }}</toggle-btn>
    </div>
</template>

<script>
import { get, sync } from "vuex-pathify";
import ToggleBtn from "Components/FormInputs/ToggleBtn.vue";
export default {
    name: "VersionToggle",
    components: { ToggleBtn },
    data() {
        return {
            v1: "v1",
            v2: "v2",
        };
    },
    computed: {
        debugMode: sync("pageConfigs/debugMode"),
        menuVersionSelected: sync("pageConfigs/menuVersionSelected"),
        enabled: get("loggedInUser/featureFlags@NEW_CONFIG_MANAGER"),
        v1Selected() {
            return this.menuVersionSelected === this.v1;
        },
        v2Selected() {
            return this.menuVersionSelected === this.v2;
        },
    },
};
</script>

<style scoped lang="scss"></style>
