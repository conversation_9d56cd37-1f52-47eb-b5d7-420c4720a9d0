<template>
    <v-app>
        <template v-if="newConfigManagerEnabled && menuVersionSelected === 'v2'">
            <drawer-v2 />
        </template>
        <template v-else>
            <program-drawer v-if="isProgramTypeUser" />
            <default-drawer v-else />
        </template>

        <default-bar />

        <default-view />

        <default-footer />

        <in-app-notifications v-if="inAppNotificationEnabled" />
    </v-app>
</template>

<script>
import { get, sync } from "vuex-pathify";
import InAppNotifications from "Components/InAppNotifications/InAppNotifications.vue";

export default {
    name: "DefaultLayout",

    components: {
        InAppNotifications,
        DefaultBar: () =>
            import(
                /* webpackChunkName: "default-app-bar" */
                "./AppBar"
            ),
        DefaultDrawer: () =>
            import(
                /* webpackChunkName: "default-drawer" */
                "./Drawer"
            ),
        ProgramDrawer: () =>
            import(
                /* webpackChunkName: "default-drawer" */
                "./DrawerProgram"
            ),
        DrawerV2: () =>
            import(
                /* webpackChunkName: "default-drawer" */
                "./DrawerV2"
            ),
        DefaultFooter: () =>
            import(
                /* webpackChunkName: "default-footer" */
                "./Footer"
            ),
        DefaultView: () =>
            import(
                /* webpackChunkName: "default-view" */
                "./View"
            ),
    },
    computed: {
        isProgramTypeUser: get("loggedInUser/isProgramTypeUser"),

        // temporary for testing New Config Manager
        newConfigManagerEnabled: get("loggedInUser/featureFlags@NEW_CONFIG_MANAGER"),
        menuVersionSelected: sync("pageConfigs/menuVersionSelected"),

        inAppNotificationEnabled: get("loggedInUser/featureFlags@ATLAS_IN_APP_NOTIFICATION_ENABLED"),
    },
};
</script>
