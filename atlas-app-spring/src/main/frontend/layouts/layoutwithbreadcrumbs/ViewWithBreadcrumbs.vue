<template>
    <v-main>
        <v-container class="background-color__gray h-100" fluid>
            <v-row>
                <v-col>
                    <breadcrumb-bar :items="filteredBreadcrumbItems" class="layout-breadcrumb">
                        <template #actionArea>
                            <update-btn :loading="saveButtonLoading.isLoading" @clicked="handleSubmit" />
                        </template>
                    </breadcrumb-bar>
                </v-col>
            </v-row>
            <v-row class="mt-2">
                <v-col class="pt-0">
                    <config-main-context>
                        <router-view :key="$route.path" />
                    </config-main-context>
                </v-col>
            </v-row>
            <Snackbar />
        </v-container>
    </v-main>
</template>
<script>
import BreadcrumbBar from "Components/BreadcrumbBar/index";
import ConfigMainContext from "@/layouts/layoutwithbreadcrumbs/ConfigMainContext";
import UpdateBtn from "Components/UpdateBtn.vue";
import { get, call } from "vuex-pathify";
import Snackbar from "Components/ConfigFormBuilder/components/Snackbar.vue";

export default {
    name: "ViewWithBreadcrumbs",
    components: { Snackbar, UpdateBtn, ConfigMainContext, BreadcrumbBar },
    props: {},
    computed: {
        userId: get("loggedInUser/userId"),
        breadcrumbItems: get("pageConfigs/breadcrumbItems"),
        selectedCategory: get("pageConfigs/selectedCategory"),
        selectedDealer: get("loggedInUser/selectedDealer"),
        pageName: get("pageConfigs/pageBuilderData@page"),
        saveButtonLoading: get("pageConfigs/saveForm@loader"),
        filteredBreadcrumbItems() {
            return this.breadcrumbItems.filter((item) => !!item.text);
        },
    },
    watch: {
        $route() {
            this.init();
        },
        selectedCategory(newValue, oldValue) {
            if (oldValue === null && newValue !== null) {
                this.init();
            }
        },
    },
    mounted() {
        this.init();
    },
    methods: {
        configureBreadcrumbs: call("pageConfigs/configureBreadcrumbs"),
        saveFormData: call("pageConfigs/saveFormData"),
        init() {
            this.configureBreadcrumbs(this.$route);
        },
        handleSubmit() {
            this.saveFormData({
                userId: this.userId,
                page: this.pageName,
                dealerId: this.selectedDealer.id,
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.background-color__gray {
    background-color: #eee;
}
.layout-breadcrumb {
    height: auto;
}
</style>
