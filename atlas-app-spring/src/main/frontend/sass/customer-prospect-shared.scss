@import "~vuetify/src/styles/settings/_variables";
.page-container {
    background-color: $gray-200;
    height: 100%;

    .gap-12 {
        gap: 12px;
    }

    .section-container {
        height: max-content;
    }

    .section-container,
    .iframe-container {
        background-color: white;

        & > iframe {
            border-radius: 4px;
        }
    }
    .placeholder-container {
        border-radius: 4px;
        max-width: 100%;

        @media #{map-get($display-breakpoints, 'lg-and-up')} {
            max-width: calc(100% - 256px);
        }
    }

    .main-contain {
        display: flex;

        .customer-details-menu-wrapper {
            width: 244px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
            flex-direction: column;
            .customer-details-menu-wrapper {
                width: auto;
            }
        }
    }
}