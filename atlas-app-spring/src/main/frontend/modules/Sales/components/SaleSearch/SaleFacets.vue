<template>
    <div>
        <facet-list-group facet-group-label="Dates" facet-group-name="dates" facet-icon="mdi-calendar-range">
            <template #activator>
                <div class="d-flex justify-space-between">
                    <span>Dates</span>
                    <!--small v-if="isFiltered" @click.stop="clearFilter(filterName)"> clear </small-->
                </div>
            </template>
            <slot>
                <facet-date-picker-range
                    facet-label="Created Date"
                    store="saleSearch"
                    facet-name="createdDate"
                    filter-name="createdDate"
                />
                <facet-date-picker-range
                    facet-label="Delivery Date"
                    store="saleSearch"
                    facet-name="deliveryDate"
                    filter-name="deliveryDate"
                />
                <facet-date-picker-range
                    facet-label="LTW Enrollment Date"
                    store="saleSearch"
                    facet-name="ltwEnrollmentDate"
                    filter-name="ltwEnrollmentDate"
                />
            </slot>
        </facet-list-group>
        <facet-list-group facet-group-label="User" facet-group-name="user" facet-icon="mdi-account-circle">
            <facet-checkbox facet-label="Tenant" store="saleSearch" facet-name="tenants" filter-name="tenants" />
            <facet-checkbox
                facet-label="Multi-Account"
                store="saleSearch"
                facet-name="userRefTenants"
                filter-name="userRefTenantIds"
            />
            <facet-radio
                v-if="isTopDmaEnabled"
                facet-label="Top DMAs"
                store="saleSearch"
                facet-name="topDmas"
                filter-name="topDmas.end"
                :show-count="false"
            />
            <facet-checkbox
                facet-label="Source"
                store="saleSearch"
                facet-name="userSourceHostnames"
                filter-name="userSourceHostnames"
            />
            <facet-checkbox facet-label="Tags" store="saleSearch" facet-name="userTags" filter-name="userTags" />
            <facet-checkbox facet-label="Cities" store="saleSearch" facet-name="userCities" filter-name="userCities" />
            <facet-checkbox
                facet-label="States"
                store="saleSearch"
                facet-name="userStates"
                filter-name="userStateCodes"
            />
            <facet-checkbox facet-label="DMA" store="saleSearch" facet-name="userDmas" filter-name="userDmaCodes" />
            <facet-date-picker-range
                facet-label="User Created Date"
                store="saleSearch"
                facet-name="createdDate"
                filter-name="createdDate"
            />
        </facet-list-group>

        <facet-list-group facet-group-label="Vehicle" facet-group-name="vehicle" facet-icon="mdi-car">
            <facet-radio facet-label="Stock Type" store="saleSearch" facet-name="stockTypes" filter-name="stockType" />
            <facet-checkbox facet-label="Years" store="saleSearch" facet-name="years" filter-name="years" />
            <facet-checkbox facet-label="Makes" store="saleSearch" facet-name="makes" filter-name="makes" />
            <facet-checkbox facet-label="Models" store="saleSearch" facet-name="models" filter-name="models" />
        </facet-list-group>

        <facet-list-group facet-group-label="Sales" facet-group-name="sales" facet-icon="mdi-currency-usd">
            <facet-checkbox facet-label="Statuses" store="saleSearch" facet-name="statuses" filter-name="statuses" />
            <facet-checkbox
                facet-label="Matched By"
                store="saleSearch"
                facet-name="saleProviders"
                filter-name="saleProviders"
            />
            <facet-checkbox facet-label="Agents" store="saleSearch" facet-name="agents" filter-name="agentIds" />
        </facet-list-group>
    </div>
</template>

<script>
import FacetListGroup from "Components/Facets/FacetListGroup";
import FacetRadio from "Components/Facets/FacetRadio";
import FacetCheckbox from "Components/Facets/FacetCheckbox";
import FacetDatePickerRange from "Components/Facets/FacetDatePickerRange";
export default {
    name: "SaleFacets",
    components: { FacetDatePickerRange, FacetCheckbox, FacetRadio, FacetListGroup },
    data() {
        return {
            isTopDmaEnabled: false,
        };
    },
};
</script>
