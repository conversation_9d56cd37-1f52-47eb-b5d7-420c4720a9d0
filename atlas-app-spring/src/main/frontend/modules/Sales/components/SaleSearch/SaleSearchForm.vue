<template>
    <v-form v-model="valid" class="mb-3" @submit.prevent="doSearch">
        <v-card>
            <v-container fluid>
                <v-row>
                    <v-col cols="12">
                        <div class="input-container d-flex flex-wrap">
                            <v-text-field
                                v-model="name"
                                label="Name"
                                outlined
                                :rules="[
                                    rules.characters({ allowHyphens: true, allowSpaces: true, allowUnderscore: true }),
                                ]"
                                dense
                                append-icon="mdi-account"
                                class="name-input w-100"
                            />
                            <div class="d-flex align-center fill-height">
                                <div class="d-flex flex-row search-btn-wrapper">
                                    <v-btn
                                        size="sm"
                                        class="mr-1 white--text"
                                        type="submit"
                                        color="primary"
                                        :disabled="!valid || isLoading"
                                    >
                                        Search
                                    </v-btn>

                                    <v-btn text size="sm" class="ml-1" :disabled="isLoading" @click="clearFilters">
                                        Reset
                                    </v-btn>
                                </div>
                            </div>
                        </div>
                    </v-col>
                </v-row>
            </v-container>
        </v-card>
    </v-form>
</template>

<script>
import { sync, dispatch, get } from "vuex-pathify";
import { characters } from "Util/formRules";
import { sanitize } from "Util/sanitize";

export default {
    name: "SaleSearchForm",
    data() {
        return {
            valid: false,
            rules: { characters },
        };
    },
    computed: {
        isLoading: get("saleSearch/<EMAIL>"),
        name: sync("saleSearch/filters@name"),
    },

    methods: {
        doSearch() {
            if (!this.valid) return;
            this.name = sanitize(this.name);
            dispatch("saleSearch/doSearch");
        },
        clearFilters() {
            dispatch("saleSearch/clearFilters");
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";
.input-container {
    gap: 15px;
}
.name-input {
    max-width: 100%;
    min-width: 200px;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
        max-width: 375px;
    }
}
</style>
