<template>
    <v-card>
        <v-toolbar flat>
            <v-toolbar-title class="grey--text">Sales</v-toolbar-title>

            <v-spacer />

            <table-column-selector id="table-column-config" v-model="displayFields" :fields="headers" />
            <export-column-selector
                id="table-column-export"
                v-model="exportFields"
                :display-fields="displayFields"
                :fields="headers"
                @doExport="prepareExport"
            />
        </v-toolbar>

        <v-divider />

        <filter-chips :store="store" />

        <table-search-count-label :page-number="pageNumber" :page-size="pageSize" :total-elements="totalElements" />

        <v-divider />

        <v-data-table
            :loading="isLoading"
            :headers="fieldsForDisplay"
            :items="searchResults"
            :server-items-length="totalElements"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            hide-default-footer
            @update:options="updateOptions"
        >
            <template #item.user.tenant.name="{ item }">
                <v-chip v-if="item.user.tenant" small dark color="primary">{{ item.user.tenant.name }}</v-chip>
            </template>
            <template #item.status="{ item }">
                <v-chip small :color="statusCss(item.status)">
                    <span>{{ item.status | lowerCase | startCase }}</span>
                </v-chip>
            </template>
            <template #item.sale.provider="{ item }">
                <span v-if="item.sale">{{ item.sale.provider }}</span>
            </template>
            <template #item.reportedDate="{ item }">
                <span>{{ item.reportedDate | formatDate }}</span>
            </template>
            <template #item.vehicle="{ item }">
                <span>{{ item.vehicle.stockType }} </span>
                <span>{{ item.vehicle.year }} </span>
                <span>{{ item.vehicle.make }} </span>
                <span>{{ item.vehicle.model }}</span>
            </template>
            <template #item.user="{ item }">
                <router-link :to="customerLink(item.user.id)">
                    <span>{{ item.user.firstName }} {{ item.user.lastName }}</span>
                </router-link>
            </template>
            <template #item.user.firstName="{ item }">
                <router-link :to="customerLink(item.user.id)">
                    <span>{{ item.user.firstName }}</span>
                </router-link>
            </template>
            <template #item.user.lastName="{ item }">
                <router-link :to="customerLink(item.user.id)">
                    <span>{{ item.user.lastName }}</span>
                </router-link>
            </template>
            <template #item.user.email="{ item }">
                <a :href="`mailto:${item.user.email}`"> {{ item.user.email }}</a>
            </template>
            <template #item.user.phoneNumber="{ item }">
                <a :href="`tel:${item.user.phoneNumber}`">
                    {{ item.user.phoneNumber | phoneFormatter }}
                </a>
            </template>
            <template #item.user.smsEnabled="{ item }">
                <div class="d-flex align-center justify-center" style="width: 100%">
                    <boolean-indicator :value="item.user.smsEnabled" />
                </div>
            </template>

            <template #item.user.tags="{ item }">
                <span>{{ item.user.tags }}</span>
            </template>

            <template #item.user.createdDate="{ item }">
                <span>{{ item.user.createdDate | formatEpochDate }}</span>
            </template>

            <template #item.user.repeatBuyer="{ item }">
                <v-chip small dark color="secondary">{{ item.user.repeatBuyer }}</v-chip>
            </template>

            <template #item.dealer.successManager="{ item }">
                <span v-if="item.dealer && item.dealer.successManager">
                    {{ item.dealer.successManager.firstName }} {{ item.dealer.successManager.lastName }}</span
                >
            </template>
            <template #item.deliveryDate="{ item }">
                <span>{{ item.deliveryDate | formatDate }}</span>
            </template>

            <template #item.createdDate="{ item }">
                <span>{{ item.createdDate | formatDate }}</span>
            </template>

            <template #item.financeAppSubmitted="{ item }">
                <span v-if="isProgramUser">{{ formatFinanceAppSubmitted(item.financeAppSubmitted) }}</span>
            </template>
        </v-data-table>

        <v-container class="pt-2">
            <v-row>
                <v-col cols="12">
                    <v-pagination :value="page" :total-visible="10" :length="totalPages" @input="changePage" />
                </v-col>
            </v-row>
        </v-container>
    </v-card>
</template>

<script>
import { get, sync, call } from "vuex-pathify";
import TableColumnSelector from "Components/TableColumnSelector";
import ExportColumnSelector from "Components/ExportColumnSelector";
import TableSearchCountLabel from "Components/TableSearchCountLabel";
import tableUtils from "@/util/tableUtils";
import FilterChips from "Components/Search/FilterChips";
import _ from "lodash";
import BooleanIndicator from "Components/BooleanIndicator";
import moment from "moment";

export default {
    name: "SaleList",
    components: {
        BooleanIndicator,
        FilterChips,
        TableColumnSelector,
        ExportColumnSelector,
        TableSearchCountLabel,
    },
    filters: {
        lowerCase(value) {
            if (!value) return "";
            value = value.toLowerCase();
            return value;
        },
        formatDate(val) {
            let formattedDate = "";
            if (val) {
                const deliveryDate = moment(val);
                formattedDate = deliveryDate.format("MMMM Do, YYYY");
            }

            return formattedDate;
        },
        startCase(val) {
            return _.startCase(val);
        },
    },
    props: {
        dealerIds: {
            type: String,
            required: true,
        },
        store: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            headers: [
                {
                    value: "user.tenant.name",
                    text: "Tenant",
                    sortable: false,
                    hideable: false,
                },
                {
                    value: "sale.provider",
                    text: "Sale Match",
                },
                {
                    value: "status",
                    text: "Status",
                    sortable: true,
                },
                {
                    value: "reportedDate",
                    text: "Reported Date",
                    sortable: true,
                    width: 125,
                },
                {
                    value: "user",
                    text: "User Name",
                    sortable: false,
                },
                {
                    value: "user.firstName",
                    text: "User First Name",
                },
                {
                    value: "user.lastName",
                    text: "User Last Name",
                },
                {
                    value: "user.email",
                    text: "User Email",
                },
                {
                    value: "user.phoneNumber",
                    text: "User Phone",
                    width: 150,
                },
                {
                    value: "user.smsEnabled",
                    text: "User Sms Enabled",
                },
                {
                    value: "user.tags",
                    text: "User Tags",
                },
                {
                    value: "user.address.city",
                    text: "User City",
                },
                {
                    value: "user.address.stateCode",
                    text: "User State Code",
                },
                {
                    value: "user.address.zipCode",
                    text: "User Zip Code",
                },
                {
                    value: "user.source.hostname",
                    text: "User Source Host",
                },
                {
                    value: "user.createdDate",
                    text: "User Created Date",
                },
                {
                    value: "dealer.group.name",
                    text: "Dealer Group",
                    sortable: false,
                },
                {
                    value: "dealer.name",
                    text: "Dealer",
                    sortable: true,
                },
                {
                    value: "dealer.dealerId",
                    text: "Dealer Id",
                },
                {
                    value: "dealer.salesTxSource",
                    text: "Dealer Sales Matching",
                },
                {
                    value: "dealer.successManager",
                    text: "Dealer Success Manager",
                },
                {
                    value: "deliveryDate",
                    text: "Delivery Date",
                    sortable: true,
                    width: 125,
                },
                {
                    value: "vehicle",
                    text: "Vehicle",
                    sortable: false,
                },
                {
                    value: "vehicle.vin",
                    text: "VIN",
                },
                {
                    value: "vehicle.stockType",
                    text: "Stock Type",
                },
                {
                    value: "vehicle.year",
                    text: "Year",
                },
                {
                    value: "vehicle.make",
                    text: "Make",
                },
                {
                    value: "vehicle.model",
                    text: "Model",
                },
                {
                    value: "lifetimeWarrantyStatus",
                    text: "Warranty",
                    sortable: false,
                },
                {
                    value: "user.source.utmSource",
                    text: "UTM Source",
                },
                {
                    value: "user.source.utmMedium",
                    text: "UTM Medium",
                },
                {
                    value: "user.source.utmCampaign",
                    text: "UTM Campaign",
                },
                {
                    value: "user.source.utmTerm",
                    text: "UTM Term",
                },
                {
                    value: "user.source.utmContent",
                    text: "UTM Content",
                },
                {
                    value: "createdDate",
                    text: "Created Date",
                    sortable: true,
                },
            ],
        };
    },

    computed: {
        apiSearchResults: get("saleSearch/searchLoader@data"),
        pageNumber: get("saleSearch/pageMetadata@number"),
        pageSize: get("saleSearch/pageMetadata@size"),
        totalPages: get("saleSearch/pageMetadata@totalPages"),
        totalElements: get("saleSearch/pageMetadata@totalElements"),
        isLoading: get("saleSearch/<EMAIL>"),
        page: sync("saleSearch/pageable@page"),
        sort: sync("saleSearch/pageable@sort"),
        displayFields: sync("saleSearch/displayFields"),
        exportFields: sync("saleSearch/exportFields"),
        exportLabels: sync("saleSearch/exportLabels"),
        sortBy: get("saleSearch/getSortBy"),
        sortDesc: get("saleSearch/getSortDesc"),
        filterDealerIds: sync("saleSearch/filters@dealerIds"),
        userId: get("loggedInUser/userId"),
        fieldsForDisplay() {
            return tableUtils.intersectFieldsForDisplay(this.headers, this.displayFields);
        },
        searchResults() {
            return _.map(this.apiSearchResults, (searchResult) => {
                const item = { ...searchResult };
                item.userFullName = _.get(item, "user.firstName", "") + " " + _.get(item, "user.lastName", "");
                const userId = _.get(item, "user.id", null);
                item.userDetailsLink = userId ? this.customerLink(userId) : null;
                item.vehicleCertified = _.get(item, "vehicle.certified", false);
                item.assigneeFullName =
                    _.get(item, "dealerLink.assignee.firstName", "") +
                    " " +
                    _.get(item, "dealerLink.assignee.lastName", "");
                return item;
            });
        },
        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
    },

    mounted() {
        this.filterDealerIds = this.dealerIds;
        this.doPageLoad();
        this.loadFilterInfo();
        this.addUserSpecificFields();
    },
    methods: {
        doPageLoad: call("saleSearch/doPageLoad"),
        loadFilterInfo: call("saleSearch/loadFilterInfo"),
        doSort: call("saleSearch/doSort"),
        changePage: call("saleSearch/changePage"),
        updateSort: call("saleSearch/updateSort"),
        doExport: call("saleSearch/doExport"),
        prepareExport() {
            this.exportLabels.length = 0;
            for (let field of this.exportFields) {
                let header = _.find(this.headers, function (o) {
                    return _.toString(o.value) === _.toString(field);
                });
                if (header === undefined) {
                    console.error("Error setting label for export, FIELD: ", field);
                    return;
                }
                this.exportLabels = [...this.exportLabels, header.text];
            }
            this.doExport();
        },
        updateOptions(options) {
            const newSortBy = _.get(options, "sortBy[0]") || "";
            const newSortDesc = _.get(options, "sortDesc[0]") || false;

            if (newSortBy === this.sortBy && newSortBy === "") {
                return;
            }

            if (newSortBy !== this.sortBy || newSortDesc !== this.sortDesc) {
                if (newSortBy === "" || newSortDesc === "") {
                    this.updateSort("");
                } else {
                    const direction = newSortDesc ? "desc" : "asc";
                    this.updateSort(`${newSortBy},${direction}`);
                }
            }
        },
        hasSaleDetailsAccess() {
            return (
                this.$acl.hasDealerPermission(this.dealerIds, "customer:read") || this.$acl.hasAuthority("read:user")
            );
        },
        statusCss(status) {
            switch (status) {
                case "DUPLICATE":
                    return "";
                case "INVALID":
                    return "error";
                case "CREDITED":
                    return "info";
                case "VERIFIED":
                    return "success";
                case "UNVERIFIED":
                    return "warning";
                case "INCOMPLETE":
                    return "warning";
                default:
                    return "";
            }
        },
        customerLink(userId) {
            return { path: `/customers/${userId}`, query: { dealerIds: this.dealerIds } };
        },
        addUserSpecificFields() {
            const hasAccountNumberPermission = this.isProgramUser && this.hasAccountNumberAccess(this.userId);

            if (this.isProgramUser) {
                this.headers.push(
                    {
                        value: "financeAppSubmitted",
                        text: "Finance App Submitted",
                        sortable: true,
                    },
                    {
                        value: "amountFinanced",
                        text: "Amount Financed",
                        sortable: true,
                    }
                );
            }

            if (hasAccountNumberPermission) {
                this.headers.push({
                    value: "prospectId",
                    text: "Prospect ID",
                    sortable: true,
                });
            }
        },
        formatFinanceAppSubmitted(val) {
            return val === true ? "Yes" : "No";
        },
        hasAccountNumberAccess(userId) {
            return this.$acl.hasProgramPermission(userId, "sales:account-number:read");
        },
    },
};
</script>
