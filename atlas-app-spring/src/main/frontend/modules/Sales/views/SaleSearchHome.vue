<template>
    <search-page :key="dealerIds">
        <sale-search-form slot="searchForm" />
        <sale-list :key="dealerIds" slot="searchList" :store="store" :dealer-ids="dealerIds" />
        <sale-facets :key="dealerIds" slot="searchFacets" />
    </search-page>
</template>
<script>
import { call } from "vuex-pathify";
import SearchPage from "Components/Search";
import SaleFacets from "@/modules/Sales/components/SaleSearch/SaleFacets";
import SaleList from "@/modules/Sales/components/SaleSearch/SaleList";
import SaleSearchForm from "@/modules/Sales/components/SaleSearch/SaleSearchForm";

export default {
    name: "SaleSearchHome",
    components: { SaleFacets, SearchPage, SaleList, SaleSearchForm },
    data: () => ({
        store: "saleSearch",
    }),
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
    },
    created() {
        this.setSearchUri(`/sales`);
    },
    methods: {
        setSearchUri: call("saleSearch/setSearchUri"),
    },
};
</script>
