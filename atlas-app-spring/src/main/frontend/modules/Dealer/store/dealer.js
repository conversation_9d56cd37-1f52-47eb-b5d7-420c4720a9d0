import { make } from "vuex-pathify";
import loader from "@/util/loader";
import api from "@/util/api.js";
import Vue from "vue";
import { get, filter, first, isEmpty, set } from "lodash";
import isNil from "lodash/isNil";

const initialState = {
    buyAtHomeProgramId: null,
    buyAtHomeProgramName: null,
    dealerChatConfig: {
        alertMessage: false,
        data: null,
        loader: loader.defaultState(),
        showChatOptions: false,
    },
    enabledLmsList: {
        data: [],
        loader: loader.defaultState(),
    },
    financiers: {
        data: [],
        loader: loader.defaultState(),
        refreshButtonLoader: loader.defaultState(),
        saveButtonLoader: loader.defaultState(),
    },
    libertyMutualProgramName: null,
    nissanProgram: null,
    programUrls: {
        data: null,
        loader: loader.defaultState(),
    },
    programs: null,
    selectedDealer: {
        data: null,
        loader: loader.defaultState(),
    },
};

const getters = {
    ...make.getters(initialState),
    selectedDealerId: (state) => {
        return state.selectedDealer.data.id;
    },
    isProgramWithNissanBuyAtHome: (state) => {},
    isDealerEnrolledInNissanBuyAtHome: (state) => {
        const data = state.programs;
        if (!data) return false;
        const filtered = data.filter((item) => item?.isNissanBuyAtHome === true);
        return !isNil(filtered) && !isEmpty(filtered);
    },
};

const mutations = {
    ...make.mutations(initialState),
    SET_SELECTED_DEALER_LOADER(state, payload) {
        _.set(state, "selectedDealer.loader", payload);
    },
    SET_SELECTED_DEALER_DATA(state, payload) {
        _.set(state, "selectedDealer.data", payload);
    },
    SET_SELECTED_DEALER_FEES(state, payload) {
        _.set(state, "selectedDealer.data.fees", payload);
    },
    SET_ENABLED_LMS_LIST(state, payload) {
        _.set(state, "enabledLmsList.data", payload);
    },
    SET_ENABLED_LMS_LIST_LOADER(state, payload) {
        _.set(state, "enabledLmsList.loader", payload);
    },
    APPEND_TO_SELECTED_DEALER_FEES(state, payload) {
        state.selectedDealer.data.fees.push(payload);
    },
    SAVE_SELECTED_DEALER_FEE_IN_PLACE(state, payload) {
        state.selectedDealer.data.fees = _.map(state.selectedDealer.data.fees, (fee) => {
            return fee.id === payload.id ? payload : fee;
        });
    },
    REMOVE_SELECTED_DEALER_FEE_IN_PLACE(state, payload) {
        state.selectedDealer.data.fees = state.selectedDealer.data.fees.filter((n) => {
            return n.id !== payload.id;
        });
    },
    SET_SELECTED_DEALER_PROGRAMS(state, payload) {
        Vue.set(state, "programs", payload);
        const program = state.programs.filter((pro) => pro.isChatFeatureEnabled);
        if (!isNil(program) && !isEmpty(program)) {
            let nissanProgram = first(program);
            set(state, "dealerChatConfig.data", nissanProgram.dealerChatConfig);
        }
    },
    SET_BUY_AT_HOME_PROGRAM_NAME(state, payload) {
        Vue.set(state, "buyAtHomeProgramName", payload);
    },
    SET_BUY_AT_HOME_PROGRAM_ID(state, payload) {
        Vue.set(state, "buyAtHomeProgramId", payload);
    },
    UPDATE_PROGRAM_FEATURE(state, payload) {
        const program = state.programs.find((program) => program.programId === payload.programId);
        if (!program) return;

        switch (payload.configType) {
            case "route_one_finance_and_insurance":
                program.isRouteOneFAndIEnabled = !!payload.data.isRouteOneFAndIEnabled;
                program.isNesnaFAndIEnabled = !program.isRouteOneFAndIEnabled;
                break;
            case "nesna_finance_and_insurance":
                program.isNesnaFAndIEnabled = !!payload.data.isNesnaFAndIEnabled;
                program.isRouteOneFAndIEnabled = !program.isNesnaFAndIEnabled;
                break;
            case "disable_protection_products_global_toggle":
                program.isRouteOneFAndIEnabled = false;
                program.isNesnaFAndIEnabled = false;
                break;
            case "spanish_translation":
                program.isSpanishTranslationEnabled = !!payload.data.isSpanishTranslationEnabled;
                break;
            case "dealer_track":
                program.isDealerTrackEnabled = !!payload.data.isDealerTrackEnabled;
                break;
            default:
                console.log("No Config Type To Update");
        }
    },
    UPDATE_DEALER_PROGRAM_FEATURE_TOGGLE(state, payload) {
        // Sync the DB call setting with the store setting in case there is some sort of error.
        const programName = get(state, "buyAtHomeProgramName");
        const program = filter(state.programs, { name: programName });

        if (!isNil(program) && !isEmpty(program)) {
            let xProgram = first(program);

            switch (payload.configType) {
                case "chat":
                    let stateConfigChat = get(xProgram, "config.isChatEnabled");
                    const payloadConfigChat = get(payload, "config.isChatEnabled");
                    const dealerChatConfig = get(payload, "config.dealerChatConfig");
                    // preventing an alias situation by using a boolean result vs value
                    stateConfigChat = payloadConfigChat ? true : false;
                    set(state, "dealerChatConfig.data", dealerChatConfig);
                    break;
                case "liberty_mutual":
                    let stateConfigLibertyMutual = get(xProgram, "config.isLibertyMutualEnabled");
                    const payloadConfigLibertyMutual = get(payload, "config.isLibertyMutualEnabled");
                    // preventing an alias situation by using a boolean result vs value
                    stateConfigLibertyMutual = payloadConfigLibertyMutual ? true : false;
                    break;
                case "route_one_finance_and_insurance":
                    let stateConfigRouteOneFAndI = get(xProgram, "config.isRouteOneFAndIEnabled");
                    const payloadConfigRouteOneFAndI = get(payload, "config.isRouteOneFAndIEnabled");
                    // preventing an alias situation by using a boolean result vs value
                    stateConfigRouteOneFAndI = payloadConfigRouteOneFAndI ? true : false;
                    break;
                default:
                    console.log("No Config Type To Update");
            }
        }
    },
    SET_DEALER_CHAT_CONFIG(state, payload) {
        set(state, "dealerChatConfig.data", payload);
    },
    SET_DEALER_CHAT_CONFIG_LOADER(state, payload) {
        set(state, "dealerChatConfig.loader", payload);
    },
    SET_DEALER_CHAT_CONFIG_ALERT_MESSAGE(state, payload) {
        set(state, "dealerChatConfig.alertMessage", payload);
    },
    SET_DEALER_CHAT_CONFIG_SHOW_CHAT_OPTIONS(state, payload) {
        set(state, "dealerChatConfig.showChatOptions", payload);
    },
    SET_LMS_PREFERENCE(state, payload) {
        set(state, "selectedDealer.data.preferences.lmsPreference", payload);
    },
    SET_SELECTED_DEALER_DATA_PREFERENCES(state, payload) {
        set(state, "selectedDealer.data.preferences", payload);
    },
    SET_FINANCIERS(state, payload) {
        set(state, "financiers.data", payload);
    },
    SET_FINANCIERS_LOADER(state, payload) {
        set(state, "financiers.loader", payload);
    },
    SET_FINANCIERS_SAVE_BUTTON_LOADER(state, payload) {
        set(state, "financiers.saveButtonLoader", payload);
    },
    SET_FINANCIERS_REFRESH_BUTTON_LOADER(state, payload) {
        set(state, "financiers.refreshButtonLoader", payload);
    },
    SET_PROGRAM_URLS_LOADER(state, payload) {
        set(state, "programUrls.loader", payload);
    },
    SET_SELECTED_DEALER_PROGRAM_URLS(state, payload) {
        // Filter out null values and ensure each program has required properties
        const validPrograms = Array.isArray(payload)
            ? payload.filter(
                  (program) =>
                      program && typeof program === "object" && program.programName && Array.isArray(program.resources)
              )
            : [];
        set(state, "programUrls.data", validPrograms);
    },
};

const actions = {
    ...make.actions(initialState),
    fetchDealer({ commit, state }, dealerId) {
        commit("SET_SELECTED_DEALER_LOADER", loader.started());

        api.get(`/dealer/${dealerId}/dealer-info`)
            .then((response) => {
                commit("SET_SELECTED_DEALER_DATA", response.data);
                commit("SET_SELECTED_DEALER_LOADER", loader.successful());
            })
            .catch((error) => {
                console.log(error);
                commit("SET_SELECTED_DEALER_LOADER", loader.error(error));
            });
    },
    fetchDealerPrograms({ commit, state }, dealerId) {
        commit("SET_SELECTED_DEALER_LOADER", loader.started());

        api.get(`/dealer/${dealerId}/programs`)
            .then((response) => {
                // this.programs = response.data;
                commit("SET_SELECTED_DEALER_PROGRAMS", response.data);
                // this.$store.commit("dealerStore/SET_SELECTED_DEALER_PROGRAMS", this.programs);
                commit("SET_SELECTED_DEALER_LOADER", loader.successful());
            })
            .catch((error) => {
                console.log(error);
                commit("SET_SELECTED_DEALER_LOADER", loader.error(error));
            });
    },
    async fetchDealerFinanciers({ commit, state }, { dealerId }) {
        try {
            commit("SET_FINANCIERS_LOADER", loader.started());
            const { data, status } = await api.get(`/dealer/${dealerId}/financiers`);
            commit("SET_FINANCIERS", sortFinanciersByName(data.dealerFinancierModels));
            commit("SET_LMS_PREFERENCE", data.lmsPreference);
            commit("SET_FINANCIERS_LOADER", loader.successful());
            return { data, status };
        } catch (error) {
            console.log(error);
            commit("SET_FINANCIERS_LOADER", loader.error(error));
            return { data: null, status: error.response.status };
        }
    },
    async saveDealerFinanciers({ commit }, { dealerId, payload }) {
        try {
            commit("SET_FINANCIERS_SAVE_BUTTON_LOADER", loader.started());
            const url = `/dealer/${dealerId}/financiers`;
            const { data, status } = await api.post(url, payload);
            commit("SET_FINANCIERS", sortFinanciersByName(data));
            commit("SET_FINANCIERS_SAVE_BUTTON_LOADER", loader.successful());
            return { data, status };
        } catch (error) {
            console.log(error);
            commit("SET_FINANCIERS_SAVE_BUTTON_LOADER", loader.error(error));
            throw error;
        }
    },
    async refreshDealerFinanciers({ commit, state }, { dealerId }) {
        try {
            commit("SET_FINANCIERS_REFRESH_BUTTON_LOADER", loader.started());
            const { data, status } = await api.post(`/dealer/${dealerId}/financiers/refresh`);
            commit("SET_FINANCIERS", sortFinanciersByName(data));
            commit("SET_FINANCIERS_REFRESH_BUTTON_LOADER", loader.successful());
            console.log(data);
            return { data, status };
        } catch (error) {
            console.log(error);
            commit("SET_FINANCIERS_REFRESH_BUTTON_LOADER", loader.error(error));
            return { data: null, status: error.response.status };
        }
    },
    async fetchEnabledLmsList({ commit }, { dealerId }) {
        commit("SET_ENABLED_LMS_LIST_LOADER", loader.started());

        try {
            const { data, status } = await api.get(`/lms-options?dealerId=${dealerId}`);
            commit("SET_ENABLED_LMS_LIST", data);
            commit("SET_ENABLED_LMS_LIST_LOADER", loader.successful());
            return { data, status };
        } catch (error) {
            console.log(error);
            commit("SET_ENABLED_LMS_LIST_LOADER", loader.error(error));
            return { data: null, status: error.response.status };
        }
    },
    setBuyAtHomeProgramId({ commit, state }, id) {
        commit("SET_BUY_AT_HOME_PROGRAM_ID", id);
    },
    setBuyAtHomeProgramName({ commit, state }, name) {
        commit("SET_BUY_AT_HOME_PROGRAM_NAME", name);
    },
    toggleDealerProgramFeature({ commit, state }, { programId, configType, isEnabled, userId }) {
        const dealerId = state.selectedDealer.data.id;
        const toggleConfigRequest = { configType: configType, isEnabled: isEnabled, userId: userId };
        const url = `/dealer/${dealerId}/programs/${programId}/features/update`;

        return api
            .put(url, toggleConfigRequest)
            .then((response) => {
                // Sync the DB call setting with the store setting in case there is some sort of error.
                commit("UPDATE_DEALER_PROGRAM_FEATURE_TOGGLE", {
                    data: response.data,
                    configType: configType,
                });
                commit("UPDATE_PROGRAM_FEATURE", { programId, data: response.data, configType: configType });
            })
            .catch((error) => {
                console.log(error);
                throw error; // Rethrow the error so it can be caught by the component
            });
    },
    fetchBuyAtHomeProgram({ commit, state }, dealerId) {
        api.get(`/dealer/${dealerId}/buy-at-home-program-subscription`)
            .then((response) => {
                commit("SET_NISSAN_PROGRAM", response.data);
            })
            .catch((error) => {
                console.error(error);
            });
    },
    updateDealerChatConfig({ commit, state }, dealerChatConfigRequest) {
        commit("SET_DEALER_CHAT_CONFIG_LOADER", loader.started());
        const url = `/chat/upsert`;

        api.post(url, dealerChatConfigRequest)
            .then((response) => {
                console.log(response);
                commit("SET_DEALER_CHAT_CONFIG", response.data);
                commit("SET_DEALER_CHAT_CONFIG_LOADER", loader.successful());
                commit("SET_DEALER_CHAT_CONFIG_ALERT_MESSAGE", true);
                setTimeout(() => {
                    commit("SET_DEALER_CHAT_CONFIG_ALERT_MESSAGE", false);
                    commit("SET_DEALER_CHAT_CONFIG_SHOW_CHAT_OPTIONS", false);

                    const program = state.programs.filter((pro) => pro.isChatFeatureEnabled);

                    if (!isNil(program) && !isEmpty(program)) {
                        let nissanProgram = first(program);
                        set(nissanProgram, "dealerChatConfig", response.data);
                    }
                }, 3000);
            })
            .catch((error) => {
                console.log(error);
                commit("SET_DEALER_CHAT_CONFIG_LOADER", loader.error(error));
            });
    },
    toggleDealerChatOptions({ commit, state }, boolean) {
        commit("SET_DEALER_CHAT_CONFIG_SHOW_CHAT_OPTIONS", boolean);
    },
    updateToggleDealerChatFeature({ commit, state }, dealerChatConfigRequest) {
        const url = `/chat/upsert`;
        api.post(url, dealerChatConfigRequest)
            .then((response) => {
                // Sync the DB call setting with the store setting in case there is some sort of error.
                commit("UPDATE_DEALER_PROGRAM_FEATURE_TOGGLE", { data: response.data, configType: "chat" });
            })
            .catch((error) => {
                console.log(error);
            });
    },
    storeLenderManagementSystemPreference({ commit, state }, { lmsPreference }) {
        const dealerId = state.selectedDealer.data.id;
        const url = `/dealer/${dealerId}/preferences/${lmsPreference}`;

        api.patch(url, {})
            .then((response) => {
                const newSelectedDealerPreferences = response.data;
                commit("SET_SELECTED_DEALER_DATA_PREFERENCES", newSelectedDealerPreferences);
            })
            .catch((error) => {
                console.log(error);
            });
    },
    setLmsPreference({ commit, state }, lmsPreference) {
        commit("SET_LMS_PREFERENCE", lmsPreference);
    },
    fetchDealerProgramURLs({ commit, state }, dealerId) {
        commit("SET_PROGRAM_URLS_LOADER", loader.started());

        api.get(`/dealer/${dealerId}/program-urls`)
            .then((response) => {
                commit("SET_SELECTED_DEALER_PROGRAM_URLS", response.data);
                commit("SET_PROGRAM_URLS_LOADER", loader.successful());
            })
            .catch((error) => {
                console.log(error);
                commit("SET_SELECTED_DEALER_PROGRAM_URLS", []);
                commit("SET_PROGRAM_URLS_LOADER", loader.error(error));
            });
    },
};

const sortFinanciersByName = (financiers) => {
    return financiers.sort((a, b) => {
        if (!a.name || a.name === "") return 1;
        if (!b.name || b.name === "") return -1;
        return a.name.localeCompare(b.name);
    });
};

export default {
    namespaced: true,
    state: initialState,
    getters,
    mutations,
    actions,
};
