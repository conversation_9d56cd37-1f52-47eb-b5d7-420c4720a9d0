import Vue from "vue";
import VueRouter from "vue-router";
import { layout, route, routerOptions, configureRouter } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/dealer/details";

const routes = [
    layout("Default", [
        route("Dealer", "DealerHome", null, PATH_PREFIX),
        route("Dealer", "DealerSearchHome", null, "/dealers"),
    ]),
];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
