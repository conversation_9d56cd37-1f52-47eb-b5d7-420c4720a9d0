<template>
    <v-card>
        <v-toolbar flat>
            <v-toolbar-title class="grey--text">Dealers</v-toolbar-title>

            <v-spacer />

            <table-column-selector id="table-column-config" v-model="displayFields" :fields="fields" />
            <export-column-selector
                id="table-column-export"
                v-model="exportFields"
                :display-fields="displayFields"
                :fields="fields"
                @doExport="prepareExport"
            />
        </v-toolbar>

        <v-divider />

        <filter-chips :store="store" />

        <table-search-count-label :page-number="pageNumber" :page-size="pageSize" :total-elements="totalElements" />

        <v-divider />

        <v-data-table
            :loading="isLoading"
            :headers="fieldsForDisplay"
            :items="searchResults"
            :server-items-length="totalElements"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            hide-default-footer
            @update:options="updateOptions"
            >\
            <template #item.inventoryPricingMetrics.percentageNewPriced="{ item }">
                <router-link v-if="item.inventoryPricingMetrics != null" :to="stageRouteNew(item)">
                    {{ item.inventoryPricingMetrics.percentageNewPriced }}
                </router-link>
            </template>
            <template #item.inventoryPricingMetrics.percentageUsedPriced="{ item }">
                <router-link v-if="item.inventoryPricingMetrics != null" :to="stageRouteUsed(item)">
                    {{ item.inventoryPricingMetrics.percentageUsedPriced }}
                </router-link>
            </template>
        </v-data-table>

        <v-container class="pt-2">
            <v-row>
                <v-col cols="12">
                    <v-pagination :value="page" :total-visible="10" :length="totalPages" @input="changePage" />
                </v-col>
            </v-row>
        </v-container>
    </v-card>
</template>
<script>
import { call, get, sync } from "vuex-pathify";
import TableColumnSelector from "Components/TableColumnSelector";
import TableSearchCountLabel from "Components/TableSearchCountLabel";
import tableUtils from "@/util/tableUtils";
import FilterChips from "Components/Search/FilterChips";
import _ from "lodash";
import ExportColumnSelector from "Components/ExportColumnSelector";

export default {
    name: "DealersList",
    components: {
        FilterChips,
        TableColumnSelector,
        TableSearchCountLabel,
        ExportColumnSelector,
    },
    props: {
        store: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            fields: [
                {
                    value: "address.dma.name",
                    text: "DMA",
                    sortable: false,
                },
                {
                    value: "nnaDealerId",
                    text: "Dealer Code",
                    sortable: true,
                },
                {
                    value: "name",
                    text: "Name",
                    sortable: true,
                },
                {
                    value: "address.city",
                    text: "City",
                    sortable: false,
                },
                {
                    value: "address.stateCode",
                    text: "State",
                    sortable: false,
                },
                {
                    value: "group.name",
                    text: "Dealer Group",
                    sortable: false,
                },
                {
                    value: "makes",
                    text: "Brands",
                    sortable: false,
                },
                {
                    value: "inventoryPricingMetrics.percentageNewPriced",
                    text: "% New Inventory Priced",
                    sortable: false,
                },
                {
                    value: "inventoryPricingMetrics.percentageUsedPriced",
                    text: "% Used Inventory Priced",
                    sortable: false,
                },
            ],
        };
    },

    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        pageNumber: get("dealerSearch/pageMetadata@number"),
        pageSize: get("dealerSearch/pageMetadata@size"),
        totalPages: get("dealerSearch/pageMetadata@totalPages"),
        totalElements: get("dealerSearch/pageMetadata@totalElements"),
        isLoading: get("dealerSearch/<EMAIL>"),
        page: sync("dealerSearch/pageable@page"),
        sort: sync("dealerSearch/pageable@sort"),
        displayFields: sync("dealerSearch/displayFields"),
        sortBy: get("dealerSearch/getSortBy"),
        sortDesc: get("dealerSearch/getSortDesc"),
        exportFields: sync("dealerSearch/exportFields"),
        exportLabels: sync("dealerSearch/exportLabels"),
        filterProgramIds: sync("dealerSearch/filters@programIds"),

        fieldsForDisplay() {
            return tableUtils.intersectFieldsForDisplay(this.fields, this.displayFields);
        },
        programIds() {
            return this.$route.query.programIds === "" ? null : this.$route.query.programIds;
        },
    },
    watch: {
        programIds(_val) {
            this.doPageLoad();
        },
    },
    mounted() {
        if (this.isProgramUser()) {
            this.filterProgramIds = this.programIds;
        }
        this.doPageLoad();
    },

    methods: {
        doPageLoad: call("dealerSearch/doPageLoad"),
        doSort: call("dealerSearch/doSort"),
        changePage: call("dealerSearch/changePage"),
        updateSort: call("dealerSearch/updateSort"),
        doExport: call("dealerSearch/doExport"),
        updateOptions(options) {
            const newSortBy = _.get(options, "sortBy[0]") || "";
            const newSortDesc = _.get(options, "sortDesc[0]") || false;

            if (newSortBy === this.sortBy && newSortBy === "") {
                return;
            }

            if (newSortBy !== this.sortBy || newSortDesc !== this.sortDesc) {
                if (newSortBy === "" || newSortDesc === "") {
                    this.updateSort("");
                } else {
                    const direction = newSortDesc ? "desc" : "asc";
                    this.updateSort(`${newSortBy},${direction}`);
                }
            }
        },
        prepareExport() {
            this.exportLabels.length = 0;
            for (let field of this.exportFields) {
                let header = _.find(this.fields, function (o) {
                    return _.toString(o.value) === _.toString(field);
                });
                if (header === undefined) {
                    console.error("Error setting label for export, FIELD: ", field);
                    return;
                }
                this.exportLabels = [...this.exportLabels, header.text];
            }
            this.doExport();
        },
        stageRouteNew(item) {
            return `/vehicles?dealerIds=${item.id}&stockTypes=USED&&negativeSearchMethods=stockTypes`;
        },
        stageRouteUsed(item) {
            return `/vehicles?dealerIds=${item.id}&stockTypes=NEW&&negativeSearchMethods=stockTypes`;
        },
        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
    },
};
</script>
<style lang="scss">
.v-data-table-header th {
    white-space: nowrap !important;
}

span.colorSquare:before {
    content: "";
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 5px;
}
.colorSquare:before {
    vertical-align: middle;
    background-color: var(--vehicle-color-var);
}
</style>
