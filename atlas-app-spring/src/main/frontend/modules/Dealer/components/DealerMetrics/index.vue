<template>
    <v-card :loading="loading" class="fill-height mb-4 d-flex flex-column">
        <v-card-title>Performance</v-card-title>
        <v-card-text v-if="!loading" class="text--primary">
            <v-timeline align-top dense>
                <v-timeline-item
                    v-for="(item, i) in timelineItems"
                    :key="i"
                    :color="item.color"
                    :icon="item.icon"
                    fill-dot
                >
                    <v-card>
                        <v-card-title class="title">
                            {{ item.name }}
                        </v-card-title>
                        <v-card-subtitle>Last 30 days</v-card-subtitle>
                        <v-card-text class="white text--primary">
                            <p>
                                {{ item.total }}
                                Compared to previous 30 days
                            </p>
                        </v-card-text>
                    </v-card>
                </v-timeline-item>
            </v-timeline>
        </v-card-text>
    </v-card>
</template>

<script>
import api from "@/util/api.js";
import _ from "lodash";

export default {
    name: "DealerMetrics",
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            loading: false,
            metrics: {},
        };
    },

    computed: {
        timelineItems() {
            let items = [];

            if (this.metrics.claimedMetrics) {
                items.push({
                    name: "Claimed Leads",
                    icon: "mdi-account-multiple-check",
                    color: "blue",
                    total: _.get(this.metrics.claimedMetrics, "last30Days.total"),
                });
            }

            if (this.metrics.contactedMetrics) {
                items.push({
                    name: "Connected Leads",
                    icon: "mdi-phone-forward",

                    color: "purple lighten-2",
                    total: _.get(this.metrics.contactedMetrics, "last30Days.total"),
                });
            }

            if (this.metrics.salesMetrics) {
                items.push({
                    name: "Sales",
                    icon: "mdi-cash",
                    color: "green",
                    total: _.get(this.metrics.salesMetrics, "last30Days.total"),
                });
            }

            return items;
        },
    },
    mounted() {
        this.fetchMetrics(this.dealerId);
    },
    methods: {
        fetchMetrics(dealerId) {
            api.get(`/dealer/${dealerId}/dealer-metrics`)
                .then((response) => {
                    this.metrics = response.data;
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
    },
};
</script>
