<template>
    <v-data-table :headers="tableHeaders" :items="videos" item-key="id" class="rounded-0" dense>
        <template #item.completedAt="{ item }">
            <boolean-indicator :value="!(item.completedAt == null)" />
            <span v-if="item.completedAt == null"> Not Watched </span>
            <span v-else> {{ item.completedAt | formatEpochDate }} </span>
        </template>
    </v-data-table>
</template>

<script>
import BooleanIndicator from "Components/BooleanIndicator";
export default {
    name: "VideoRow",
    components: { BooleanIndicator },
    props: {
        videos: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            tableHeaders: [
                {
                    text: "Video",
                    sortable: true,
                    value: "title",
                    align: "left",
                },
                {
                    text: "Watched",
                    sortable: true,
                    value: "completedAt",
                },
            ],
        };
    },
};
</script>
