<template>
    <v-data-table
        :headers="tableHeaders"
        :items="videos"
        item-key="id"
        show-expand
        :single-expand="singleExpand"
        :expanded.sync="expanded"
        hide-default-header
        class="pt-2"
        :item-class="clickableRowStyle"
        @click:row="clickedRow"
    >
        <template #item.title="{ item }">
            <span><span class="font-weight-bold"> Video:</span> {{ item.title }}</span>
        </template>
        <template #expanded-item="{ headers, item }">
            <td :colspan="headers.length" class="px-0">
                <user-row :users-records="item.dealerUsersRecords" />
            </td>
        </template>
    </v-data-table>
</template>

<script>
import UserRow from "@/modules/Dealer/components/TrainingCourses/ByCourse/UserRow";
export default {
    name: "VideoRow",
    components: { UserRow },
    props: {
        videos: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            tableHeaders: [
                {
                    text: "title",
                    sortable: true,
                    value: "title",
                    align: "left",
                },
            ],
            expanded: [],
            singleExpand: true,
        };
    },
    methods: {
        clickedRow(row) {
            if (_.includes(this.expanded, row)) {
                this.expanded = [];
            } else {
                this.expanded = [row];
            }
        },
        clickableRowStyle(item) {
            return "clickable-row";
        },
    },
};
</script>

<style lang="scss">
.clickable-row {
    cursor: pointer;
}
</style>
