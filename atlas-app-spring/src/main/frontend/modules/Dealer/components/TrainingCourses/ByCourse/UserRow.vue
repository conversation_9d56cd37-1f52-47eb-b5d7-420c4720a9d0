<template>
    <v-data-table :headers="tableHeaders" :items="usersRecords" item-key="id" class="pt-2 rounded-0" dense>
        <template #item.email="{ item }">
            <a :href="`mailto:${item.email}`">
                {{ item.email }}
            </a>
        </template>
        <template #item.completedAt="{ item }">
            <boolean-indicator :value="!(item.completedAt == null)" />
            <span v-if="item.completedAt == null"> Not Watched </span>
            <span v-else> {{ item.completedAt | formatEpochDate }} </span>
        </template>
    </v-data-table>
</template>

<script>
import BooleanIndicator from "Components/BooleanIndicator";
export default {
    name: "UserRow",
    components: { BooleanIndicator },
    props: {
        usersRecords: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            tableHeaders: [
                {
                    text: "User Name",
                    sortable: true,
                    value: "fullName",
                    align: "left",
                },
                {
                    text: "Email",
                    sortable: true,
                    value: "email",
                    align: "left",
                },
                {
                    text: "Watched",
                    sortable: true,
                    value: "completedAt",
                },
            ],
        };
    },
};
</script>
