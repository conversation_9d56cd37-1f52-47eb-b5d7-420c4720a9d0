<template>
    <div>
        {{ getDealerValueOrPlaceholder() }}
    </div>
</template>

<script>
import _ from "lodash";

export default {
    name: "CardValue",
    props: {
        obj: {
            type: Object,
            required: true,
        },
        property: {
            type: String,
            required: true,
        },
        appender: {
            type: String,
            required: false,
            default: "",
        },
    },
    methods: {
        getDealerValueOrPlaceholder() {
            const placeholder = "--";
            const foundValue = _.get(this.obj, this.property);
            const appender = _.get(this, "appender", "");

            // value is not null or blank or undefined
            if (foundValue && typeof foundValue !== "undefined" && !_.isEmpty(foundValue + "")) {
                return `${foundValue}${appender}`;
            } else {
                return placeholder;
            }
        },
    },
};
</script>

<style scoped></style>
