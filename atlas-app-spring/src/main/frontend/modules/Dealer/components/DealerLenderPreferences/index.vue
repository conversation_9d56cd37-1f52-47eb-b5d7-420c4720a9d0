<template>
    <v-card class="lender-preferences" :loading="financiersLoader.isLoading || enabledLmsListLoader.isLoading">
        <v-card-title class="lender-preferences-title">
            <h3 class="lender-preferences-title-text">Lender Preferences</h3>
            <v-btn
                color="primary"
                class="text-uppercase"
                :loading="financiersSaveButtonLoader.isLoading || enabledLmsListLoader.isLoading"
                :disabled="!enabledSave"
                @click="save"
            >
                Save
            </v-btn>
        </v-card-title>
        <v-card-text class="lender-container">
            <v-card class="lender-select-container">
                <v-card-text class="lender-select">
                    <p class="description">
                        Please select your preferred Lender Management System below. Your selection will determine the
                        system used to facilitate finance applications with your selected lenders.
                    </p>
                    <v-select
                        v-model="preferencesOption"
                        class="dropdown"
                        label="LMS Preference"
                        :items="enabledLmsList"
                        outlined
                        dense
                        hide-details
                    >
                    </v-select>
                    <transition name="slide-fade">
                        <Alerts v-if="customAlert.display" :mode="customAlert.type" :message="customAlert.message" />
                    </transition>
                </v-card-text>
            </v-card>
            <v-card class="lender-list-container">
                <v-card-title class="leader-list-title">
                    <h3 class="title">Lender</h3>
                    <div class="action">
                        <p class="action-text">
                            <span class="action-text-property">Last Updated: </span>
                            <span class="action-text-value">{{ lastUpdatedDateFormatted }}</span>
                        </p>
                        <v-btn
                            color="primary"
                            class="text-uppercase"
                            :loading="financiersRefreshButtonLoader.isLoading"
                            @click="handleRefreshList"
                        >
                            Refresh list
                            <v-icon right dark> mdi-autorenew </v-icon>
                        </v-btn>
                    </div>
                </v-card-title>
                <v-card-text class="leader-list-table-container">
                    <p class="leader-list-table-info">To enable a Lender below, they need to be active in your LMS.</p>
                    <v-form ref="form" v-model="valid">
                        <v-data-table
                            :headers="getTableHeaders"
                            :items="items"
                            item-key="id"
                            class="leader-list-table"
                            :expanded="expandedRowsMapping"
                            :loading="financiersRefreshButtonLoader.isLoading || financiersLoader.isLoading"
                        >
                            <template #item.name="{ item }">
                                <div
                                    :class="{ 'disabled-state': !isItemEnabled(preferencesOption, item) }"
                                    class="lender-list-table-name"
                                >
                                    {{ item.name }}
                                    <v-tooltip v-if="item.lenderDeskCodesConfigured === false" bottom>
                                        <template #activator="{ on, attrs }">
                                            <v-icon v-bind="attrs" v-on="on"> mdi-information-outline</v-icon>
                                        </template>
                                        <span>
                                            Published Programs are not <br />
                                            available for this Lender. <br />
                                            Please reach out to your <br />
                                            Performance Manager for <br />
                                            additional information.
                                        </span>
                                    </v-tooltip>
                                </div>
                            </template>
                            <template #item.enabled="{ item }">
                                <v-switch
                                    :input-value="item.enabled"
                                    :label="item.enabled ? 'On' : 'Off'"
                                    :disabled="!isItemEnabled(preferencesOption, item) || toggleTransitionPeriod"
                                    class="switch-input"
                                    @click.stop
                                    @change="handleActiveToggle($event, item.id)"
                                ></v-switch>
                            </template>
                            <template #item.dealerTrackId="{ item }">
                                <div v-if="item.dealerTrackId" class="d-flex flex-column">
                                    <span :class="{ 'disabled-state': preferencesOption !== 'DealerTrack' }">
                                        {{ item.dealerTrackId }}
                                    </span>
                                    <v-tooltip bottom>
                                        <template #activator="{ on, attrs }">
                                            <v-chip
                                                v-if="item.dealerTrackEnabled"
                                                color="#60B044"
                                                dark
                                                small
                                                class="chip-style"
                                                :disabled="preferencesOption !== 'DealerTrack'"
                                                v-bind="attrs"
                                                v-on="on"
                                            >
                                                Active
                                            </v-chip>
                                        </template>
                                        <span>This lender is active in your LMS</span>
                                    </v-tooltip>
                                </div>
                                <div v-else>-</div>
                            </template>
                            <template #item.providerId="{ item }">
                                <div v-if="item.routeOneId" class="d-flex flex-column">
                                    <span :class="{ 'disabled-state': preferencesOption !== 'RouteOne' }">
                                        {{ item.routeOneId }}
                                    </span>
                                </div>
                                <div v-else>-</div>
                                <v-tooltip v-if="item.providerId" bottom>
                                    <template #activator="{ on, attrs }">
                                        <v-chip
                                            color="#60B044"
                                            dark
                                            small
                                            class="chip-style"
                                            :disabled="preferencesOption !== 'RouteOne'"
                                            v-bind="attrs"
                                            v-on="on"
                                        >
                                            Active
                                        </v-chip>
                                    </template>
                                    <span>This lender is active in your LMS</span>
                                </v-tooltip>
                            </template>
                            <template #item.newMaxLenderFee="{ item }">
                                <div class="flex-row-gap-3">
                                    <span class="dollar">$</span>
                                    <v-text-field
                                        placeholder="New Max Lender Fee"
                                        :value="formatIntoCurrency(item.newMaxLenderFee)"
                                        class="lender-fee-input"
                                        :disabled="!item.enabled || !isItemEnabled(preferencesOption, item)"
                                        :rules="[
                                            rules.required('New Max Lender Fee'),
                                            rules.validAmount,
                                            rules.currency,
                                        ]"
                                        @input="handleNewMaxLenderFee($event, item.id)"
                                    />
                                </div>
                            </template>
                            <template #item.usedMaxLenderFee="{ item }">
                                <div class="flex-row-gap-3">
                                    <span class="dollar">$</span>
                                    <v-text-field
                                        placeholder="Used Max Lender Fee"
                                        :value="formatIntoCurrency(item.usedMaxLenderFee)"
                                        class="lender-fee-input"
                                        :disabled="!item.enabled || !isItemEnabled(preferencesOption, item)"
                                        :rules="[
                                            rules.required('Used Max Lender Fee'),
                                            rules.validAmount,
                                            rules.currency,
                                        ]"
                                        @input="handleUsedMaxLenderFee($event, item.id)"
                                    />
                                </div>
                            </template>
                            <template #expanded-item="{ item }">
                                <td v-if="itemInExpandedList(item.id)" :colspan="getTableHeaders.length">
                                    <Alerts mode="error" :message="getExpandedItemMessageById(item.id)" />
                                </td>
                            </template>
                        </v-data-table>
                    </v-form>
                </v-card-text>
            </v-card>
        </v-card-text>
    </v-card>
</template>

<script>
import lodashIsEqual from "lodash/isEqual";
import moment from "moment";
import Alerts from "Components/Alerts.vue";
import { camelCaseToSpaced, convertArrayToSentence } from "Util/globalHelpers";
import lodashGet from "lodash/get";
import { call, get } from "vuex-pathify";
import { formatIntoCurrency, parseCurrency } from "Util/helpers";
import { currency, required } from "Util/formRules";

export default {
    name: "DealerLenderPreferences",
    components: { Alerts },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            dealerTrackEnabledList: [],
            routeOneEnabledList: [],
            items: [],
            isLoading: false,
            preferencesOption: "",
            expandedRows: [],
            lastUpdated: "",
            toggleTransitionPeriod: false,
            customAlert: {
                display: false,
                type: "success",
                message: "Your changes have been successfully saved.\n",
            },
            fixedTotalEnabled: 4,
            isFormDirty: false,
            valid: true,
            minFee: 0,
            maxFee: 99999,
        };
    },
    computed: {
        savedFinancierData: get("dealerStore/financiers@data"),
        financiersLoader: get("dealerStore/financiers@loader"),
        enabledLmsList: get("dealerStore/enabledLmsList@data"),
        enabledLmsListLoader: get("dealerStore/enabledLmsList@loader"),
        financiersSaveButtonLoader: get("dealerStore/financiers@saveButtonLoader"),
        financiersRefreshButtonLoader: get("dealerStore/financiers@refreshButtonLoader"),
        getLmsPreference: get("dealerStore/<EMAIL>"),
        getTableHeaders() {
            return [
                {
                    text: "Name",
                    sortable: true,
                    value: "name",
                    align: "left",
                },
                {
                    text: `Enabled (${this.getTotalEnabled}/${this.fixedTotalEnabled})`,
                    sortable: true,
                    value: "enabled",
                    align: "left",
                },
                {
                    text: "Dealertrack Id",
                    sortable: true,
                    value: "dealerTrackId",
                },
                {
                    text: "Route One Id",
                    sortable: true,
                    value: "providerId",
                    align: "left",
                },
                {
                    text: "New Max Lender Fee",
                    sortable: true,
                    value: "newMaxLenderFee",
                },
                {
                    text: "Used Max Lender Fee",
                    sortable: true,
                    value: "usedMaxLenderFee",
                },
            ];
        },
        rules() {
            return {
                required,
                validAmount: (value) =>
                    (Number(this.parseCurrency(value)) >= this.minFee &&
                        Number(this.parseCurrency(value)) <= this.maxFee) ||
                    "Amount must be between $0 and $99,999",
                currency,
            };
        },
        expandedRowsMapping() {
            return this.expandedRows.map(({ id }) => this.items.find((item) => item.id === id));
        },
        getTotalEnabled() {
            if (this.preferencesOption === "DealerTrack") {
                return this.dealerTrackEnabledList.length;
            } else if (this.preferencesOption === "RouteOne") {
                return this.routeOneEnabledList.length;
            }

            console.trace("Unable to get total enabled lenders");
            return 0;
        },
        lastUpdatedDateFormatted() {
            return moment(this.lastUpdated).format("MM/DD/YYYY [at] hh:mma");
        },
        exceededTotalLimit() {
            return this.getTotalEnabled > this.fixedTotalEnabled;
        },
        isLmsPreferencesChanged() {
            return this.preferencesOption !== this.getLmsPreference;
        },
        enabledSave() {
            return this.isLmsPreferencesChanged || (this.isFormDirty && this.valid && !this.exceededTotalLimit);
        },
    },
    watch: {
        getLmsPreference: {
            handler(val) {
                this.preferencesOption = val;
            },
            immediate: true,
        },
        items: {
            deep: true,
            handler(val) {
                this.dealerTrackEnabledList = this.items.filter((item) => item.dealerTrackEnabled && item.enabled);
                this.routeOneEnabledList = this.items.filter((item) => item.providerId && item.enabled);

                if (!lodashIsEqual(this.savedFinancierData, this.items)) {
                    this.$refs.form.validate();
                    this.isFormDirty = true;
                } else {
                    this.isFormDirty = false;
                }
            },
        },
    },
    async mounted() {
        await this.fetchLenders();
        await this.fetchEnabledLms();
        this.expandInactiveRow();
    },
    methods: {
        formatIntoCurrency,
        parseCurrency,
        fetchEnabledLmsList: call("dealerStore/fetchEnabledLmsList"),
        setLmsPreference: call("dealerStore/setLmsPreference"),
        fetchDealerFinanciers: call("dealerStore/fetchDealerFinanciers"),
        saveDealerFinanciers: call("dealerStore/saveDealerFinanciers"),
        isItemEnabled(preferencesOption, item) {
            if (!item) {
                console.trace("Invalid item provided in isItemEnabled");
                return false;
            }

            let enabled = false;
            const providerId = item.providerId;
            const routeOneEnabled = !(providerId === null || providerId === undefined || providerId === "");
            const dealerTrackEnabled = item.dealerTrackEnabled;
            const lmsPreference = preferencesOption.toUpperCase();
            const routeOne = "RouteOne".toUpperCase();
            const dealerTrack = "DealerTrack".toUpperCase();

            if (lmsPreference === routeOne) {
                enabled = routeOneEnabled;
            } else if (lmsPreference === dealerTrack) {
                enabled = dealerTrackEnabled;
            } else {
                console.trace("Invalid LMS Preference");
            }
            return enabled;
        },
        handleNewMaxLenderFee(val, id) {
            const selectedLender = this.items.find((item) => item.id === id);
            selectedLender.newMaxLenderFee = this.parseCurrency(val);
        },
        handleUsedMaxLenderFee(val, id) {
            const selectedLender = this.items.find((item) => item.id === id);
            selectedLender.usedMaxLenderFee = this.parseCurrency(val);
        },
        itemInExpandedList(id) {
            return this.expandedRows.some((item) => item.id === id);
        },
        showLenderNotActive(item) {
            return !item.providerId && !item.dealerTrackEnabled;
        },
        expandInactiveRow() {
            this.items.forEach((item) => {
                if (this.showLenderNotActive(item)) {
                    this.expandedRows = [
                        {
                            id: item.id,
                            message:
                                "This lender is not active in your LMS Preferred System. To enable" +
                                " this lender, please activate in your LMS.",
                        },
                        ...this.expandedRows,
                    ];
                }
            });
        },
        removeExpandedRowAndResetToggle(lenderListId) {
            const lenderList = this.items.find((item) => item.id === lenderListId);
            const index = this.expandedRows.findIndex((item) => item.id === lenderListId);
            if (index !== -1) {
                this.expandedRows.splice(index, 1);
                if (lenderList) lenderList.enabled = false; // Reset the toggle back to off
            }
        },
        handleActiveToggle(value, id) {
            const selectedLender = this.items.find((item) => item.id === id);
            if (value && this.getTotalEnabled >= this.fixedTotalEnabled) {
                this.expandedRows = [
                    {
                        id,
                        message:
                            "A maximum of 4 lenders can be enabled. Please disable one" +
                            " of your other selections to enable this one.",
                    },
                    ...this.expandedRows,
                ];

                // Remove Show Error message after 4 sec and reset the toggle
                setTimeout(() => this.removeExpandedRowAndResetToggle(id), 4000);
            }
            selectedLender.enabled = value;
        },
        getExpandedItemMessageById(id) {
            const row = this.expandedRows.find((row) => row.id === id);
            return row ? row.message : "Something is wrong";
        },
        async handleRefreshList() {
            try {
                await this.fetchDealerFinanciers({ dealerId: this.dealerId });
                this.items = JSON.parse(JSON.stringify(this.savedFinancierData));
                this.lastUpdated = new Date();
            } catch (error) {
                console.error(error);
            }
        },
        async fetchEnabledLms() {
            try {
                await this.fetchEnabledLmsList({ dealerId: this.dealerId });
            } catch (error) {
                console.error(error);
            }
        },

        async fetchLenders() {
            try {
                await this.fetchDealerFinanciers({ dealerId: this.dealerId });
                this.items = JSON.parse(JSON.stringify(this.savedFinancierData));
                this.lastUpdated = new Date();
            } catch (error) {
                console.error(error);
            }
        },

        formatErrorMessages(errorList, payloadLenders) {
            return errorList.map((err) => {
                const fieldParts = err.field.split(".");
                const index = parseInt(fieldParts[0].match(/\d+/)[0], 10);
                const financierId = payloadLenders[index].financierId;
                const financierName = this.items.find((item) => item.financierId === financierId)?.name || "";
                return `${camelCaseToSpaced(fieldParts[1])} of ${financierName} ${err.message}`;
            });
        },

        async save() {
            const valid = this.$refs.form.validate();
            if (!valid) return;

            const payload = {
                lmsPreference: this.preferencesOption,
                dealerFinancierModels: this.items.map((item) => ({
                    financierId: item.financierId,
                    enabled: item.enabled,
                    newMaxLenderFee: item.newMaxLenderFee,
                    usedMaxLenderFee: item.usedMaxLenderFee,
                })),
            };
            try {
                const { status } = await this.saveDealerFinanciers({ dealerId: this.dealerId, payload });
                if (status === 200) {
                    this.setLmsPreference(this.preferencesOption);
                    this.customAlert = {
                        display: true,
                        type: "success",
                        message: "Your changes have been successfully saved.\n",
                    };
                } else {
                    this.customAlert = {
                        display: true,
                        type: "error",
                        message: "Something went wrong. Please try again.\n",
                    };
                }
            } catch (error) {
                console.log(error);
                this.$refs.form.validate();
                const errors = lodashGet(error, "response.data.errors", []);
                if (errors.length > 0) {
                    this.customAlert = {
                        display: true,
                        type: "error",
                        message: convertArrayToSentence(
                            this.formatErrorMessages(errors, payload.dealerFinancierModels)
                        ),
                    };
                }
            } finally {
                setTimeout(
                    () => {
                        this.customAlert = {
                            ...this.customAlert,
                            display: false,
                        };
                    },
                    this.customAlert.type === "error" ? 10000 : 4000
                );
            }
        },
    },
};
</script>

<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.lender-preferences {
    *[disabled] {
        cursor: not-allowed;
    }
    .lender-preferences-title {
        display: flex;
        justify-content: space-between;

        .lender-preferences-title-text {
            font-size: px2rem(20);
            font-weight: 500;
        }
    }

    .lender-container {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .lender-select-container {
            padding: 12px 10px;

            .lender-select {
                padding: 0 !important;
                display: flex;
                flex-direction: column;
                gap: 10px;

                .description {
                    margin: 0;
                }
            }

            .dropdown {
                width: 330px;
            }
        }
        .lender-list-container {
            // TABLE
            .v-input__slot {
                label {
                    margin-left: 6px;
                }
            }
            .leader-list-table-container {
                .leader-list-table {
                    margin-top: 8px;
                }
                .leader-list-table-info {
                    font-size: px2rem(14);
                    color: #757575;
                    margin: 8px 0 16px 0 !important;
                }
                .lender-fee-input {
                    .v-input__control > .v-input__slot:before {
                        border-style: none;
                    }

                    .v-text-field__slot {
                        input {
                            border-bottom: 2px dotted #9e9e9e !important;
                        }
                    }

                    &:focus {
                        border: 0;
                        outline: 0;
                        border-bottom: 2px dotted #9e9e9e;
                        color: #666;
                    }
                }
                .lender-list-table-name {
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                }

                .v-data-table {
                    & > .v-data-table__wrapper {
                        tbody {
                            tr.v-data-table__expanded__content {
                                box-shadow: none !important;

                                td {
                                    padding: 0;
                                    border-right: 4px;
                                    .v-alert {
                                        margin: 0 !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            // TABLE

            .leader-list-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .title {
                    font-size: px2rem(20);
                    font-weight: 500;
                }

                .action {
                    display: flex;
                    align-items: center;
                    gap: 24px;

                    .action-text {
                        margin: 0;
                        font-size: px2rem(14);

                        .action-text-property {
                            color: #212121;
                        }

                        .action-text-value {
                            color: #666;
                        }
                    }
                }
            }
        }

        .switch-input {
            font-size: px2rem(16);
            font-weight: 700;
        }
    }
    .flex-row-gap-3 {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .dollar {
        font-size: px2rem(16);
    }

    .chip-style {
        width: 52px !important;
        height: 16px !important;
        font-size: px2rem(10) !important;
        border-radius: 8px;
    }

    .disabled-state {
        color: #c6c6c6;
    }

    .slide-fade-enter-active,
    .slide-fade-leave-active {
        transition: opacity 0.5s, transform 0.5s;
        transition-delay: 100ms;
    }
    .slide-fade-enter {
        opacity: 0;
        transform: translateY(-20px);
    }
    .slide-fade-leave-to {
        opacity: 0;
    }
    .slide-fade-enter-to,
    .slide-fade-leave {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
