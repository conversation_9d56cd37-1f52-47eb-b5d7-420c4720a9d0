<template>
    <v-app id="inspire">
        <v-main>
            <v-container class="fill-height" fluid>
                <v-row align="center" justify="center">
                    <v-col cols="12" sm="8" md="4">
                        <v-card class="elevation-12" :loading="loading">
                            <template slot="progress">
                                <v-progress-linear color="primary" height="10" indeterminate />
                            </template>

                            <v-form ref="form" v-model="valid" lazy-validation @submit.prevent="submit">
                                <v-toolbar color="primary" dark flat>
                                    <v-toolbar-title> Forgot Password </v-toolbar-title>
                                </v-toolbar>
                                <v-card-text>
                                    <div v-if="submitted">
                                        <p>An email has been sent to you with a link to reset your password.</p>
                                    </div>
                                    <div v-else>
                                        <p>
                                            Enter your email address and we'll send you a link to reset your password.
                                        </p>
                                        <v-text-field
                                            v-model="email"
                                            :rules="[rules.required('E-mail'), rules.strictEmail]"
                                            label="E-mail"
                                            :placeholder="!autofilled ? ' ' : ''"
                                            outlined
                                            dense
                                            prepend-icon="mdi-email"
                                            :error-messages="userNotFound ? ['User not found'] : null"
                                            required
                                        />
                                    </div>
                                </v-card-text>
                                <v-card-actions v-if="!submitted" class="justify-end">
                                    <v-btn
                                        :disabled="!valid"
                                        class="px-10"
                                        color="primary"
                                        type="submit"
                                        @click="validate"
                                    >
                                        Next
                                    </v-btn>
                                </v-card-actions>
                            </v-form>
                        </v-card>
                    </v-col>
                </v-row>
            </v-container>
        </v-main>
    </v-app>
</template>

<script>
import api from "Util/api";
import { strictEmail, required } from "Util/formRules";
import { get } from "vuex-pathify";
import lodashGet from "lodash/get";
import { sanitize } from "Util/sanitize";

export default {
    data: () => ({
        email: "",
        valid: true,
        autofilled: false,
        submitted: false,
        userNotFound: false,
        loading: false,
        userId: null,
        invalidToken: false,
        rules: { required, strictEmail },
    }),
    computed: {
        featureFlags: get("loginPageStore/featureFlags"),
        isLoginSanitizationEnabled() {
            return lodashGet(this.featureFlags, "ATLAS_LOGIN_SANITIZATION_ENABLE", false) || false;
        },
    },
    watch: {
        email() {
            this.autofilled = true;
            this.userNotFound = false;
        },
    },
    methods: {
        validate() {
            this.$refs.form.validate();
        },
        submit() {
            this.userNotFound = false;
            this.loading = true;
            if (this.isLoginSanitizationEnabled) {
                this.email = sanitize(this.email);
            }
            api.post("/auth/password-reset", { email: this.email })
                .then(() => {
                    this.submitted = true;
                    this.loading = false;
                })
                .catch(() => {
                    this.loading = false;
                    this.userNotFound = true;
                });
        },
    },
};
</script>
