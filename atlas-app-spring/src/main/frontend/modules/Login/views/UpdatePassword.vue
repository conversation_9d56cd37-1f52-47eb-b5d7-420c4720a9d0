<template>
    <v-app id="inspire">
        <v-main>
            <v-container class="fill-height" fluid>
                <v-row align="center" justify="center">
                    <v-col cols="12" sm="8" md="4">
                        <transition name="slide-left" mode="out-in">
                            <v-card class="elevation-12" :loading="loading">
                                <template slot="progress">
                                    <v-progress-linear color="primary" height="10" indeterminate />
                                </template>
                                <validation-observer ref="observer" v-slot="{ invalid }">
                                    <v-form @submit.prevent="submit">
                                        <v-toolbar color="primary" dark flat>
                                            <v-toolbar-title> Please Change your password </v-toolbar-title>
                                        </v-toolbar>
                                        <v-card-text v-if="!complete">
                                            <v-alert v-if="invalidToken" type="error">
                                                Password reset link has expired.
                                            </v-alert>
                                            <div v-else>
                                                <validation-provider
                                                    v-slot="{ errors }"
                                                    name="password"
                                                    rules="required|password:@confirm"
                                                >
                                                    <v-text-field
                                                        v-model="password"
                                                        label="Password"
                                                        outlined
                                                        dense
                                                        prepend-icon="mdi-lock"
                                                        required
                                                        :error-messages="errors"
                                                        type="password"
                                                    />
                                                </validation-provider>
                                                <validation-provider
                                                    v-slot="{ errors }"
                                                    name="confirm"
                                                    rules="required"
                                                >
                                                    <v-text-field
                                                        v-model="passwordConfirm"
                                                        label="Confirm Password"
                                                        outlined
                                                        dense
                                                        prepend-icon="mdi-lock"
                                                        required
                                                        :error-messages="errors"
                                                        type="password"
                                                    />
                                                </validation-provider>
                                            </div>
                                        </v-card-text>
                                        <v-card-actions v-if="!invalidToken && !complete" class="justify-end">
                                            <v-btn
                                                :disabled="invalid || loading"
                                                class="px-10"
                                                color="primary"
                                                type="submit"
                                            >
                                                Change Password
                                            </v-btn>
                                        </v-card-actions>

                                        <v-card-text v-if="complete">
                                            <p>
                                                You may now <router-link to="/login">login</router-link> with your new
                                                password.
                                            </p>
                                        </v-card-text>
                                        <v-card-actions v-if="complete" class="justify-end">
                                            <v-btn class="px-10" color="primary" to="/login"> Login </v-btn>
                                        </v-card-actions>
                                    </v-form>
                                </validation-observer>
                            </v-card>
                        </transition>
                    </v-col>
                </v-row>
            </v-container>
        </v-main>
    </v-app>
</template>

<script>
import api from "Util/api";
import { required } from "vee-validate/dist/rules";
import { ValidationProvider, ValidationObserver, setInteractionMode, extend } from "vee-validate";

setInteractionMode("eager");

extend("required", {
    ...required,
    message: "{_field_} can not be empty",
});

extend("password", {
    params: ["target"],
    validate(value, { target }) {
        return value === target;
    },
    message: "Password confirmation does not match",
});

export default {
    components: {
        ValidationProvider,
        ValidationObserver,
    },
    data: () => ({
        complete: false,
        loading: true,
        password: "",
        passwordConfirm: "",
        userId: null,
        invalidToken: false,
    }),
    computed: {
        token() {
            return _.get(this.$route, "query.token");
        },
    },
    created() {
        this.verifyToken();
    },
    methods: {
        verifyToken() {
            api.get("/auth/verify-token", { token: this.token })
                .then((response) => {
                    this.userId = _.get(response, "data.userId");
                    this.loading = false;
                })
                .catch(() => {
                    this.userId = null;
                    this.invalidToken = true;
                    this.loading = false;
                });
        },
        submit() {
            if (this.$refs.observer.validate()) {
                this.loading = true;
                api.post("/auth/update-password", {
                    userId: this.userId,
                    token: this.token,
                    password: this.password,
                    passwordConfirm: this.passwordConfirm,
                })
                    .then(() => {
                        this.loading = false;
                        this.complete = true;
                    })
                    .catch(() => {
                        this.loading = false;
                    });
            }
        },
    },
};
</script>
