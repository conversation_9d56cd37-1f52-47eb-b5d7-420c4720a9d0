<template>
    <div class="video-player-section">
        <video ref="video" :key="mp4Source" class="video-player" controls :autoplay="true" @ended="markVideoCompleted">
            <source :src="mp4Source" type="video/mp4" />
            <p>This browser does not support the video element.</p>
        </video>
        <section class="content-section d-flex flex-column flex-md-row">
            <div class="title-block">
                <h3>{{ title }}</h3>
                <p class="completion-status">
                    Completion status: <span :class="{ completedColor: completed }">{{ completionStatus }}</span>
                </p>
            </div>

            <div class="d-flex flex-column">
                <div v-if="description" class="desc-block">
                    <h4>Description:</h4>
                    <p id="video-description">{{ description }}</p>
                </div>

                <div v-if="videoResources">
                    <h4>Additional Resources:</h4>
                    <ul id="additional-resources">
                        <li
                            v-for="(resource, index) in videoResources"
                            :key="index + '-' + resource.linkName"
                            class="my-2"
                        >
                            <a
                                :href="resource.url"
                                target="_blank"
                                :aria-label="`opens new browser window for resource: ${resource.linkName}`"
                                @keypress="openNewResourceWindow(resource.url)"
                            >
                                {{ resource.linkName }}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </section>
    </div>
</template>

<script>
import _ from "lodash";
import { call, get } from "vuex-pathify";

export default {
    name: "VideoPlayer",
    data() {
        return {
            displayVideo: true,
        };
    },
    computed: {
        active: get("trainingStore/activeVideo"),
        value() {
            if (Array.isArray(this.active)) {
                return this.active[0];
            } else {
                return this.active;
            }
        },
        title() {
            return this.value.title;
        },
        description() {
            if (_.isNil(this.value.description) || _.isEmpty(this.value.description)) {
                return false;
            }
            return this.value.description;
        },
        videoResources() {
            return _.get(this.value, "resources", null);
        },
        poster() {
            if (_.isNil(this.value)) {
                return null;
            }

            return this.value.url + ".png";
        },
        mp4Source() {
            if (_.isNil(this.value)) {
                return null;
            }

            return this.value.url + ".mp4";
        },
        completed() {
            return this.value.completedAt ? true : false;
        },
        completionStatus() {
            if (this.value.completedAt) {
                return "Completed";
            } else {
                return "Not yet completed";
            }
        },
    },
    beforeDestroy: function () {
        this.pauseVideo();
    },
    methods: {
        callMarkVideoCompleted: call("trainingStore/markVideoCompleted"),
        markVideoCompleted() {
            if (this.value.completedAt !== null) {
                return;
            }
            this.callMarkVideoCompleted({ videoId: this.value.videoId });
        },
        playVideo() {
            this.displayVideo = true;
            this.$refs.video?.play();
        },
        pauseVideo() {
            this.$refs.video?.pause();
            this.displayVideo = false;
        },
        openNewResourceWindow(url) {
            window.open(url, "_blank");
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.video-player-section {
    width: 100%;
    min-width: 325px;

    .video-player {
        width: 100%;
        margin-bottom: 24px;
    }

    .completion-status {
        color: #444;
        font-size: px2rem(14);

        span {
            font-style: italic;
        }

        .completedColor {
            color: darkgreen;
        }
    }

    .content-section {
        div:not(:last-child) {
            margin-bottom: 16px;
        }

        @media #{map-get($display-breakpoints, 'md-and-up')} {
            div:not(:last-child) {
                margin-right: 16px;
            }
        }
        p {
            margin: 0;
        }
        .title-block {
            min-width: 325px;
        }
        .desc-block {
        }
    }
}
</style>
