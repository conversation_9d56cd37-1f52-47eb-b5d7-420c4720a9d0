<template>
    <div id="app">
        <DomoReport v-if="embedId && embedToken" :embed-id="embedId" :embed-token="embedToken" />
        <p v-else>Loading embed data...</p>
    </div>
</template>

<script>
import DomoReport from "../components/DomoReport.vue";
import { call } from "vuex-pathify";

export default {
    name: "App",
    components: {
        DomoReport,
    },
    data() {
        return {
            embedId: "",
            embedToken: "",
            isLoading: false,
        };
    },
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        programIds() {
            return this.$route.query.programIds;
        },
        dashboardId() {
            return this.$route.query.dashboardId;
        },
    },
    watch: {
        dealerIds: "fetchDomoIdToken",
        programIds: "fetchDomoIdToken",
        dashboardId: "fetchDomoIdToken",
    },
    created() {
        this.fetchDomoIdToken();
    },
    methods: {
        generateDomoToken: call("domoStore/generateDomoToken"),
        fetchDomoIdToken() {
            const params = {
                dealerId: this.dealerIds,
                programId: this.programIds,
                dashboardId: this.dashboardId,
            };
            this.isLoading = true;
            this.generateDomoToken(params)
                .then((response) => {
                    this.embedId = response?.embedId;
                    this.embedToken = response?.embedToken;
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>
