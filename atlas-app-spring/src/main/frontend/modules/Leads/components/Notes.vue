<template>
    <v-card :loading="loading">
        <v-card-title>Notes</v-card-title>
        <v-card-text v-if="!loading">
            <ul v-if="notes.length > 0">
                <li v-for="(note, index) in notes" :key="index">{{ note }}</li>
            </ul>
        </v-card-text>
    </v-card>
</template>

<script>
export default {
    name: "Notes",
    props: {
        lead: {
            type: Object,
            required: true,
        },
        loading: {
            type: Boolean,
            required: true,
        },
    },
    computed: {
        notes() {
            const trade = _.get(this.lead.deal, "trade", {});

            const notes = [];

            if (_.get(trade, "purchaseType") === "LEASE") {
                notes.push("Turning in current lease");
            }

            if (_.get(trade, "purchaseType") === "LEASE" && !_.get(trade, "leaseInspectionScheduled", false)) {
                notes.push("Lease inspection needed");
            }

            return notes;
        },
    },
};
</script>

<style scoped></style>
