<template>
    <v-card :loading="loading">
        <v-card-title>Lead Information</v-card-title>
        <v-card-text v-if="!loading">
            <v-simple-table v-if="lead">
                <template #default>
                    <tbody>
                        <lead-preview-row label="Customer" :display="true">
                            {{ lead.customer.firstName }}
                            {{ lead.customer.lastName }}
                        </lead-preview-row>
                        <lead-preview-row label="Stock #" :display="true">
                            <span v-if="lead.vehicle.inTransit">N/A</span>
                            <span v-else>{{ lead.vehicle.stockNumber }}</span>
                        </lead-preview-row>
                        <lead-preview-row
                            v-if="lead.vehicle.inTransit"
                            label="In Transit"
                            :display="lead.vehicle.inTransit"
                        >
                            <v-chip class="text-uppercase" color="success" small>{{ lead.vehicle.inTransit }}</v-chip>
                        </lead-preview-row>
                        <lead-preview-row
                            v-if="lead.vehicle.dealerEstimatedTimeOfArrival"
                            label="In Transit Est. Delivery Date"
                            :display="lead.vehicle.dealerEstimatedTimeOfArrival"
                        >
                            {{ lead.vehicle.dealerEstimatedTimeOfArrival | moment("MM/DD/YYYY") }}
                        </lead-preview-row>
                        <lead-preview-row v-if="deal" label="Deal Type" :display="true">
                            {{ deal.financeType }}
                        </lead-preview-row>
                        <lead-preview-row v-if="deal" label="Lender" :display="isFinance || isLease">
                            {{ deal.financierName }}
                        </lead-preview-row>
                        <lead-preview-row v-if="deal" :display="isPreApprovalValid" label="Pre-approval ID">
                            {{ preApprovalNumber }}
                        </lead-preview-row>
                        <lead-preview-row v-if="deal" label="Term" :display="isFinance || isLease">
                            {{ deal.termLength }}
                        </lead-preview-row>
                        <lead-preview-row v-if="deal" label="APR" :display="isFinance">
                            {{ deal.interestRate | numeral("0.00") }}%
                        </lead-preview-row>
                        <lead-preview-row v-if="deal" label="Payment" :display="isFinance || isLease">
                            {{ deal.monthlyPayment | numeral("$0,0.00") }}
                        </lead-preview-row>
                        <lead-preview-row
                            v-if="deal"
                            label="Preferred Lender Tier"
                            :display="hasCustomerReadCreditInfo && (isFinance || isLease)"
                        >
                            {{ deal.lenderTier }}
                        </lead-preview-row>
                        <!-- Cash Rows -->
                        <lead-preview-row
                            v-for="(field, index) in cashFields"
                            :key="index"
                            :label="field.label"
                            :display="true"
                        >
                            {{ field.value | numeral("$0,0.00") }}
                        </lead-preview-row>
                        <lead-preview-row label="Lead Message" :display="true">
                            {{ lead.message }}
                        </lead-preview-row>
                    </tbody>
                </template>
            </v-simple-table>
            <div v-else>Problem loading lead information</div>
        </v-card-text>
        <v-card-actions v-if="deal">
            <v-btn text color="primary" :href="callCustomerLink">Call Customer</v-btn>
            <v-btn v-if="showRouteOneButton" text color="primary" :href="routeOneRoute" target="_blank">
                View Loan Approval
            </v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import LeadPreviewRow from "./components/LeadPreviewRow";
import lodashGet from "lodash/get";
import lodashIsNil from "lodash/isNil";

export default {
    name: "LeadInfo",
    components: { LeadPreviewRow },
    props: {
        lead: {
            type: Object,
            required: true,
        },
        loading: {
            type: Boolean,
            required: true,
        },
    },

    computed: {
        deal() {
            return lodashGet(this.lead, "deal");
        },
        dealType() {
            return lodashGet(this.deal, "dealType", null);
        },
        isCash() {
            return this.dealType === "CASH";
        },
        isFinance() {
            return this.dealType === "FINANCE";
        },
        isLease() {
            return this.dealType === "LEASE";
        },
        total() {
            return lodashGet(this.deal, "totalAmountFinance", 0);
        },

        cashFields() {
            let result = [];
            if (!lodashIsNil(this.deal)) {
                result = [
                    {
                        label: "MSRP",
                        value: this.deal.msrp || this.vehicleMSRP,
                    },
                    {
                        label: "Rebates",
                        value: this.deal.rebates,
                    },
                    {
                        label: "Discount",
                        value: this.deal.discount,
                    },
                    {
                        label: "Down Payment",
                        value: this.deal.downPayment,
                    },
                    {
                        label: "Sale Price",
                        value: this.deal.salePrice,
                    },
                    {
                        label: "Registration Fee",
                        value: this.deal.registration,
                    },
                    {
                        label: "Dealer Fee",
                        value: this.deal.dealerFee,
                    },
                    {
                        label: "Total",
                        value: this.total,
                    },
                ];
            }
            return result;
        },

        callCustomerLink() {
            return `tel:${this.lead.customer.phoneNumber}`;
        },

        showRouteOneButton() {
            return !this.loading && !lodashIsNil(this.orderId);
        },
        orderId() {
            return lodashGet(this.deal, "orderId", null);
        },
        routeOneRoute() {
            return `/routeone/${this.orderId}/sso`;
        },
        dealerId() {
            return this.$route.params.dealerId;
        },
        hasCustomerReadCreditInfo() {
            return this.$acl.hasDealerPermission(this.dealerId, "customer:soft-pull:read");
        },
        preApprovalNumber() {
            return lodashGet(this.lead, "deal.preApproval.approvalNumber", false);
        },
        isPreApprovalValid() {
            return lodashGet(this.lead, "deal.preApproval.valid", false);
        },
        vehicleMSRP() {
            return lodashGet(this.lead, "vehicle.msrp", null);
        },
    },
};
</script>

<style scoped></style>
