<template>
    <v-container fluid>
        <v-row>
            <v-col cols="12">
                <deal-preview v-if="!loading" :deal-id="lead.deal.certificateId" :dealer-id="dealerIds" />
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import api from "Util/api";
import DealPreview from "Components/Deal/DealPreview";

export default {
    name: "LeadDetailsHome",
    components: { DealPreview },
    props: {
        leadId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            lead: {},
            loading: true,
        };
    },
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
    },
    watch: {
        dealerIds(value) {
            this.$router.push({
                path: "/leads",
                params: { dealerIds: value },
                query: { dealerIds: value },
            });
        },
    },
    mounted() {
        this.fetchLead(this.leadId);
    },
    methods: {
        fetchLead(leadId) {
            this.loading = true;

            api.get(`/leads/${leadId}`)
                .then((response) => {
                    this.lead = response.data;
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
    },
};
</script>

<style scoped></style>
