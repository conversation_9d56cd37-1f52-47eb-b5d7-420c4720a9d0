<template>
    <v-container fluid>
        <v-banner elevation="5" class="red darken-4 title text-center white--text">
            <template #default>
                <v-icon color="white" size="36" center aria-hidden="true">mdi-alert</v-icon>
                SMS have been disabled for today!
            </template>
        </v-banner>

        <v-banner elevation="5" class="mt-4 grey lighten-2 text-center">
            <template #default>
                If you disabled SMS by mistake or have any questions please contact
                <a :href="`mailto:${supportContact.supportEmail}`" class="font-weight-black">Client Services</a>
            </template>
        </v-banner>

        <v-dialog v-model="successDisabledSMSDialog" max-width="500px">
            <template #default>
                <v-card>
                    <v-card-title dark class="title secondary text-center white--text">
                        Enjoy your Day Off!
                    </v-card-title>
                    <v-card-text class="text--primary mt-2">
                        Notifications are disabled until: <span class="font-weight-black">{{ disabledUntilDate }}</span>
                    </v-card-text>
                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn color="blue darken-1" text @click.stop="successDisabledSMSDialog = false">
                            Close Window
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </template>
        </v-dialog>
    </v-container>
</template>

<script>
export default {
    name: "LeadSMSDisabled",
    props: {
        disabledUntilDate: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            successDisabledSMSDialog: false,
            supportContact: {
                name: "CarSaver Support",
                supportEmail: "<EMAIL>",
                supportPhone: "************",
            },
        };
    },
    mounted() {
        this.successDisabledSMSDialog = true;
    },
};
</script>
