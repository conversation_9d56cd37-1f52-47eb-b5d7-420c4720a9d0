import { make } from "vuex-pathify";
import loader from "@/util/loader";
import searchUtils from "@/util/searchUtils";

const uriRoot = "/users";

const initialState = {
    ...searchUtils.state(),
    searchUri: null,
    initialLoad: true,
    pushHistoryEnabled: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "createdDate,desc",
        page: 1,
    }),
    facets: null,
    filterNames: null,
    filters: searchUtils.parseFiltersFromUrl(),
    searchLoader: {
        loader: loader.defaultState(),
        data: [],
    },
    displayFields: [
        "user",
        "leadType",
        "visitTime",
        "certificate.orderId",
        "vehicle.stockNumber",
        "vehicle.fullName",
        "dealerLink.assignee.lastName",
        "createdDate",
        "actions",
    ],
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0,
    },
    /**
     * pills schema
     * {
                    "name": {
                        label: 'Name',
                        enabled: false,
                    },
                    "warrantyStatuses": {
                        label: 'Warranty',
                        enabled: true
                    },
                    "topDmas": {
                        type: 'range'
                    }
                }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     *     type: the type of filter i.e. 'range' filter
     * }
     */
    pills: {
        dealerIds: {
            enabled: false,
        },
        name: {
            enabled: false,
        },
        emailOrPhone: {
            enabled: false,
        },
        city: {
            enabled: false,
        },
        createdDate: {
            enabled: false,
        },
        userDmaCodes: {
            label: "DMAs",
            facet: "userDmas",
        },
        topDmas: {
            type: "range",
        },
    },
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations(),
};

const actions = {
    ...make.actions(initialState),
    ...searchUtils.actions(uriRoot, "Lead Search"),
    setSearchUri({ commit, state }, searchUri) {
        commit("SET_SEARCH_URI", searchUri);
    },
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters(),
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
