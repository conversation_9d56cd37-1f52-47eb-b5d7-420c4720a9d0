<template>
    <v-card>
        <v-card-title>Form</v-card-title>
        <v-card-text>
            <v-form>
                <v-container>
                    <v-row align="start" dense>
                        <v-col cols="8">
                            <h3>Action</h3>
                            <v-radio-group v-model="importAction" row>
                                <v-radio label="Upsert" value="Upsert"></v-radio>
                                <v-radio label="Shared Upsert" value="Shared_Upsert"></v-radio>
                                <v-radio label="Delete" value="Delete"></v-radio>
                            </v-radio-group>
                        </v-col>
                        <v-col cols="4" class="text-right">
                            <v-btn
                                depressed
                                color="primary"
                                :loading="isSubmitLoading"
                                :disabled="isSubmitLoading"
                                @click="submit"
                            >
                                Submit
                                <template #loader>
                                    <span class="custom-loader">
                                        <v-icon light small>mdi-cached</v-icon>
                                    </span>
                                </template>
                            </v-btn>
                        </v-col>
                    </v-row>
                    <v-row dense>
                        <v-col cols="12">
                            <ImportVehicles />
                        </v-col>
                    </v-row>
                </v-container>
            </v-form>
        </v-card-text>
    </v-card>
</template>

<script>
import { sync, get, call } from "vuex-pathify";
import ImportVehicles from "@/modules/QaTools/components/inventory/ImportVehicles";
import api from "Util/api";
export default {
    name: "RequestForm",
    components: { ImportVehicles },
    data() {
        return {
            isSubmitLoading: false,
        };
    },
    computed: {
        importAction: sync("qaToolsStore/inventoryImportRequest@importAction"),
        vehicles: get("qaToolsStore/inventoryImportRequest@vehicles"),
    },
    methods: {
        addImportJob: call("qaToolsStore/addImportJob"),
        submit() {
            this.isSubmitLoading = true;
            const data = {
                action: this.importAction,
                vehicles: this.vehicles,
            };
            api.post(`/qaTools/inventory/importJob/homenet`, data)
                .then((response) => {
                    this.addImportJob(response.data);
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isSubmitLoading = false;
                });
        },
    },
};
</script>

<style scoped>
.custom-loader {
    animation: loader 1s infinite;
    display: flex;
}
@-moz-keyframes loader {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
@-webkit-keyframes loader {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
@-o-keyframes loader {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
@keyframes loader {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
