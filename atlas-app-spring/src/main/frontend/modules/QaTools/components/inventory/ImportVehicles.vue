<template>
    <v-data-table
        :disable-pagination="true"
        :hide-default-footer="true"
        :headers="[
            { text: 'Vin', value: 'vehicle.vehicleId.vin' },
            { text: 'Dealer Id', value: 'vehicle.vehicleId.dealerId' },
            { text: 'YMM', value: 'vehicle.year', sortable: false },
            { text: 'Stock Type', value: 'vehicle.vehicleCondition' },
            { text: 'Action', value: 'context.action' },
            { text: 'Remove', value: 'remove', sortable: false },
        ]"
        :items="vehicles"
        :single-expand="singleExpand"
        :expanded.sync="expanded"
        item-key="vehicle.id"
        show-expand
        class="elevation-1"
    >
        <template #top>
            <v-toolbar flat>
                <v-toolbar-title>Vehicles</v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn v-if="vehicles.length > 0" class="float-right" light small @click="addVehicle"
                    >Clone Last Vehicle</v-btn
                >
                <v-spacer></v-spacer>
                <v-switch v-model="singleExpand" label="Single expand" class="mt-2"></v-switch>
            </v-toolbar>
        </template>
        <template #item.vehicle.year="{ item }">
            {{ item.vehicle.year }} {{ item.vehicle.make }} {{ item.vehicle.model }}
        </template>
        <template #item.remove="{ item }">
            <v-icon small @click="removeVehicle(item)"> mdi-delete </v-icon>
        </template>
        <template #expanded-item="{ headers, item }">
            <td v-if="item" :colspan="headers.length"><import-vehicle-row-form :vehicle="item" /></td>
        </template>
    </v-data-table>
</template>

<script>
import { call, sync } from "vuex-pathify";
import ImportVehicleRowForm from "@/modules/QaTools/components/inventory/ImportVehicleRowForm";

export default {
    name: "ImportVehicles",
    components: { ImportVehicleRowForm },
    data() {
        return {
            expanded: [],
            singleExpand: true,
        };
    },
    computed: {
        vehicles: sync("qaToolsStore/inventoryImportRequest@vehicles"),
        importAction: sync("qaToolsStore/inventoryImportRequest@importAction"),
    },
    methods: {
        addVehicle: call("qaToolsStore/addVehicle"),
        removeVehicle(vehicle) {
            const vehicleIndex = this.vehicles.indexOf(vehicle);
            this.vehicles.splice(vehicleIndex, 1);
        },
    },
};
</script>

<style scoped></style>
