<template>
    <v-container fluid>
        <v-row class="fill-height" align-content="start">
            <v-col cols="12">
                <v-text-field v-model="url" label="URL" />
            </v-col>
        </v-row>
        <v-row>
            <v-col>
                <!--eslint-disable-next-line-->
                <pre class="output-preview" v-html="styledPayload"></pre>
            </v-col>
            <v-col>
                <v-form ref="form" method="POST" :action="url">
                    <v-row class="fill-height" align-content="start">
                        <v-col cols="6">
                            <v-text-field v-model="form.firstName" name="firstName" label="first Name" />
                        </v-col>
                        <v-col cols="6">
                            <v-text-field v-model="form.lastName" name="lastName" label="Last Name" />
                        </v-col>
                        <v-col cols="12">
                            <v-text-field v-model="form.email" name="email" label="E-mail" />
                        </v-col>
                        <v-col cols="4">
                            <v-text-field v-model="form.area" name="area" label="area" />
                        </v-col>
                        <v-col cols="4">
                            <v-text-field v-model="form.phonePrefix" name="phonePrefix" label="Phone Pre-fix" />
                        </v-col>
                        <v-col cols="4">
                            <v-text-field v-model="form.phoneSuffix" name="phoneSuffix" label="Phone Suffix" />
                        </v-col>
                        <v-col cols="12">
                            <v-text-field v-model="form.zipcode" name="zipcode" label="ZipCode" />
                        </v-col>
                        <v-col>
                            <v-btn type="submit" color="primary">Submit form</v-btn>
                        </v-col>
                    </v-row>
                </v-form>
                <hr class="divider" />
                <v-form ref="jsonform" method="POST" :action="url">
                    <v-textarea v-model="prettyPayload" class="jsonpayload-input" name="payload" label="JSON payload" />
                    <v-btn type="submit" color="primary">Submit JSON</v-btn>
                </v-form>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
export default {
    name: "QaToolCunexus",
    data() {
        return {
            isLoading: true,
            //url: "http://localhost-nissan-upgrade:4000?noPinProspect=true",
            url: "https://cunexus-upgrade.beta.carsaver.com?noPinProspect=true",
            form: {
                memberId: "93080530240",
                firstName: "First Name",
                lastName: "Last Name",
                email: "<EMAIL>",
                area: "813",
                phonePrefix: "123",
                phoneSuffix: "4567",
                zipcode: "33545",
                loanTerms: {
                    newTerms: {
                        term48: {
                            apr: 2.49,
                            paymentMultiplier: 21.91,
                            creditLimit: 60000,
                        },
                        term60: {
                            apr: 1.99,
                            paymentMultiplier: 17.52,
                            creditLimit: 60000,
                        },
                        term72: {
                            apr: 3.25,
                            paymentMultiplier: 15.31,
                            creditLimit: 60000,
                        },
                        term84: {
                            apr: 3.79,
                            paymentMultiplier: 13.57,
                            creditLimit: 60000,
                        },
                        offerId: "123456",
                        productId: "123456",
                        downPayment: 0,
                        defaultTerm: "term60",
                        rateType: "preapproved",
                    },
                },
            },
        };
    },
    computed: {
        prettyPayload: {
            set(val) {
                this.form = JSON.parse(val);
            },
            get() {
                return JSON.stringify(this.form, undefined, 4);
            },
        },
        stringifyPayload: {
            set(val) {
                this.form = JSON.parse(val);
            },
            get() {
                return JSON.stringify(this.form);
            },
        },
        styledPayload() {
            return this.$options.filters.jsonPrettier(this.prettyPayload);
        },
    },
};
</script>

<style lang="scss" scoped>
pre {
    outline: 1px solid #ccc;
    padding: 5px;
    margin: 5px;
}
.output-preview {
    width: 100%;
}
.divider {
    margin: 40px 0 60px 0;
}
</style>
<style lang="scss">
.jsonpayload-input {
    textarea {
        height: 240px;
    }
}
</style>
