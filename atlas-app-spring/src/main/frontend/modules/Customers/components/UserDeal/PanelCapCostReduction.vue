<template>
    <v-expansion-panel :key="7" class="mb-2">
        <v-expansion-panel-header class="grey-bg-color">
            <div class="d-flex justify-space-between">
                <span class="font-weight-bold">Cap Cost Reduction</span>
                <span v-if="isAdvanceEditOnly" class="font-weight-bold">
                    {{ capCostReductionEditingTotal | numeral("$0,0.00") }}
                </span>
                <span v-else class="font-weight-bold">
                    {{ capCostReductionTotal | numeral("$0,0.00") }}
                </span>
            </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content class="pt-2 fw-400 fs-14">
            <v-form v-model="localValidCapCostReduction">
                <div class="my-3">
                    <div v-if="isAdvanceEditOnly">
                        <div v-for="(lineItem, index) in localForm.capCostReduction.lineItems" :key="index" class="row">
                            <div class="col-md-6">{{ lineItem.name }}</div>
                            <div class="col-6 text-end">
                                <span>{{ lineItem.amount | numeral("$0,0.00") }}</span>

                                <v-btn
                                    class="ml-2"
                                    x-small
                                    text
                                    icon
                                    color="red"
                                    @click="removeLineItem('capCostReduction', lineItem)"
                                >
                                    <v-icon>mdi-close-circle-outline</v-icon>
                                </v-btn>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div v-for="(lineItem, index) in capCostReduction.lineItems" :key="index" class="row">
                            <div class="col-md-6">{{ lineItem.name }}</div>
                            <div class="col-6 text-end">
                                {{ lineItem.amount | numeral("$0,0.00") }}
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="localNewLineItems.capCostReduction.show" class="row">
                    <div class="col-md-6">
                        <v-text-field
                            v-model="localNewLineItems.capCostReduction.name"
                            placeholder="Label"
                            outlined
                            dense
                            hide-details
                            :rules="[required]"
                        ></v-text-field>
                    </div>
                    <div class="col-md-6 text-end">
                        <v-text-field
                            v-model="localNewLineItems.capCostReduction.amount"
                            class="align-text-end"
                            placeholder="Amount"
                            prefix="$"
                            outlined
                            dense
                            hide-details
                            :rules="[numericOnly, required]"
                        ></v-text-field>
                    </div>
                </div>
            </v-form>

            <div v-if="isAdvanceEditOnly" class="d-flex justify-end mt-3">
                <v-btn
                    v-if="localNewLineItems.capCostReduction.show"
                    text
                    :disabled="!localValidCapCostReduction"
                    @click="saveLineItem('capCostReduction')"
                    >Save</v-btn
                >
                <v-btn v-else text @click="addHandler('capCostReduction')">+ ADD</v-btn>
            </div>
        </v-expansion-panel-content>
    </v-expansion-panel>
</template>
<script>
import loadashGet from "lodash/get";
import * as _ from "lodash";
import lodashToNumber from "lodash/toNumber";
export default {
    name: "PanelCapCostReduction",
    props: {
        isAdvanceEditOnly: {
            type: Boolean,
            required: true,
        },
        form: {
            type: Object,
            required: true,
        },
        dealDetail: {
            type: Object,
            required: true,
        },
        validCapCostReduction: {
            type: Boolean,
            required: true,
        },
        newLineItems: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            localForm: {
                capCostReduction: {
                    capCostReduction: 0.0,
                    lineItems: [],
                },
            },
            localValidCapCostReduction: null,
            numericOnly: (v) => !isNaN(v) || "Input is not a number",
            required: (v) => !!v || "Input is required",
            localNewLineItems: {
                capCostReduction: {
                    name: "",
                    amount: 0,
                    show: false,
                },
            },
        };
    },
    computed: {
        capCostReductionEditingTotal() {
            const lineItems = this.localForm.capCostReduction.lineItems;
            let lineItemsTotal = 0;

            _.forEach(lineItems, (lineItem) => {
                lineItemsTotal += lodashToNumber(lineItem.amount);
            });

            lineItemsTotal += lodashToNumber(this.localForm.capCostReduction.capCostReduction);

            return lineItemsTotal;
        },
        capCostReductionTotal() {
            const capCostReductionTotal = loadashGet(this.dealDetail, "capCostReductionTotal", null);

            return capCostReductionTotal;
        },
        capCostReduction() {
            const capCostReduction = loadashGet(this.dealDetail, "capCostReduction", null);

            return capCostReduction;
        },
    },
    watch: {
        form(newValue) {
            this.localForm = newValue;
        },
        validCapCostReduction(newValue) {
            this.localValidCapCostReduction = newValue;
        },
    },
    methods: {
        addHandler(field) {
            this.localNewLineItems[field].show = true;
        },
        removeLineItem(field, lineItem) {
            const index = this.localForm[field].lineItems.findIndex((item) => item.name === lineItem.name);
            this.localForm[field].lineItems.splice(index, 1);
        },
        saveLineItem(field) {
            const formField = this.localForm[field];
            if (!formField) {
                console.error("form field is not defined", field);
            } else {
                formField.lineItems.push(this.localNewLineItems[field]);
                this.resetLineItem(field);
            }
        },
        resetLineItem(field) {
            this.localNewLineItems[field] = {
                name: "",
                amount: 0,
                show: false,
            };
        },
    },
};
</script>
