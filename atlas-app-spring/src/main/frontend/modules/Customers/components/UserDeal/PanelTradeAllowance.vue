<template>
    <v-expansion-panel :key="4" class="mb-2">
        <v-expansion-panel-header class="grey-bg-color">
            <div class="d-flex justify-space-between">
                <span class="font-weight-bold">Trade Allowance</span>
                <span v-if="isAdvanceEditOnly" class="font-weight-bold">
                    {{ tradeAllowanceEditingTotal | numeral("$0,0.00") }}
                </span>
                <span v-else class="font-weight-bold">
                    {{ tradeAllowanceTotal | numeral("$0,0.00") }}
                </span>
            </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content class="pt-4 fw-400 fs-14">
            <div class="d-flex flex-column flex-sm-row justify-space-between">
                <div class="d-flex flex-column">
                    <div class="trade-allowance-block mb-4">Model: {{ tradeAllowance.model }}</div>
                    <div class="trade-allowance-block mb-4">Trim: {{ tradeAllowance.trim }}</div>
                    <div class="trade-allowance-block">Appraisal Type: {{ tradeAllowance.appraisalType }}</div>
                </div>

                <div class="d-flex flex-column align-end">
                    <v-form v-model="localValidAllowance">
                        <div class="d-flex justify-space-between mb-4">
                            <span class="mr-3">Allowance</span>
                            <div v-if="isAdvanceEditOnly" class="trade-allowance-input-wrapper">
                                <v-text-field
                                    v-model="localForm.tradeAllowance.allowance"
                                    prefix="$"
                                    class="align-text-end"
                                    placeholder="Trade Allowance"
                                    outlined
                                    dense
                                    hide-details
                                    :rules="[numericOnly, required]"
                                ></v-text-field>
                            </div>

                            <span v-else class="trade-allowance-input-wrapper text-end">
                                {{ tradeAllowance.allowance | numeral("$0,0.00") }}
                            </span>
                        </div>
                    </v-form>
                    <v-form v-model="localValidPayOff">
                        <div class="d-flex justify-space-between mb-4">
                            <span class="mr-3">Payoff</span>
                            <div v-if="isAdvanceEditOnly" class="trade-allowance-input-wrapper">
                                <v-text-field
                                    v-model="localForm.tradeAllowance.payoff"
                                    prefix="$"
                                    class="align-text-end"
                                    placeholder="Trade Payoff"
                                    outlined
                                    dense
                                    hide-details
                                    :rules="[numericOnly, required]"
                                ></v-text-field>
                            </div>

                            <span v-else class="trade-allowance-input-wrapper text-end">
                                {{ tradeAllowance.payoff | numeral("$0,0.00") }}
                            </span>
                        </div>
                    </v-form>
                    <div class="d-flex justify-space-between mb-4">
                        <span class="mr-3">Net Trade</span>
                        <span v-if="isAdvanceEditOnly" class="trade-allowance-input-wrapper text-end">
                            {{ tradeAllowanceEditingTotal | numeral("$0,0.00") }}
                        </span>
                        <span v-else class="trade-allowance-input-wrapper text-end">
                            {{ tradeAllowance.netTrade | numeral("$0,0.00") }}
                        </span>
                    </div>
                </div>
            </div>
        </v-expansion-panel-content>
    </v-expansion-panel>
</template>
<script>
import loadashGet from "lodash/get";
import * as _ from "lodash";
import lodashToNumber from "lodash/toNumber";

export default {
    name: "PanelTradeAllowance",
    props: {
        isAdvanceEditOnly: {
            type: Boolean,
            required: true,
        },
        form: {
            type: Object,
            required: true,
        },
        dealDetail: {
            type: Object,
            required: true,
        },
        validAllowance: {
            type: Boolean,
            required: true,
        },
        validPayOff: {
            type: Boolean,
            required: true,
        },
    },
    data() {
        return {
            localForm: {
                tradeAllowance: {
                    model: "",
                    trim: "",
                    appraisalType: "",
                    allowance: "",
                    payoff: 0.0,
                    netTrade: 0.0,
                },
            },
            localValidAllowance: null,
            localValidPayOff: null,
            numericOnly: (v) => !isNaN(v) || "Input is not a number",
            required: (v) => !!v || "Input is required",
        };
    },
    computed: {
        tradeAllowance() {
            const tradeAllowance = loadashGet(this.dealDetail, "tradeAllowance", null);

            return tradeAllowance;
        },
        tradeAllowanceEditingTotal() {
            const allowance = lodashToNumber(this.localForm.tradeAllowance.allowance);
            const payoff = lodashToNumber(this.localForm.tradeAllowance.payoff);
            let total = 0;

            total += Math.abs(allowance) - Math.abs(payoff);
            return total;
        },
        tradeAllowanceTotal() {
            const tradeAllowanceTotal = loadashGet(this.dealDetail, "tradeAllowanceTotal", 0);

            return tradeAllowanceTotal;
        },
    },
    watch: {
        form(newValue) {
            this.localForm = newValue;
        },
        validAllowance(newValue) {
            this.localValidAllowance = newValue;
        },
        validPayOff(newValue) {
            this.localValidPayOff = newValue;
        },
    },
};
</script>
