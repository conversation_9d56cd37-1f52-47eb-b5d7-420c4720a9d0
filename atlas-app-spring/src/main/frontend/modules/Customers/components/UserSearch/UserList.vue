<template>
    <div>
        <v-card>
            <v-toolbar flat>
                <v-toolbar-title class="grey--text">Customers</v-toolbar-title>

                <v-spacer />

                <table-column-selector id="table-column-config" v-model="displayFields" :fields="headers" />
                <export-column-selector
                    id="table-column-export"
                    v-model="exportFields"
                    :display-fields="displayFields"
                    :exclude-columns="['vehicleOfInterest']"
                    :fields="headers"
                    @doExport="prepareExport"
                />
            </v-toolbar>

            <v-divider />

            <filter-chips :store="store" />

            <table-search-count-label :page-number="pageNumber" :page-size="pageSize" :total-elements="totalElements" />

            <v-divider />

            <v-data-table
                :headers="fieldsForDisplay"
                :items="searchResults"
                :loading="isLoading"
                :server-items-length="totalElements"
                :sort-by="sortBy"
                :sort-desc="sortDesc"
                hide-default-footer
                :mobile-breakpoint="0"
                @update:options="updateOptions"
            >
                <template v-for="header in headers" #[`header.${header.value}`]>
                    {{ header.text }}
                    <info-tooltip v-if="header.tooltip" :key="header.value" size="16">
                        {{ header.tooltip }}
                    </info-tooltip>
                </template>

                <template #item.actions="{ item }">
                    <send-to-crm-enhanced-dialog
                        v-if="
                            isSendToCRMEnhancedDialogEnabled && (!isProspectOnly(item) || isCRMLeadsForProspectEnabled)
                        "
                        :user-id="selectedUser?.id"
                        :dealer-id="dealerId"
                        :selected-dealer-id="selectedDealerId"
                        :name="fullName"
                        :dealership="selectedDealerName"
                        :from="isProspectOnly(item) ? 'prospects' : 'customers'"
                    >
                        <template #activator>
                            <v-btn
                                x-small
                                outlined
                                color="primary"
                                :disabled="disableSendToCRMProspects(item)"
                                @click="setSelectedUser(item)"
                            >
                                Send to CRM
                            </v-btn>
                        </template>
                    </send-to-crm-enhanced-dialog>
                    <v-btn
                        v-else-if="!isProspectOnly(item) || isCRMLeadsForProspectEnabled"
                        :disabled="disableSendToCRMProspects(item)"
                        color="primary"
                        outlined
                        x-small
                        @click="openCrmModal(item)"
                    >
                        Send to CRM
                    </v-btn>
                </template>
                <template #item.program.name="{ item }">
                    <v-chip v-if="item.program" color="primary" dark small>{{ item.program.name }}</v-chip>
                </template>
                <template #item.firstName="{ item }">
                    <router-link :to="stageRoute(item)">
                        {{ item.firstName }}
                    </router-link>
                </template>
                <template #item.lastName="{ item }">
                    <router-link :to="stageRoute(item)">
                        {{ item.lastName }}
                    </router-link>
                </template>
                <template #item.fullName="{ item }">
                    <router-link :to="stageRoute(item)"> {{ item.firstName }} {{ item.lastName }}</router-link>
                </template>
                <template #item.stage="{ item }">
                    <user-ranked-stage-tool-tip
                        v-if="item.stage"
                        :rank="getStageRank(item)"
                        :stage="
                            getSelectedProgramStage(
                                item.stage,
                                selectedDealerId,
                                item.programSubscriptionStages,
                                item.program
                            )
                        "
                    >
                    </user-ranked-stage-tool-tip>
                </template>
                <template #item.phoneNumber="{ item }">
                    <a :href="`tel:${item.phoneNumber}`">
                        {{ item.phoneNumber | phoneFormatter }}
                    </a>
                </template>
                <template #item.email="{ item }">
                    <a :href="`mailto:${item.email}`">{{ item.email }}</a>
                </template>
                <template #item.smsEnabled="{ item }">
                    <boolean-indicator :value="item.smsEnabled" />
                </template>
                <template #item.address="{ item }">
                    <span v-if="item.address">
                        {{ item.address.street }} {{ item.address.city }}, {{ item.address.stateCode }}
                        {{ item.address.zipCode }}
                    </span>
                </template>
                <template #item.vehicleOfInterest="{ item }">
                    <span v-if="item.vehicleOfInterest">
                        {{ item.vehicleOfInterest.stockType }}
                        {{ item.vehicleOfInterest.year }}
                        {{ item.vehicleOfInterest.make }}
                        {{ item.vehicleOfInterest.model }}
                    </span>
                </template>
                <template #item.vehicleOfInterest.inTransit="{ item }">
                    <boolean-indicator :value="item.vehicleInTransit" />
                </template>
                <template #item.traits.dealerUserTraitsList.activeVehiclesInGarageCount="{ item }">
                    {{ getDealerTraitProperty(item, "activeVehiclesInGarageCount") }}
                </template>
                <template #item.traits.dealerUserTraitsList.vehiclesInGarageCount="{ item }">
                    {{ getDealerTraitProperty(item, "vehiclesInGarageCount") }}
                </template>
                <template #item.traits.dealerUserTraitsList.new="{ item }">
                    {{ getDealerTraitProperty(item, "stockTypeNewVehiclesInGarageCount") }}
                </template>
                <template #item.traits.dealerUserTraitsList.used="{ item }">
                    {{ getDealerTraitProperty(item, "stockTypeUsedVehiclesInGarageCount") }}
                </template>
                <template #item.createdDate="{ item }">
                    {{ item.createdDate | formatEpochDate }}
                </template>
                <template #item.lastLoginAt="{ item }">
                    {{ item.lastLoginAt | formatEpochDate }}
                </template>
                <template #item.tradePayment="{ item }">
                    <div v-if="isDisplayNonZeroValue(item.tradePayment)">
                        {{ item.tradePayment | currency("$", ",") }}
                    </div>
                </template>
                <template #item.tradeEquity="{ item }">
                    <div v-if="item.tradeValue && item.tradePaymentType === 'PAID_OFF'">
                        {{ item.tradeValue | currency("$", ",") }}
                    </div>
                    <div v-else-if="isDisplayNonZeroValue(item.tradeEquity)">
                        {{ item.tradeEquity | currency("$", ",") }}
                    </div>
                </template>
                <template #item.tradeValue="{ item }">
                    <div v-if="isDisplayNonZeroValue(item.tradeValue)">
                        {{ item.tradeValue | currency("$", ",") }}
                    </div>
                </template>
                <template #item.maturityDate="{ item }">
                    {{ item.maturityDate | dateMDY(item.maturityDate) }}
                </template>
                <template #item.tradePaymentType="{ item }">
                    {{ formatTradeType(item.tradePaymentType) }}
                </template>
                <template #item.programSubscriptionStages.lastModifiedDate="{ item }">
                    {{ getSelectedProgramLastModifiedDate(item) | formatEpochDate }}
                </template>
                <template #item.customerStatus="{ item }">
                    {{ getSelectedProgramCustomerStatus(item) }}
                </template>
                <template #item.userLeads="{ item }">
                    {{ getUserLeads(item.userLeads) }}
                </template>
            </v-data-table>

            <v-container class="pt-2">
                <v-row>
                    <v-col cols="12">
                        <v-pagination :length="totalPages" :total-visible="10" :value="page" @input="pageChanged" />
                    </v-col>
                </v-row>
            </v-container>

            <slot name="footer" />
        </v-card>
        <v-dialog v-if="selectedUser" v-model="dialog" persistent width="400">
            <v-card>
                <v-card-title> Send Customer to CRM</v-card-title>
                <v-card-text>
                    Are you sure you want to send
                    <strong class="text-capitalize">
                        {{ selectedUser.firstName }}
                        {{ selectedUser.lastName }}
                    </strong>
                    to your CRM?
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn color="error" text @click="dialog = false"> Cancel</v-btn>
                    <v-btn color="success" text @click="sendToCrm(selectedUser)"> Send</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { call, get, sync } from "vuex-pathify";
import TableColumnSelector from "Components/TableColumnSelector";
import ExportColumnSelector from "Components/ExportColumnSelector";
import TableSearchCountLabel from "Components/TableSearchCountLabel";
import tableUtils from "@/util/tableUtils";
import BooleanIndicator from "Components/BooleanIndicator";
import FilterChips from "Components/Search/FilterChips";
import UserRankedStageToolTip from "./UserRankedStageToolTip";
import InfoTooltip from "Components/InfoTooltip";
import SendToCrmEnhancedDialog from "Modules/Customers/components/UserDetails/SendToCrmEnhancedDialog.vue";

import api from "Util/api";
import isNil from "lodash/isNil";
import lodashIsNil from "lodash/isNil";
import map from "lodash/map";
import find from "lodash/find";
import toString from "lodash/toString";
import forEach from "lodash/forEach";
import capitalize from "lodash/capitalize";
import lodashGet from "lodash/get";
import moment from "moment/moment";
import { getSelectedProgramStage } from "Util/renameUtils";
import { convertToTitleCase } from "Util/searchUtils";

export default {
    name: "UserList",
    filters: {
        dateMDY(val) {
            return val === null ? "" : moment(val).format("MM/DD/YYYY");
        },
    },
    components: {
        SendToCrmEnhancedDialog,
        FilterChips,
        BooleanIndicator,
        TableColumnSelector,
        TableSearchCountLabel,
        UserRankedStageToolTip,
        ExportColumnSelector,
        InfoTooltip,
    },
    props: {
        store: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            dialog: false,
            selectedUser: null,
            headers: [
                { text: "Program", value: "program.name", sortable: true },
                {
                    text: "First Name",
                    align: "start",
                    value: "firstName",
                    width: 125,
                },
                { text: "Last Name", value: "lastName", align: "start", width: 125 },
                {
                    text: "Full Name",
                    value: "fullName",
                    align: "start",
                    sortable: true,
                    width: 200,
                    exportable: false,
                },
                {
                    text: "Stage",
                    value: "stage",
                    align: "start",
                    sortable: false,
                    width: 120,
                },
                {
                    text: "Originating Salesperson",
                    value: "clientAdvisor",
                    align: "start",
                    sortable: true,
                    width: 160,
                },
                { text: "Phone Number", value: "phoneNumber", sortable: true, width: 130 },
                { text: "Email", value: "email", sortable: true, width: 250 },
                { text: "Sms", value: "smsEnabled", width: 100 },
                {
                    text: "Vehicle Of Interest (VOI)",
                    value: "vehicleOfInterest",
                    sortable: true,
                    width: 150,
                    exportable: false,
                },
                { text: "VOI StockType", value: "vehicleOfInterest.stockType", sortable: false, width: 125 },
                { text: "VOI In Transit", value: "vehicleOfInterest.inTransit", sortable: true, width: 80 },
                { text: "VOI Certified", value: "vehicleOfInterest.certified", sortable: false, width: 105 },
                { text: "VOI Year", value: "vehicleOfInterest.year", sortable: true, width: 80 },
                { text: "VOI Make", value: "vehicleOfInterest.make", sortable: true, width: 125 },
                { text: "VOI Model", value: "vehicleOfInterest.model", sortable: true, width: 125 },
                { text: "VOI Stock Number", value: "vehicleOfInterest.stockNumber", sortable: false, width: 125 },
                { text: "Time Zone", value: "timeZone", width: 125 },
                { text: "Address", value: "address", sortable: true, width: 200 },
                { text: "Street", value: "address.street", sortable: false, width: 150 },
                { text: "City", value: "address.city", sortable: true, width: 125 },
                { text: "State", value: "address.stateCode", width: 100 },
                { text: "Zip", value: "address.zipCode", sortable: true, width: 70 },
                { text: "DMA Name", value: "address.dma.name", sortable: true, width: 150 },
                { text: "DMA Rank", value: "address.dma.rank", width: 110 },
                { text: "DMA Code", value: "address.dma.code", width: 125 },
                { text: "Pre-approvals", value: "preApprovalCount", width: 125 },
                { text: "Pre-quals", value: "traits.preQualificationsCount", width: 110 },
                { text: "Trade-ins", value: "traits.tradeInsCount", width: 110 },
                {
                    text: "Active Vehicles in Garage",
                    value: "traits.activeVehiclesInGarageCount",
                    width: 125,
                },
                { text: "Saved Vehicles", value: "traits.garageSavedVehicleCount", sortable: true, width: 125 },
                {
                    text: "Recently Viewed Vehicles",
                    value: "traits.garageViewedVehicleCount",
                    sortable: true,
                    width: 125,
                },
                {
                    text: "Garage Vehicles",
                    value: "traits.vehiclesInGarageCount",
                    sortable: true,
                    width: 145,
                },
                {
                    text: "Garage: New",
                    value: "traits.dealerUserTraitsList.new",
                    sortable: false,
                    width: 145,
                },
                {
                    text: "Garage: Used",
                    value: "traits.dealerUserTraitsList.used",
                    sortable: false,
                    width: 145,
                },
                { text: "Leads", value: "traits.leadsCount", width: 110 },
                { text: "Lead Type", value: "userLeads", width: 180 },
                { text: "Applications", value: "traits.financeAppsSubmittedCount", width: 125 },
                { text: "Sales", value: "traits.salesCount", width: 110 },
                { text: "Sign Up Date", value: "createdDate", sortable: true, width: 125 },
                { text: "Last Login", value: "lastLoginAt", sortable: true, width: 125 },
                { text: "Actions", value: "actions", sortable: false, width: 125, exportable: false },
                {
                    sortable: true,
                    text: "Visits w/ PIN",
                    value: "websiteVisitsBeforeSignup",
                    width: 150,
                    tooltip: "Amount of times a user has used their PIN prior to creating an account",
                },
                {
                    sortable: true,
                    text: "Stage Transition",
                    value: "programSubscriptionStages.lastModifiedDate",
                    width: 150,
                },
                {
                    sortable: true,
                    text: "Customer Status",
                    value: "customerStatus",
                    width: 150,
                },
                {
                    // Only for program user
                    sortable: false,
                    text: "Assigned Dealer",
                    value: "assignedDealer",
                    width: 150,
                },
                { text: "Language", value: "locale", sortable: true, width: 150 },
            ],
        };
    },

    computed: {
        apiSearchResults: get("userSearch/searchLoader@data"),
        pageNumber: get("userSearch/pageMetadata@number"),
        pageSize: get("userSearch/pageMetadata@size"),
        totalPages: get("userSearch/pageMetadata@totalPages"),
        totalElements: get("userSearch/pageMetadata@totalElements"),
        isLoading: get("userSearch/<EMAIL>"),
        page: sync("userSearch/pageable@page"),
        sort: sync("userSearch/pageable@sort"),
        displayFields: sync("userSearch/displayFields"),
        exportFields: sync("userSearch/exportFields"),
        exportLabels: sync("userSearch/exportLabels"),
        sortBy: get("userSearch/getSortBy"),
        sortDesc: get("userSearch/getSortDesc"),
        selectedDealer: get("loggedInUser/selectedDealer"),
        filterPrograms: sync("userSearch/filters@programs"),
        filterDealerIds: sync("userSearch/filters@dealerIds"),
        filterStageTileForExport: sync("userSearch/filters@stageTile"),
        stageTitle: get("userSearch/stageTitle"),
        featureFlags: get("loggedInUser/featureFlags"),
        isBMWProgramUser: get("loggedInUser/isBMWProgramUser"),
        customerSortFieldsEnabled: get("loggedInUser/featureFlags@ATLAS_CUSTOMER_SORT_FIELDS_ENABLED"),
        customerNewFieldsEnabled: get("loggedInUser/featureFlags@ATLAS_CUSTOMER_BMW_NEW_COLUMNS"),
        enableAtlasTradePurchaseFields: get("loggedInUser/featureFlags@ENABLE_ATLAS_TRADE_PURCHASE_FIELDS"),
        selectedDealerName: get("loggedInUser/selectedDealer@name"),
        selectedDealerId() {
            return lodashGet(this.selectedDealer, "id", null);
        },
        dealerId() {
            return this.dealerIds;
        },
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        programIds() {
            return this.$route.query.programIds === "" ? null : this.$route.query.programIds;
        },
        fieldsForDisplay() {
            return tableUtils.intersectFieldsForDisplay(this.headers, this.displayFields);
        },
        fullName() {
            return `${this.selectedUser?.firstName} ${this.selectedUser?.lastName}`;
        },
        searchResults() {
            return map(this.apiSearchResults, (searchResult) => {
                const item = { ...searchResult };
                item.vehicleInTransit = lodashGet(item, "vehicleOfInterest.inTransit", false);
                return item;
            });
        },
        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
        isCRMLeadsForProspectEnabled() {
            return lodashGet(this.featureFlags, "CRM_LEADS_FOR_PROSPECTS", false) || false;
        },
        isSendToCRMEnhancedDialogEnabled() {
            const result = lodashGet(this.featureFlags, "SEND_TO_CRM_ENHANCEMENT", false) || false;
            return result;
        },
        isAtlasStickyFiltersFeatureEnabled() {
            const result = lodashGet(this.featureFlags, "ATLAS_STICKY_FILTERS_ENABLED", false) || false;
            return result;
        },
    },
    watch: {
        dealerIds(_val) {
            this.filterDealerIds = _val;
            this.doPageLoad();
            this.loadFilterInfo();
        },
        programIds(_val) {
            this.filterPrograms = _val;
            this.doPageLoad();
            this.loadFilterInfo();
        },
    },
    created() {
        if (this.customerNewFieldsEnabled) {
            this.replaceHeaderWithNewColumnStructure();
        }

        if (!this.enableAtlasTradePurchaseFields) {
            this.removeDynamicFieldsFromList();
        }

        if (this.isProgramUser === true) {
            this.filterPrograms = this.programIds;
        }

        this.updateSortFieldsState(this.customerSortFieldsEnabled);

        this.doPageLoad();
        this.loadFilterInfo();
    },
    mounted() {
        this.filteredHeaderValues();
        this.filterOutFieldsBasedOnFeatureFlag();
    },
    methods: {
        isDisplayNonZeroValue(amount) {
            const result = !lodashIsNil(amount) && amount !== 0;
            return result;
        },
        formatTradeType(str) {
            let result = "";
            if (typeof str === "string") {
                // Replace all dashes with spaces
                const modifiedStr = str.replace(/_/g, " ");
                // Split the string into words
                const words = modifiedStr.split(" ");
                // Capitalize the first letter of each word and join them back into a string
                const capitalizedWords = words.map(
                    (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                );
                result = capitalizedWords.join(" ");
                if (result === "Lease Turn In") {
                    result = "Lease Turn-In";
                }
            }
            return result;
        },
        doPageLoad: call("userSearch/doPageLoad"),
        loadFilterInfo: call("userSearch/loadFilterInfo"),
        doSort: call("userSearch/doSort"),
        changePage: call("userSearch/changePage"),
        updateSort: call("userSearch/updateSort"),
        doExport: call("userSearch/doExport"),
        doExportReduced: call("userSearch/doExportReduced"),
        fetchStageRecords: call("userSearch/fetchStageRecords"),
        checkBMWUser(programId) {
            return this.isBMWProgramUser(programId);
        },
        pageChanged(newPage) {
            if (this.stageTitle != null) {
                this.$store.commit("userSearch/SET_PAGE", newPage);
                this.fetchStageRecords();
            } else {
                this.changePage(newPage);
            }
        },
        stageRoute(item) {
            const dealerIdsParam =
                "dealerIds=" +
                (isNil(this.dealerIds) ? (isNil(this.selectedDealerId) ? "" : this.selectedDealerId) : this.dealerIds);

            let programIdsParam = "";
            if (this.isProgramUser) {
                programIdsParam = isNil(this.programIds) ? "" : this.programIds;
                programIdsParam = "&programIds=" + programIdsParam;
            }
            let selectedProgramIdParams = "";
            if (item.program) {
                selectedProgramIdParams = isNil(item.program.id) ? "" : item.program.id;
                selectedProgramIdParams = "&selecteduserProgramId=" + selectedProgramIdParams;
            }
            const page = this.isProspectOnly(item) ? "prospects" : "customers";
            return `/${page}/${item.id}?${dealerIdsParam}${programIdsParam}${selectedProgramIdParams}`;
        },
        isProspectOnly(item) {
            const PROSPECT_RANK = 2;
            const DOC_TYPE = "PROSPECT";
            return item?.rankedStage === PROSPECT_RANK || (item?.docType || "").toUpperCase() === DOC_TYPE;
        },
        prepareExport() {
            this.exportLabels.length = 0;
            for (let field of this.exportFields) {
                let header = find(this.headers, function (o) {
                    return toString(o.value) === toString(field);
                });
                if (header === undefined) {
                    console.error("Error setting label for export, FIELD: ", field);
                    return;
                }
                this.exportLabels = [...this.exportLabels, header.text];
            }

            //To avoid a full refactor of the export process, I am adding the stageTile as filter
            if (!isNil(this.stageTitle)) {
                this.filterStageTileForExport = this.stageTitle;
            }

            this.doExportReduced("customer_report.csv");

            //remove to avoid displaying it as filter pill
            if (!isNil(this.stageTitle)) {
                this.filterStageTileForExport = null;
            }
        },
        updateOptions(options) {
            const newSortBy = lodashGet(options, "sortBy[0]") || "";
            const newSortDesc = lodashGet(options, "sortDesc[0]") || false;

            if (newSortBy === this.sortBy && newSortBy === "") {
                return;
            }

            if (newSortBy !== this.sortBy || newSortDesc !== this.sortDesc) {
                if (newSortBy === "" || newSortDesc === "") {
                    this.updateSort("");
                } else {
                    const direction = newSortDesc ? "desc" : "asc";
                    this.updateSort(`${newSortBy},${direction}`);
                }
            }
        },
        openCrmModal(user) {
            this.selectedUser = user;
            return (this.dialog = !this.dialog);
        },
        sendToCrm(selectedUser) {
            this.dialog = false;
            this.dialog = false;
            if (!this.dealerIds && !this.selectedDealerId) {
                const dealerMessage = "Please select a dealer to send CRM.";
                this.$toast.error(dealerMessage);
            } else {
                const dealerId = !this.dealerIds ? this.selectedDealerId : this.dealerIds;

                if (this.isProspectOnly(selectedUser)) {
                    api.post(`/dealer/${dealerId}/prospects/${selectedUser.id}/send-to-crm`)
                        .then(() => {
                            this.confirmText = `Lead has been delivered to your CRM for Upgrade Prospect ${selectedUser.firstName} ${selectedUser.lastName}`;
                        })
                        .catch(() => {
                            this.confirmText = "Error sending lead to your CRM.";
                        })
                        .finally(() => {
                            this.$toast(this.confirmText);
                        });
                } else {
                    api.post(`/dealer/${dealerId}/users/${selectedUser.id}/send-to-crm`)
                        .then(() => {
                            this.confirmText = `Lead has been delivered to your CRM for customer ${selectedUser.firstName} ${selectedUser.lastName}`;
                        })
                        .catch((error) => {
                            const message = lodashGet(error, "response.data.error", "Error sending lead to your CRM.");
                            this.confirmText = message;
                        })
                        .finally(() => {
                            this.$toast(this.confirmText);
                        });
                }
            }
        },
        getSelectedProgramStage,
        getStageRank(item) {
            // set selected program ranked as global stage rank
            let stageRank = item.rankedStage;

            //use global stage if no dealer is selected
            if (this.selectedDealerId === null) {
                return stageRank;
            }

            const programSubscriptionStages = lodashGet(item, "programSubscriptionStages", []);
            forEach(programSubscriptionStages, (program) => {
                let dealerMatch = this.selectedDealerId === program.dealerId;
                // case for Express users. No program associated as there is no campaign defined on the source
                let noProgramFound = item.program === null;
                let programMatch = item.program?.id === program.programId;

                // if selectedDealer id equals programDealer id && customers program id matches program id override global stage rank
                if (dealerMatch && (noProgramFound || programMatch)) {
                    stageRank = program.rank;
                    return stageRank;
                }
            });

            return stageRank;
        },
        getSelectedProgramLastModifiedDate(item) {
            // set selected program ranked as global lastModifiedDate
            let lastModifiedDate = item.lastModifiedDate;

            // use global lastModifiedDate if no dealer is selected
            if (!this.selectedDealerId) {
                return lastModifiedDate;
            }

            const programSubscriptionStages = lodashGet(item, "programSubscriptionStages", []);
            forEach(programSubscriptionStages, (program) => {
                let dealerMatch = this.selectedDealerId === program.dealerId;

                // case for Express users. No program associated as there is no campaign defined on the source
                let noProgramFound = item.program === null;
                let programMatch = item.program?.id === program.programId;

                // if selectedDealer id equals programDealer id && customers program id matches program id override global lastModifiedDate
                if (dealerMatch && (noProgramFound || programMatch)) {
                    lastModifiedDate = program.lastModifiedDate;
                    return lastModifiedDate;
                }
            });

            return lastModifiedDate;
        },
        getSelectedProgramCustomerStatus(item) {
            const getSelectedDealer = item?.dealerLinks?.find((dealer) => dealer.dealerId === this.selectedDealerId);
            return capitalize(getSelectedDealer?.status || "");
        },
        getDealerTraitProperty(item, propertyName) {
            let traits = lodashGet(item, "traits", undefined);
            if (traits === undefined) return 0;

            let dealerUserTraitsList = lodashGet(traits, "dealerUserTraitsList", undefined);
            if (dealerUserTraitsList === undefined) return 0;

            if (dealerUserTraitsList && dealerUserTraitsList.length) {
                if (this.dealerId) {
                    let dealer = dealerUserTraitsList.find((fil) => fil.dealerId === this.dealerIds);
                    if (dealer) {
                        return lodashGet(dealer, propertyName, 0);
                    }
                    return 0;
                } else {
                    let totalCount = 0;
                    forEach(dealerUserTraitsList, (user) => {
                        let userCount = lodashGet(user, "stockTypeNewVehiclesInGarageCount", 0);
                        totalCount = totalCount + userCount;
                    });
                    return totalCount;
                }
            }
            return 0;
        },
        getNewVehicleInGarage(item) {
            let dealerUserTraitsList = item.traits?.dealerUserTraitsList;
            if (dealerUserTraitsList && dealerUserTraitsList.length) {
                if (this.dealerId) {
                    let isDealer = dealerUserTraitsList.find((fil) => fil.dealerId === this.dealerIds);
                    if (isDealer) {
                        return isDealer.stockTypeNewVehiclesInGarageCount
                            ? isDealer.stockTypeNewVehiclesInGarageCount
                            : 0;
                    }
                    return 0;
                } else {
                    let newCount = 0;
                    forEach(dealerUserTraitsList, (user) => {
                        newCount = newCount + user.stockTypeNewVehiclesInGarageCount;
                    });
                    return newCount;
                }
            }
            return 0;
        },
        setSelectedUser(user) {
            this.selectedUser = user;
        },
        getUsedVehicleInGarage(item) {
            let dealerUserTraitsList = item.traits?.dealerUserTraitsList;
            if (dealerUserTraitsList && dealerUserTraitsList.length) {
                if (this.dealerId) {
                    let isDealer = dealerUserTraitsList.find((fil) => fil.dealerId === this.dealerIds);
                    if (isDealer) {
                        return isDealer.stockTypeUsedVehiclesInGarageCount
                            ? isDealer.stockTypeUsedVehiclesInGarageCount
                            : 0;
                    }
                    return 0;
                } else {
                    let usedCount = 0;
                    forEach(dealerUserTraitsList, (user) => {
                        usedCount = usedCount + user.stockTypeUsedVehiclesInGarageCount;
                    });
                    return usedCount;
                }
            }
            return 0;
        },
        getUserLeads(item) {
            if (!item?.length) return "";
            let filteredLeads = item;
            if (this.dealerId) {
                filteredLeads = item.filter((lead) => lead.dealerId === this.dealerId);
            }
            const leadTypes = filteredLeads.map((lead) => convertToTitleCase(lead.type));
            return [...new Set(leadTypes)].join(", ");
        },
        disableSendToCRMProspects(item) {
            if (this.isProspectOnly(item)) {
                return !(this.isCRMLeadsForProspectEnabled && this.dealerId !== null);
            }
            return false;
        },
        updateSortFieldsState(value) {
            this.headers.find((x) => x.text === "Garage Vehicles").sortable = value;
            this.headers.find((x) => x.text === "Garage: New").sortable = value;
            this.headers.find((x) => x.text === "Garage: Used").sortable = value;
            this.headers.find((x) => x.text === "Stage").sortable = value;
        },
        replaceHeaderWithNewColumnStructure() {
            this.headers = [
                { text: "Program", value: "program.name", sortable: true },
                {
                    text: "First Name",
                    align: "start",
                    value: "firstName",
                    width: 125,
                },
                { text: "Last Name", value: "lastName", align: "start", width: 125 },
                { text: "Full Name", value: "fullName", align: "start", sortable: true, width: 200, exportable: false },
                { text: "Stage", value: "stage", align: "start", sortable: false, width: 120 },
                { text: "Email", value: "email", sortable: true, width: 250 },
                { text: "Originating Salesperson", value: "clientAdvisor", align: "start", sortable: true, width: 125 },
                { text: "Last Login", value: "lastLoginAt", sortable: true, width: 125 },
                { text: "Sign Up Date", value: "createdDate", sortable: true, width: 125 },
                {
                    sortable: true,
                    text: "Website Visits",
                    value: "websiteVisits",
                    width: 150,
                    tooltip: "Number of website visits by customer",
                },
                {
                    text: "Logins",
                    value: "logins",
                    sortable: true,
                    width: 125,
                    tooltip: "Number of site logins by prospect",
                },
                {
                    sortable: true,
                    text: "PIN Visits",
                    value: "websiteVisitsBeforeSignup",
                    width: 150,
                    tooltip: "Customer arrived at website by clicking on a URL that included a PIN",
                },
                { text: "Trade-ins", value: "traits.tradeInsCount", width: 110, sortable: true },
                { text: "Trade Year", value: "tradeYear", width: 120, sortable: true },
                { text: "Trade Make", value: "tradeMake", width: 120, sortable: true },
                { text: "Trade Model", value: "tradeModel", width: 130, sortable: true },
                { text: "Trade Offer", value: "tradeValue", width: 130, sortable: true },
                { text: "Trade Mileage", value: "tradeMileage", width: 130, sortable: true },
                { text: "Est. Trade Equity", value: "tradeEquity", width: 150, sortable: true },
                { text: "Trade Purchase Type", value: "tradePurchaseType", sortable: true, width: 120 },
                { text: "Trade Purchase Date", value: "tradePurchaseDate", sortable: true, width: 120 },
                { text: "Current Payment", value: "tradePayment", width: 120, sortable: true },
                { text: "Lender", value: "lenderName", width: 120, sortable: true },
                { text: "Trade Type", value: "tradePaymentType", width: 120, sortable: true },
                { text: "Remaining Payments", value: "remainingPayments", width: 120, sortable: true },
                { text: "Financing Term", value: "tradeTerm", width: 120, sortable: true },
                { text: "Financing Maturity Date", value: "maturityDate", width: 150, sortable: true },
                { text: "Credit Tier", value: "tier", width: 120, sortable: true },
                { text: "Pre-approvals", value: "preApprovalCount", width: 130 },
                { text: "Pre-quals", value: "traits.preQualificationsCount", width: 110 },
                { text: "Leads", value: "traits.leadsCount", width: 110 },
                { text: "Lead Type", value: "userLeads", width: 180 },
                { text: "Applications", value: "traits.financeAppsSubmittedCount", width: 125 },
                { text: "Sales", value: "traits.salesCount", width: 110 },
                { text: "Phone Number", value: "phoneNumber", sortable: true, width: 130 },
                { text: "Sms", value: "smsEnabled", width: 100 },
                {
                    text: "Vehicle Of Interest (VOI)",
                    value: "vehicleOfInterest",
                    sortable: true,
                    width: 150,
                    exportable: false,
                },
                { text: "VOI StockType", value: "vehicleOfInterest.stockType", sortable: false, width: 125 },
                { text: "VOI In Transit", value: "vehicleOfInterest.inTransit", sortable: true, width: 80 },
                { text: "VOI Certified", value: "vehicleOfInterest.certified", sortable: false, width: 105 },
                { text: "VOI Year", value: "vehicleOfInterest.year", sortable: true, width: 80 },
                { text: "VOI Make", value: "vehicleOfInterest.make", sortable: true, width: 125 },
                { text: "VOI Stock Number", value: "vehicleOfInterest.stockNumber", sortable: false, width: 125 },
                { text: "VOI Model", value: "vehicleOfInterest.model", sortable: true, width: 125 },
                { text: "Time Zone", value: "timeZone", width: 125 },
                { text: "Address", value: "address", sortable: true, width: 200 },
                { text: "Street", value: "address.street", sortable: false, width: 150 },
                { text: "City", value: "address.city", sortable: true, width: 125 },
                { text: "State", value: "address.stateCode", width: 100 },
                { text: "Zip", value: "address.zipCode", sortable: true, width: 70 },
                { text: "DMA Name", value: "address.dma.name", sortable: true, width: 150 },
                { text: "DMA Rank", value: "address.dma.rank", width: 110 },
                { text: "DMA Code", value: "address.dma.code", width: 125 },
                {
                    text: "Active Vehicles in Garage",
                    value: "traits.activeVehiclesInGarageCount",
                    width: 125,
                },
                { text: "Saved Vehicles", value: "traits.garageSavedVehicleCount", sortable: true, width: 125 },
                {
                    text: "Recently Viewed Vehicles",
                    value: "traits.garageViewedVehicleCount",
                    sortable: true,
                    width: 125,
                },
                {
                    text: "Garage Vehicles",
                    value: "traits.vehiclesInGarageCount",
                    sortable: true,
                    width: 145,
                },
                { text: "Garage: New", value: "traits.new", sortable: true, width: 145 },
                { text: "Garage: Used", value: "traits.used", sortable: true, width: 145 },
                {
                    text: "Stage Transition",
                    value: "programSubscriptionStages.lastModifiedDate",
                    sortable: true,
                    width: 145,
                },
                { text: "Customer Status", value: "customerStatus", sortable: true, width: 145 },
                { text: "Assigned Dealer", value: "assignedDealer", sortable: false, width: 145 }, // Dealer User
                { text: "Language", value: "locale", sortable: true, width: 150 },

                { text: "Actions", value: "actions", sortable: false, width: 125, exportable: false },
            ];
        },
        filteredHeaderValues() {
            const FILTER_OUT_VALUES = !this.isProgramUser ? ["assignedDealer"] : [];
            this.headers = this.headers.filter((header) => !FILTER_OUT_VALUES.includes(header.value));
        },
        filterOutFieldsBasedOnFeatureFlag() {
            if (!this.isAtlasStickyFiltersFeatureEnabled) {
                const FILTER_OUT_VALUES = [
                    "assignedDealer",
                    "vehicleOfInterest.stockNumber",
                    "programSubscriptionStages.lastModifiedDate",
                    "customerStatus",
                ];
                this.headers = this.headers.filter((header) => !FILTER_OUT_VALUES.includes(header.value));
            }
        },
        removeDynamicFieldsFromList() {
            this.headers = this.headers.filter(
                (header) => header.value !== "tradePurchaseType" && header.value !== "tradePurchaseDate"
            );
        },
    },
};
</script>
