<template>
    <span style="white-space: nowrap">
        <info-tooltip v-if="rank !== 8" size="20" :max-width="250">
            <p class="font-weight-black subtitle-2 mb-0">{{ stage }}</p>
            <p class="body-1 mb-0">{{ getRankedStageMessage(rank) }}</p>
        </info-tooltip>
        {{ rank }} - {{ stage }}
    </span>
</template>

<script>
import InfoTooltip from "Components/InfoTooltip";

export default {
    name: "UserRankedStageToolTip",
    components: { InfoTooltip },
    props: {
        rank: {
            type: Number,
            required: true,
        },
        stage: {
            type: String,
            required: true,
        },
    },
    methods: {
        getRankedStageMessage(rank) {
            let message = "";

            switch (rank) {
                case 0:
                    message = "Imported into the system, but not identified for marketing.";
                    break;
                case 1:
                    message =
                        "Created a profile, logged in within last 60 days. Also encountered an issue with the pre-qual process, finance application, or password reset process.";
                    break;
                case 2:
                    message = "Qualifies for one or more marketing campaigns.";
                    break;
                case 3:
                    message =
                        "Created a profile, logged in within last 60 days, has no Active Vehicles in their Garage.";
                    break;
                case 4:
                    message =
                        "Created a profile, logged in within last 60 days, has at least one Active Vehicle in their Garage.";
                    break;
                case 5:
                    message =
                        "Created a profile, logged in within last 60 days, has initiated contact with the dealer.";
                    break;
                case 6:
                    message = "Created a profile, logged in within last 60 days, submitted a Finance Application.";
                    break;
                case 7:
                    message = "Created a profile, logged in within last 60 days, completed a vehicle purchase.";
                    break;
                case 8:
                    //Jira ATS-177 requires to not display users with rank 8 (needs dealer)
                    message = "";
                    break;
                case 9:
                    message = "Hasn't logged into the platform for 60+ days.";
                    break;
                default:
                    message = "The current rank is " + rank;
            }

            return message;
        },
    },
};
</script>

<style scoped></style>
