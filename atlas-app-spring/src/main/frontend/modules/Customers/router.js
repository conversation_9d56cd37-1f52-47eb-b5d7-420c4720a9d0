import Vue from "vue";
import VueRouter from "vue-router";
import { routerOptions, layout, route, configureRouter } from "Util/routerHelper";
import UserDeal from "@/modules/Customers/views/UserDeal";

Vue.use(VueRouter);

const PATH_PREFIX = "/customers";

const routes = [
    layout("Default", [
        route("Customers", "UserSearchHome", null, PATH_PREFIX),
        route("Customers", "UserDetails", null, PATH_PREFIX + "/:userId"),
        {
            name: "UserDeal",
            path: PATH_PREFIX + "/:userId/deal/:dealId/",
            component: UserDeal,
            props: (route) => {
                const userId = route.params.userId;
                const dealId = parseInt(route.params.dealId);
                const dealerId = route.query.dealerIds;
                return { userId, dealId, dealerId };
            },
        },
    ]),
];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
