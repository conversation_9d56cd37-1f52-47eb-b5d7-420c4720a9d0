import { make } from "vuex-pathify";
import loader from "@/util/loader";
import api from "@/util/api";
import Vue from "vue";
import lodashGet from "lodash/get";
import lodashIsNil from "lodash/isNil";

const initialState = {
    userModel: {
        data: null,
        loader: loader.defaultState(),
    },
    deals: {
        data: null,
        loader: loader.defaultState(),
    },
    vehicleSales: {
        data: null,
        loader: loader.defaultState(),
    },
    contractRequests: {
        data: null,
        loader: loader.defaultState(),
    },
    leads: {
        data: null,
        loader: loader.defaultState(),
    },
    preApprovals: {
        data: null,
        loader: loader.defaultState(),
    },
    documents: {
        data: [],
        loader: loader.defaultState(),
    },
    userStatus: {
        data: null,
        loader: loader.defaultState(),
    },
    quickLinks: {
        data: [],
        loader: loader.defaultState(),
    },
};

const mutations = {
    ...make.mutations(initialState),
    addVehicleSale(state, newVehicleSaleRecord) {
        if (lodashIsNil(state.vehicleSales.data.vehicleSales)) {
            state.vehicleSales.data = {
                vehicleSales: [newVehicleSaleRecord],
            };
        } else {
            state.vehicleSales.data.vehicleSales.unshift(newVehicleSaleRecord);
        }
    },
    ADD_DOCUMENTS: (state, payload) => {
        Vue.set(state.documents, "data", payload);
    },
    UPLOAD_DOC_LOADER: (state, payload) => {
        state.documents.loader = payload;
    },
};

const actions = {
    fetchUser({ commit, state }, { dealerId, userId }) {
        commit("SET_USER_MODEL", {
            data: null,
            loader: loader.started(),
        });
        let params = {};
        if (!lodashIsNil(dealerId)) {
            params["dealerIds"] = dealerId;
        }
        return api
            .get(`/users/${userId}`, params)
            .then((response) => {
                commit("SET_USER_MODEL", {
                    data: lodashGet(response, "data", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_USER_MODEL", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    fetchDeals({ commit, state }, { dealerId, programId, isProgramUser, page }) {
        commit("SET_DEALS", {
            data: null,
            loader: loader.started(),
        });
        let params = {};
        if (!lodashIsNil(dealerId)) {
            params["dealerIds"] = dealerId;
            params["page"] = page;
        }

        if (isProgramUser && !lodashIsNil(programId)) {
            params["programIds"] = programId;
        }

        api.get(`/users/${state.userModel.data.id}/deals`, params)
            .then((response) => {
                commit("SET_DEALS", {
                    data: response.data,
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_DEALS", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    addVehicleSale({ commit }, { vehicleSaleRecord }) {
        commit("addVehicleSale", vehicleSaleRecord);
    },
    fetchUserVehicleSales({ commit, state }, { dealerId, userId }) {
        commit("SET_VEHICLE_SALES", {
            data: null,
            loader: loader.started(),
        });
        let params = {};
        if (!lodashIsNil(dealerId)) {
            params["dealerIds"] = dealerId;
        }
        api.get(`/users/${userId}/vehicles/sales`, params)
            .then((response) => {
                commit("SET_VEHICLE_SALES", {
                    data: lodashGet(response, "data", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_VEHICLE_SALES", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    fetchUserContractRequests({ commit, state }, { dealerId, userId }) {
        commit("SET_CONTRACT_REQUESTS", {
            data: null,
            loader: loader.started(),
        });
        let params = {};
        if (!lodashIsNil(dealerId)) {
            params["dealerIds"] = dealerId;
        }
        api.get(`/users/${userId}/contractRequests`, params)
            .then((response) => {
                commit("SET_CONTRACT_REQUESTS", {
                    data: lodashGet(response, "data", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.error(error);
                commit("SET_CONTRACT_REQUESTS", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    fetchUserLeads({ commit, state }, { dealerId }) {
        commit("SET_LEADS", {
            data: null,
            loader: loader.started(),
        });
        let params = {};
        params["size"] = 100;
        if (!lodashIsNil(dealerId)) {
            params["dealerIds"] = dealerId;
        }
        api.get(`/users/${state.userModel.data.id}/leads`, params)
            .then((response) => {
                commit("SET_LEADS", {
                    data: lodashGet(response, "data", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_LEADS", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    fetchPreApprovals({ commit, state }, { dealerId }) {
        commit("SET_PRE_APPROVALS", {
            data: null,
            loader: loader.started(),
        });

        let params = {};
        if (!lodashIsNil(dealerId)) {
            params["dealerIds"] = dealerId;
        }
        api.get(`/users/${state.userModel.data.id}/pre-approvals`, params)
            .then((response) => {
                commit("SET_PRE_APPROVALS", {
                    data: response,
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_PRE_APPROVALS", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    fetchDocumentsList({ commit, state }, { dealerId, userId }) {
        commit("UPLOAD_DOC_LOADER", loader.started());
        const result = api
            .get(`/document_upload/dealer/${dealerId}/user/${userId}`)
            .then((response) => {
                commit("ADD_DOCUMENTS", response.data.documents);
                commit("UPLOAD_DOC_LOADER", loader.successful());

                return true;
            })
            .catch((error) => {
                console.error("error: ", error);
                commit("UPLOAD_DOC_LOADER", loader.error(error));
                return false;
            });

        return result;
    },
    fetchUserStatus({ commit, state }, { dealerId, userId }) {
        commit("SET_USER_STATUS", {
            data: null,
            loader: loader.started(),
        });
        let params = {};
        if (lodashIsNil(dealerId)) {
            console.error("dealerId is required");
            commit("SET_USER_STATUS", {
                data: null,
                loader: loader.error("dealerId is required"),
            });
            return;
        }
        params["dealerId"] = dealerId;
        api.get(`/users/${userId}/login-details`, params)
            .then((response) => {
                commit("SET_USER_STATUS", {
                    data: lodashGet(response, "data", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.error(error);
                commit("SET_USER_STATUS", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    fetchQuickLinks({ commit }, { userId, dealerId }) {
        commit("SET_QUICK_LINKS", {
            data: null,
            loader: loader.started(),
        });
        if (lodashIsNil(dealerId) || lodashIsNil(userId)) {
            commit("SET_QUICK_LINKS", {
                data: null,
                loader: loader.error("Invalid dealerId or userId"),
            });
            console.error("Invalid dealerId or userId");
            return;
        }
        let params = {};
        params["userId"] = userId;
        params["dealerId"] = dealerId;
        api.get(`/quick-links/v2`, params)
            .then((response) => {
                commit("SET_QUICK_LINKS", {
                    data: lodashGet(response, "data", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_QUICK_LINKS", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    triggerFastPassEventAndLead({ commit }, { user, dealer }) {
        const urlParams = new URLSearchParams(window.location.search);
        const utm_source = urlParams.get("utm_source");
        if (utm_source === "fastPass")
            return api.post(`/fast-pass/users/${user}/dealers/${dealer}`).then((response) => {
                // Remove utm_source query param
                const url = new URL(window.location.href);
                url.searchParams.delete("utm_source");
                window.history.replaceState({}, "", url);
            });
    },
};

const getters = {
    isEnvironmentProd: () => {
        const environment = lodashGet(window, "_APP_CONFIG.env[0]", "local") || "local";
        return environment === "prod";
    },
    isDigitalRetailsUserSource: (state) => {
        return lodashGet(state.userModel.data, "source.channel", "") === "digital-retail";
    },
    isUpgradeUserSource: (state) => {
        return lodashGet(state.userModel.data, "source.channel", "") === "upgrade";
    },
    isEcommerceProductUser: (state) => {
        return lodashGet(state.userModel.data, "program.product.id", "") === 102;
    },
};

export default {
    namespaced: true,
    state: initialState,
    actions,
    mutations,
    getters,
};
