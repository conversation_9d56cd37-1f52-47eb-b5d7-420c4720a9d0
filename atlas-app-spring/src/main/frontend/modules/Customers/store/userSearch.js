import { make } from "vuex-pathify";
import loader from "@/util/loader";
import searchUtils from "@/util/searchUtils";
import get from "lodash/get";
import remove from "lodash/remove";
import keys from "lodash/keys";
import api from "Util/api";

const uriRoot = "/users";

const initialState = {
    ...searchUtils.state(),
    searchUri: null,
    initialLoad: true,
    pushHistoryEnabled: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "createdDate,desc",
        page: 1,
    }),
    facets: null,
    filterNames: null,
    filters: searchUtils.parseFiltersFromUrl(),
    searchLoader: {
        loader: loader.defaultState(),
        data: [],
    },
    displayFields: [
        "firstName",
        "lastName",
        "stage",
        "preApprovalCount",
        "traits.preQualificationsCount",
        "traits.tradeInsCount",
        "traits.activeVehiclesInGarageCount",
        "traits.vehiclesInGarageCount",
        "traits.leadsCount",
        "traits.financeAppsSubmittedCount",
        "traits.salesCount",
        "lastLoginAt",
        "createdDate",
        "actions",
        "logins",
        "clientAdvisor",
        "websiteVisitsBeforeSignup",
        "traits.tradeInsCount",
        "tier",
    ],
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0,
    },
    /**
     * pills schema
     *      {
                "name": {
                    label: 'Name',
                    enabled: false,
                },
                "warrantyStatuses": {
                    label: 'Warranty',
                    enabled: true
                },
                "topDmas": {
                    type: 'range'
                }
            }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     *     type: the type of filter i.e. 'range' filter
     * }
     */
    pills: {
        dealerIds: {
            enabled: false,
        },
        name: {
            enabled: false,
        },

        emailOrPhone: {
            enabled: false,
        },
        city: {
            enabled: false,
        },
        vehicleOfInterest: {
            enabled: false,
        },
        createdDate: {
            enabled: false,
        },
        dmaCodes: {
            label: "DMAs",
            facet: "dmas",
        },
        topDmas: {
            type: "range",
        },
        programIds: {
            enabled: false,
        },
    },
    stageTiles: {
        data: null,
        loader: loader.defaultState(),
    },
    stageUsers: {
        data: null,
        loader: loader.defaultState(),
    },
    onlineUsers: {
        data: [],
        loader: loader.defaultState(),
    },
    inShowroomUsers: {
        data: [],
        loader: loader.defaultState(),
    },
    stageTitle: null,
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations(),

    SET_STAGE_TITLE(state, stageTitle) {
        console.log(stageTitle);
        state.stageTitle = stageTitle;
    },
};

const actions = {
    ...make.actions(initialState),
    ...searchUtils.actions(uriRoot, "User Search"),
    setSearchUri({ commit, state }, searchUri) {
        commit("SET_SEARCH_URI", searchUri);
    },
    fetchStageTiles({ commit, state }) {
        commit("SET_STAGE_TILES", {
            data: null,
            loader: loader.started(),
        });

        const body = { ...state.filters };
        body.searchMethods = state.searchMethods;
        body.includes = get(state, "fetchSource.includes", null);
        body.excludes = get(state, "fetchSource.excludes", null);

        let stageUrl = `${state.searchUri}/sales/stages-stats`;

        api.post(stageUrl, body)
            .then((response) => {
                let filterTiles = get(response, "data", null);
                remove(filterTiles.stages, function (n) {
                    return keys(n)[0] === "Needs Dealer";
                });
                commit("SET_STAGE_TILES", {
                    data: get(response, "data", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_STAGE_TILES", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    fetchStageRecords({ commit, state }) {
        commit("SET_SEARCH_LOADER", {
            data: null,
            loader: loader.started(),
        });

        console.log(`in fetchStageRecords: ${state.stageTitle}`);

        const body = { ...state.filters };
        body.searchMethods = state.searchMethods;
        body.includes = get(state, "fetchSource.includes", null);
        body.excludes = get(state, "fetchSource.excludes", null);

        let baseUrl = `${state.searchUri}/${state.stageTitle}/search`;
        let queryUrl = `?page=${state.pageable.page}&sort=${state.pageable.sort}`;
        let stageUrl = `${baseUrl}${queryUrl}`;

        api.post(stageUrl, body)
            .then((response) => {
                console.log(response);
                console.log(response.data.content);
                commit("SET_SEARCH_LOADER", {
                    data: response.data.content,
                    loader: loader.successful(),
                });
                commit("SET_PAGE_METADATA", response.data.pageMetadata);
            })
            .catch((error) => {
                console.log(error);
                commit("SET_SEARCH_LOADER", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },

    fetchOnlineUsers({ commit }, dealerId) {
        commit("SET_ONLINE_USERS", {
            data: [],
            loader: loader.started(),
        });
        api.get(`/online-users/dealers/${dealerId}`)
            .then((response) => {
                commit("SET_ONLINE_USERS", {
                    data: response.data,
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                commit("SET_ONLINE_USERS", {
                    data: [],
                    loader: loader.error(),
                });
            });
    },
    fetchInShowroomUsers({ commit, rootState }, dealerId) {
        let url = `/showroom-users/dealers/${dealerId}`;
        if (rootState.loggedInUser.featureFlags["ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM_V2"]) {
            url = `/showroom-users/v2/dealers/${dealerId}`;
        }
        commit("SET_IN_SHOWROOM_USERS", {
            data: [],
            loader: loader.started(),
        });
        const result = api
            .get(url)
            .then((response) => {
                commit("SET_IN_SHOWROOM_USERS", {
                    data: response.data,
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                commit("SET_IN_SHOWROOM_USERS", {
                    data: [],
                    loader: loader.error(),
                });
            });

        return result;
    },
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters(),
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
