<template>
    <v-app>
        <v-container fluid>
            <v-row no-gutters>
                <v-col cols="12">
                    <v-alert v-if="isSuccess()" color="primary" dark icon="mdi-check" border="left" prominent>
                        <h1>SMS Verified Successfully</h1>
                        Thank you for verifying your phone number.
                    </v-alert>

                    <v-alert v-else color="warning" dark icon="mdi-close" border="left" prominent>
                        <h1>SMS Verification Failed</h1>
                        Your SMS verification has failed. Please trigger another verification to continue.
                    </v-alert>
                </v-col>
            </v-row>
        </v-container>
    </v-app>
</template>
<script>
export default {
    props: {
        status: {
            required: true,
            type: String,
        },
    },
    methods: {
        isSuccess() {
            return this.status.toLowerCase() === "success";
        },
    },
};
</script>
