<template>
    <v-container fluid>
        <v-row no-gutters>
            <v-col cols="12">
                <h1>Welcome to Atlas!</h1>
                <p>From here you can manage your dealership features.</p>
                <program-access-list v-if="isProgramTypeUser" />
                <dealer-access-list v-else />
            </v-col>
        </v-row>
    </v-container>
</template>
<script>
import DealerAccessList from "@/modules/Home/components/DealerAccessList";
import ProgramAccessList from "@/modules/Home/components/ProgramAccessList";
import { get } from "vuex-pathify";
export default {
    components: { DealerAccessList, ProgramAccessList },
    computed: {
        isProgramTypeUser: get("loggedInUser/isProgramTypeUser"),
    },
};
</script>
