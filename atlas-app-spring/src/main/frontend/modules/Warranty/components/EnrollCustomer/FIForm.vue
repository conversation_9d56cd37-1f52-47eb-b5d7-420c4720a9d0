<template>
    <v-card>
        <v-card-title>F&I Information</v-card-title>
        <v-card-text>
            <v-row>
                <v-col>
                    <v-text-field v-model="dealDealerFAndINumber" dense hide-details outlined label="F&I Number" />
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-text-field v-model="dealDealerContactName" dense hide-details outlined label="Associate Name" />
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script>
import { get, sync } from "vuex-pathify";
import FISummary from "@/modules/Warranty/components/EnrollCustomer/FISummary";

export default {
    name: "FIForm",
    computed: {
        dealDealerFAndINumber: sync("eRating/<EMAIL>"),
        dealDealerContactName: sync("eRating/<EMAIL>"),
    },
};
</script>

<style scoped></style>
