<template>
    <base-form>
        <v-row>
            <v-col>
                <v-banner single-line> Financial Information </v-banner>
            </v-col>
        </v-row>

        <v-row>
            <v-col>
                <v-text-field
                    v-model="lienholderName"
                    dense
                    :rules="[rules.required]"
                    :hide-details="hideDetails"
                    outlined
                    label="Lien Holder *"
                />
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="12" md="auto" lg="4">
                <v-text-field
                    v-model="lienholderPhoneNumber"
                    dense
                    :hide-details="hideDetails"
                    outlined
                    label="Phone Number"
                />
            </v-col>
        </v-row>

        <v-row>
            <v-col>
                <v-text-field
                    v-model="lienholderStreetAddress"
                    dense
                    :hide-details="hideDetails"
                    outlined
                    label="Street Address"
                />
            </v-col>
        </v-row>

        <v-row>
            <v-col cols="5" sm="auto">
                <v-text-field v-model="lienholderCity" dense :hide-details="hideDetails" outlined label="City" />
            </v-col>
            <v-col cols="6" sm="auto" lg="3">
                <v-select
                    v-model="lienholderState"
                    :items="states"
                    label="State"
                    :hide-details="hideDetails"
                    dense
                    outlined
                />
            </v-col>
            <v-col cols="6" sm="auto" lg="4">
                <v-text-field v-model="lienholderZip" dense :hide-details="hideDetails" outlined label="Zip Code" />
            </v-col>
        </v-row>

        <v-row>
            <v-col cols="12" sm="6">
                <v-text-field
                    v-model="vehiclePurchasePrice"
                    label="Purchase Price *"
                    prefix="$"
                    type="number"
                    maxlength="8"
                    :rules="[rules.required]"
                    :hide-details="hideDetails"
                    outlined
                    dense
                />
            </v-col>
            <v-col cols="12" sm="6">
                <v-select
                    v-model="vehicleConditionCode"
                    :items="vehicleConditionCodes"
                    item-text="description"
                    item-value="code"
                    label="Vehicle Condition *"
                    :rules="[rules.required]"
                    :hide-details="hideDetails"
                    outlined
                    dense
                />
            </v-col>
        </v-row>
        <Footer />
    </base-form>
</template>

<script>
import { sync } from "vuex-pathify";
import states from "@/util/states";
import BaseForm from "@/modules/Warranty/components/EnrollCustomer/EnrollCustomerForm/BaseForm";
import formRules from "Util/formRules";

export default {
    name: "VehicleInfo",
    components: { BaseForm },
    data() {
        return {
            states: states.getStates(),
            rules: formRules,
            hideDetails: "auto",
        };
    },
    computed: {
        lienholderName: sync("eRating/<EMAIL>"),
        lienholderPhoneNumber: sync("eRating/<EMAIL>"),
        lienholderStreetAddress: sync("eRating/<EMAIL>"),
        lienholderCity: sync("eRating/<EMAIL>"),
        lienholderState: sync("eRating/<EMAIL>"),
        lienholderZip: sync("eRating/<EMAIL>"),
        vehiclePurchasePrice: sync("eRating/<EMAIL>"),
        vehicleConditionCodes: sync("eRating/enrollCustomerFormModel@vehicleConditionCodes"),
        vehicleConditionCode: sync("eRating/<EMAIL>"),
    },
};
</script>

<style scoped></style>
