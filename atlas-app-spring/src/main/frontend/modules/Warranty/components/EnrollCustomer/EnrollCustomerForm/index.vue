<template>
    <v-container>
        <v-skeleton-loader v-if="loading" type="container" />
        <v-card v-else>
            <v-card-title class="d-flex justify-space-between">
                {{ pageTitle }}
            </v-card-title>
            <v-card-text>
                <v-form>
                    <v-stepper v-model="step">
                        <v-stepper-header step="step">
                            <v-stepper-step :complete="step > 1" step="1"> 1. Customer Info </v-stepper-step>

                            <v-divider></v-divider>

                            <v-stepper-step :complete="step > 2" step="2"> 2. Vehicle Info </v-stepper-step>

                            <v-divider></v-divider>

                            <v-stepper-step :complete="step > 3" step="3"> 3. Agreement Info </v-stepper-step>
                        </v-stepper-header>

                        <v-stepper-items>
                            <v-stepper-content step="1">
                                <v-card>
                                    <customer-info />
                                </v-card>
                            </v-stepper-content>

                            <v-stepper-content step="2">
                                <v-card>
                                    <vehicle-info />
                                </v-card>
                            </v-stepper-content>

                            <v-stepper-content step="3">
                                <v-card>
                                    <agreement-info />
                                </v-card>
                            </v-stepper-content>
                        </v-stepper-items>
                    </v-stepper>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>

<script>
import { get, sync } from "vuex-pathify";
import api from "Util/api";
import CustomerInfo from "@/modules/Warranty/components/EnrollCustomer/EnrollCustomerForm/CustomerInfo";
import VehicleInfo from "@/modules/Warranty/components/EnrollCustomer/EnrollCustomerForm/VehicleInfo";
import AgreementInfo from "@/modules/Warranty/components/EnrollCustomer/EnrollCustomerForm/AgreementInfo";

export default {
    name: "EnrollCustomerForm",
    components: { AgreementInfo, VehicleInfo, CustomerInfo },
    data() {
        return {
            loading: false,
        };
    },
    computed: {
        enrollCustomerForm: get("eRating/enrollCustomerForm"),

        step: sync("eRating/enrollCustomerFormModel@warrantyFormStep"),

        agreementFormNumber: sync("eRating/<EMAIL>"),
        agreementFormNumbers: sync("eRating/enrollCustomerFormModel@agreementFormNumbers"),
        agreementSurcharges: sync("eRating/enrollCustomerFormModel@agreementSurcharges"),
        vehicleConditionCodes: sync("eRating/enrollCustomerFormModel@vehicleConditionCodes"),

        dealerId: get("eRating/dealer@id"),
        userId: get("eRating/customer@id"),
        selectedPremium: get("eRating/selectedPremium"),
        vehicle: get("eRating/enrollCustomerForm@vehicle"),
        purchaseDate: get("eRating/<EMAIL>"),

        pageTitle() {
            if (this.selectedPremium) {
                return this.selectedPremium.giveAway === true
                    ? "Lifetime Powertrain Warranty"
                    : this.selectedPremium.planName;
            }
            return "";
        },
    },
    created() {
        this.initiateStep();
        this.loadEnrollCustomerModel();
    },
    methods: {
        initiateStep() {
            this.step = 1;
        },
        loadEnrollCustomerModel() {
            const purchaseDate = this.purchaseDate;
            const request = {
                ...this.selectedPremium,
                ...this.vehicle,
                purchaseDate,
                dealerIds: this.dealerId,
            };
            if (_.isNil(request.scheduleId)) {
                return;
            }
            this.loading = true;
            api.post(`/dealer/warranty/${this.userId}/load-enroll-customer-model`, request)
                .then((resp) => {
                    this.agreementFormNumbers = _.get(resp, "data.forms", null);
                    this.agreementSurcharges = _.get(resp, "data.surchargesList", null);
                    this.vehicleConditionCodes = _.get(resp, "data.vehicleConditionCodes", null);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
    },
};
</script>

<style scoped></style>
