<template>
    <v-form ref="form" v-model="valid" lazy-validation>
        <v-container>
            <v-row>
                <v-col class="d-flex justify-end font-italic py-0"> * required </v-col>
            </v-row>

            <slot></slot>

            <v-row v-if="!hideFooter" class="mt-8">
                <v-col>
                    <v-btn
                        v-if="finalStep"
                        color="primary"
                        :loading="submitting"
                        type="submit"
                        @click.prevent="enrollCustomer()"
                    >
                        Enroll
                    </v-btn>
                    <v-btn v-else color="primary" :loading="submitting" @click="onNext"> Next </v-btn>
                    <v-btn v-if="step > 1" text @click="previousStep"> Previous </v-btn>
                </v-col>
            </v-row>
        </v-container>
        <enroll-customer-error-modal v-if="finalStep" />
    </v-form>
</template>

<script>
import { get, sync } from "vuex-pathify";
import api from "@/util/api";
import EventBus from "@/util/eventBus";
import EnrollCustomerErrorModal from "@/modules/Warranty/components/EnrollCustomer/EnrollCustomerForm/EnrollCustomerErrorModal";

export default {
    name: "BaseForm",
    components: { EnrollCustomerErrorModal },
    props: {
        hideFooter: {
            type: Boolean,
            required: false,
            default: false,
        },
        finalStep: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            valid: true,
            submitting: false,
        };
    },
    computed: {
        dealerId: get("eRating/dealer@id"),
        userId: get("eRating/customer@id"),
        enrollCustomerForm: get("eRating/enrollCustomerForm"),
        vehicle: get("eRating/enrollCustomerForm@vehicle"),
        step: sync("eRating/enrollCustomerFormModel@warrantyFormStep"),
    },
    methods: {
        validate() {
            this.$refs.form.validate();
        },
        resetValidation() {
            this.$refs.form.resetValidation();
        },
        nextStep() {
            this.step++;
        },
        previousStep() {
            if (this.step > 1) {
                this.step--;
            }
        },
        onNext() {
            this.submitting = true;
            this.validate();

            setTimeout(() => {
                this.submitting = false;

                if (this.valid) {
                    this.nextStep();
                }
            }, 500);
        },
        enrollCustomer() {
            this.validate();
            this.submitting = true;
            this.enrollCustomerForm.dealerIds = this.dealerId;
            const url = `/dealer/warranty/${this.userId}/register-applicant`;

            if (this.valid) {
                api.post(url, this.enrollCustomerForm)
                    .then((resp) => {
                        this.$router.push({
                            path: `/dealer/warranty/${this.userId}/confirmations/${this.vehicle.vin}`,
                            query: { initialRegistration: true, dealerIds: this.dealerId },
                        });
                    })
                    .catch((error) => {
                        const errorType = _.get(error.response, "data.type", null);

                        if (
                            //redirect to confirmation page as user was attempting duplicate registration
                            //this shouldn't happen under normal circumstances
                            errorType === "LIFETIME_WARRANTY_ALREADY_REGISTERED" ||
                            errorType === "PREMIUM_UPGRADE_ALREADY_REGISTERED"
                        ) {
                            this.$router.push({
                                path: `/dealer/warranty/${this.userId}/confirmations/${this.vehicle.vin}`,
                                query: { initialRegistration: false, dealerIds: this.dealerId },
                            });
                        } else {
                            const evt = {
                                title: "Unexpected Error",
                                instructions: "Unexpected error enrolling customer, please try again",
                            };
                            EventBus.$emit("enroll-customer-error-modal", evt);
                        }
                    })
                    .finally(() => {
                        this.submitting = false;
                    });
            } else {
                this.submitting = false;
            }
        },
    },
};
</script>

<style scoped></style>
