<template>
    <v-card>
        <v-card-title class="d-flex justify-space-between">
            <span>Customer Search</span>
            <!--TODO hasPermission(#request.getAttribute('dealer'), 'user:save-a-deal')-->
            <v-btn
                v-if="$acl.hasDealerPermission(dealerIds, 'user:save-a-deal') && saveADealEnabledForDealer"
                color="primary"
                prefix="mdiAccountPlus"
                outlined
                small
                :to="{ path: `/dealer/warranty/create-customer`, query: { dealerIds: dealerIds } }"
            >
                Create Customer
                <v-icon right dark> mdi-account-plus </v-icon>
            </v-btn>
        </v-card-title>
        <v-card-text>
            <v-form v-model="valid" @submit.prevent="onSubmit">
                <v-row class="align-start">
                    <v-col cols="12" md="4">
                        <v-text-field
                            v-model="name"
                            :rules="[rules.characterLimit]"
                            dense
                            outlined
                            label="First name, Last name, email address, phone number"
                        />
                    </v-col>

                    <v-col cols="12" md="4" class="d-flex">
                        <div class="d-flex flex-row align-center justify-center">
                            <v-btn size="sm" type="submit" color="primary" :disabled="!valid || isLoading" class="mr-2">
                                Search
                            </v-btn>
                        </div>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
    </v-card>
</template>

<script>
import { call, get, sync } from "vuex-pathify";
import api from "@/util/api";
import { characterLimit } from "Util/formRules";
import { sanitize } from "Util/sanitize";

export default {
    name: "CustomerSearchForm",
    props: {
        dealerIds: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            saveADealEnabledForDealer: false,
            rules: { characterLimit },
            valid: false,
        };
    },
    computed: {
        name: sync("customerSearch/filters@name"),
        isLoading: get("customerSearch/<EMAIL>"),
    },
    created() {
        api.get(`/dealer/save-a-deal-enabled`, { dealerIds: this.dealerIds }).then((resp) => {
            this.saveADealEnabledForDealer = _.get(resp, "data", false) === true;
        });
    },
    methods: {
        doSearch: call("customerSearch/doSearch"),
        onSubmit(event) {
            if (!this.valid) return;
            this.name = sanitize(this.name);
            this.doSearch(event);
        },
    },
};
</script>

<style scoped></style>
