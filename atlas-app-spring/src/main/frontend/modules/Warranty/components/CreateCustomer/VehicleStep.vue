<template>
    <v-form ref="form" lazy-validation @submit.prevent.stop="next">
        <v-container>
            <v-row>
                <v-col lg="8">
                    <v-radio-group v-model="lookupType" hint="Vehicle selection only requires either one">
                        <v-radio label="VIN" value="vin" />
                        <v-radio label="Stock #" value="stock" />
                    </v-radio-group>

                    <v-row class="mt-3">
                        <v-col v-if="showVinLookup" cols="6" class="lookup-container">
                            <v-text-field
                                v-model="vin"
                                label="VIN"
                                placeholder="Enter VIN"
                                :rules="[rules.vinRequired, rules.vin]"
                                :error-messages="validVin ? null : 'Invalid VIN'"
                                hint="Must be 17 characters in length and exist in your inventory"
                                @blur="validateVin"
                            />
                        </v-col>
                        <v-col v-if="showStockLookup" cols="6" class="lookup-container">
                            <v-text-field
                                v-model="stockNumber"
                                label="Stock #"
                                placeholder="Enter Stock #"
                                :rules="[rules.stockNumberRequired, rules.stockNumber]"
                                :error-messages="validStockNumber ? null : 'Invalid Stock Number'"
                                hint="Must be a valid Stock # that existed in your inventory"
                                @blur="validStockNumber"
                            />
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <v-switch
                                v-model="sendToCrm"
                                label="Send to CRM"
                                :disabled="sendToCrmDisabled"
                                persistent-hint
                                :hint="!inDealerInventory ? 'Can not send to CRM, not in dealer inventory' : ''"
                            />
                        </v-col>
                    </v-row>
                </v-col>
                <v-col lg="4">
                    <div class="text-center">
                        <div style="margin-top: 20px">
                            <div v-if="vehicle">
                                <h1>
                                    {{ vehicle.year }} {{ vehicle.make }}
                                    {{ vehicle.model }}
                                </h1>
                                <h4>{{ vehicle.trim }}</h4>
                                <v-img :src="vehicle.imageUrl" />
                                <p>
                                    <strong>Stock Number:</strong>
                                    {{ vehicle.stockNumber }}<br />
                                    <strong>VIN:</strong> {{ vehicle.vin }}
                                </p>
                            </div>
                            <v-icon v-else style="font-size: 180px" color="grey lighten-3"> mdi-car </v-icon>
                        </div>
                    </div>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-btn color="primary" @click="next">Next</v-btn>
                </v-col>
            </v-row>
        </v-container>
    </v-form>
</template>

<script>
import _ from "lodash";
import api from "Util/api";

export default {
    name: "VehicleStep",
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },

    data() {
        return {
            lookupType: "vin",
            vin: "",
            validVin: true,
            stockNumber: "",
            validStockNumber: true,
            vehicle: null,
            sendToCrm: true,
            sendToCrmDisabled: false,
            rules: {
                vinRequired: (value) => !!value || !!this.stockNumber,
                stockNumberRequired: (value) => !!value || !!this.vin,
                vin: (value) => {
                    return value.length === 17;
                },
                stockNumber: (value) => {
                    return value.length >= 2;
                },
            },
        };
    },

    computed: {
        showVinLookup() {
            return this.lookupType === "vin";
        },
        showStockLookup() {
            return this.lookupType === "stock";
        },
        inDealerInventory() {
            const vehicleDealerId = _.get(this.vehicle, "dealerId");
            return vehicleDealerId === this.dealerId;
        },
    },

    watch: {
        lookupType(newValue) {
            switch (newValue) {
                case "vin":
                    if (!_.isEmpty(this.stockNumber)) {
                        this.stockNumber = "";
                        this.vehicle = null;
                        this.$refs.form.resetValidation();
                    }
                    break;
                case "stock":
                    if (!_.isEmpty(this.vin)) {
                        this.vin = "";
                        this.vehicle = null;
                        this.$refs.form.resetValidation();
                    }
                    break;
            }
        },
    },

    methods: {
        next() {
            if (this.$refs.form.validate()) {
                if (this.lookupType === "vin") {
                    this.validateVin().then((success) => {
                        this.validVin = success;
                        if (success) {
                            this.$emit("next", {
                                vin: this.vin,
                                stockNumber: this.stockNumber,
                                sendToCrm: this.sendToCrm,
                            });
                        }
                    });
                } else {
                    this.validateStockNumber().then((success) => {
                        this.validStockNumber = success;
                        if (success) {
                            this.$emit("next", {
                                vin: this.vin,
                                stockNumber: this.stockNumber,
                                sendToCrm: this.sendToCrm,
                            });
                        }
                    });
                }
            }
        },
        validateVin() {
            // standalone validator should not assume a field is required
            if (_.isNil(this.vin) || this.vin === "" || this.vin.length !== 17) {
                return Promise.resolve(true);
            }

            return api
                .get("/warranty/vehicles", { vin: this.vin })
                .then((response) => {
                    this.vehicle = response.data;

                    const vehicleDealerId = _.get(this.vehicle, "dealerId");
                    if (vehicleDealerId !== this.dealerId) {
                        this.sendToCrmDisabled = true;
                        this.sendToCrm = false;
                    } else {
                        this.sendToCrmDisabled = false;
                        this.sendToCrm = true;
                    }

                    return true;
                })
                .catch(() => {
                    return true;
                });
        },
        validateStockNumber() {
            // standalone validator should not assume a field is required
            if (_.isNil(this.stockNumber) || this.stockNumber === "" || this.stockNumber.length < 2) {
                return Promise.resolve(true);
            }

            return api
                .get(`/warranty/verify-stock-number`, {
                    stockNumber: this.stockNumber,
                    dealerIds: this.dealerId,
                })
                .then((response) => {
                    this.vehicle = response.data;
                    return true;
                })
                .catch(() => {
                    return false;
                });
        },
    },
};
</script>
