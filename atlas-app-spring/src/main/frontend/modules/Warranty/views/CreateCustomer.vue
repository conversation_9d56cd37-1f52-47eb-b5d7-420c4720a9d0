<template>
    <v-container :key="dealerId" fluid>
        <v-row>
            <v-col>
                <v-stepper v-model="step">
                    <v-stepper-header>
                        <v-stepper-step :complete="step > 1" step="1"> Email </v-stepper-step>

                        <v-divider />

                        <v-stepper-step :complete="step > 2" step="2"> Personal Info </v-stepper-step>

                        <v-divider />

                        <v-stepper-step :complete="step > 3" step="3"> Vehicle </v-stepper-step>
                    </v-stepper-header>

                    <v-stepper-items>
                        <v-stepper-content step="1">
                            <email-step :dealer-id="dealerId" @next="(u) => continueToPersonal(u)" />
                        </v-stepper-content>
                        <v-stepper-content step="2">
                            <personal-step :user="user" @next="(userForm) => continueToVehicle(userForm)" />
                        </v-stepper-content>
                        <v-stepper-content step="3">
                            <vehicle-step :dealer-id="dealerId" @next="(vehicleForm) => complete(vehicleForm)" />
                        </v-stepper-content>
                    </v-stepper-items>
                </v-stepper>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import EmailStep from "../components/CreateCustomer/EmailStep.vue";
import PersonalStep from "../components/CreateCustomer/PersonalStep";
import VehicleStep from "../components/CreateCustomer/VehicleStep";
import api from "Util/api";

export default {
    name: "CreateCustomer",
    components: { VehicleStep, PersonalStep, EmailStep },
    data() {
        return {
            step: 1,
            user: null,
            form: {},
        };
    },
    computed: {
        dealerId() {
            return this.$route.query.dealerIds;
        },
    },
    methods: {
        continueToPersonal(user) {
            this.user = user;
            this.form = {
                ...this.form,
                email: user.email,
            };
            this.next(2);
        },
        continueToVehicle(userForm) {
            this.form = {
                ...this.form,
                ...userForm,
            };
            this.next(3);
        },
        complete(vehicleForm) {
            this.form = {
                ...this.form,
                ...vehicleForm,
                dealerIds: this.dealerId,
            };

            api.post(`/warranty/create-customer`, this.form)
                .then((response) => {
                    const customerId = _.get(response, "data.id");
                    this.$router.push({
                        path: `/dealer/warranty/${customerId}/search-premiums`,
                        query: {
                            vin: this.form.vin,
                            dealerIds: this.dealerId,
                        },
                    });
                })
                .catch(() => {
                    this.$toast.error("Error Creating User");
                });
        },
        next(step) {
            this.step = step;
        },
    },
};
</script>
