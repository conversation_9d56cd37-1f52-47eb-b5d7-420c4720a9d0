import _ from "lodash";

export default {
    beforeCreate() {
        const self = this;

        const hasDealerPermission = (dealerId, permission) => {
            // mimic serverside DealerPermissionEvaluator behavior for now
            if (hasAuthority("ROLE_ADMIN")) {
                return hasAuthority(permission);
            }

            if (_.isNil(dealerId) || _.isNil(permission)) {
                return false;
            }

            return self.$store.getters["loggedInUser/hasDealerPermission"](dealerId, permission);
        };

        const hasAnyDealerPermission = (dealerId) => {
            // mimic serverside DealerPermissionEvaluator behavior for now
            if (hasAuthority("ROLE_ADMIN")) {
                return true;
            }

            if (_.isNil(dealerId)) {
                return false;
            }

            return self.$store.getters["loggedInUser/hasAnyDealerPermission"](dealerId);
        };

        const hasAnyAuthorities = (authorities) => {
            if (!_.isArray(authorities)) {
                return false;
            }

            return self.$store.getters["loggedInUser/hasAnyAuthorities"](authorities);
        };

        const hasAuthority = (authority) => {
            if (_.isNil(authority)) {
                return false;
            }
            if (!_.isArray(authority)) {
                authority = [authority];
            }

            return self.$store.getters["loggedInUser/hasAuthority"](authority);
        };

        const hasProgramPermission = (userId, permission) => {
            // mimic serverside DealerPermissionEvaluator behavior for now
            if (hasAuthority("ROLE_ADMIN")) {
                return hasAuthority(permission);
            }

            if (_.isNil(userId) || _.isNil(permission)) {
                return false;
            }

            return self.$store.getters["loggedInUser/hasProgramPermission"](userId, permission);
        };

        this.$acl = {
            hasDealerPermission,
            hasAnyDealerPermission,
            hasAnyAuthorities,
            hasAuthority,
            hasProgramPermission,
        };
    },
};
