package com.carsaver.partner.controller;


import com.carsaver.partner.model.dealercta.AtlasCTAReqRes;
import com.carsaver.partner.model.dealercta.Global;
import com.carsaver.partner.model.dealercta.Style;
import com.carsaver.partner.model.dealercta.StyleCTA;
import com.carsaver.partner.service.DealerCTAService;
import com.carsaver.partner.web.DealerCTAController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DealerCTAControllerTest {

    final DealerCTAService dealerCTAService = mock(DealerCTAService.class);

    @InjectMocks
    private DealerCTAController dealerCTAController;

    private MockMvc mockMvc;

    ObjectMapper objectMapper;

    @BeforeEach
    public void init() {
        objectMapper = new ObjectMapper();
        mockMvc = MockMvcBuilders.standaloneSetup(dealerCTAController).build();
    }

    @Test
    void upsertTest() throws Exception
    {
        List<StyleCTA> styleCTAS = new ArrayList<>();
        StyleCTA cta = new StyleCTA();
        cta.setCtaType("primary");
        cta.setName("primary");
        cta.setActive(true);
        cta.setDetailsHover(true);
        styleCTAS.add(cta);
        AtlasCTAReqRes atlasCTAReqRes = AtlasCTAReqRes.builder().style(Style.builder().styleCTAs(styleCTAS).global(Global.builder().padding("12").margin("12").height("12").build()).build()).build();
        when(dealerCTAService.upsertDealerCTA(atlasCTAReqRes)).thenReturn(new AtlasCTAReqRes());
        mockMvc.perform(MockMvcRequestBuilders.post("/api/dealer-cta")
                .content(objectMapper.writeValueAsString(atlasCTAReqRes)).contentType(MediaType.APPLICATION_JSON
                ))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    void retrieveVehicleTest() throws Exception
    {
        when(dealerCTAService.getDealerCTA(anyString())).thenReturn(new AtlasCTAReqRes());
        mockMvc.perform(MockMvcRequestBuilders.get("/api/dealer-cta/{dealerId}","123")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

}
