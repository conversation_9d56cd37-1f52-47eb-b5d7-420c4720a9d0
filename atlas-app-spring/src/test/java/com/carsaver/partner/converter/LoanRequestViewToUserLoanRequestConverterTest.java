package com.carsaver.partner.converter;

import com.carsaver.magellan.api.finance.FinanceDecisionModel;
import com.carsaver.magellan.api.finance.LoanDecisionService;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.finance.LoanResponseView;
import com.carsaver.partner.model.finance.FinanceDecision;
import com.carsaver.partner.model.mapper.FinanceDecisionMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LoanRequestViewToUserLoanRequestConverterTest {

    @Mock
    private LoanDecisionService mockLoanDecisionService;
    @Mock
    private FinancierClient mockFinancierClient;
    @Mock
    private FinanceDecisionMapper mockFinanceDecisionMapper;

    @InjectMocks
    private LoanRequestViewToUserLoanRequestConverter loanRequestViewToUserLoanRequestConverterUnderTest;

    @Test
    void testConvertToFinanceDecisionModel_ReturnsOptionalFinanceDecisionModel() {
        final Integer lenderId = 12;
        final Integer horizonId = 125;
        final LoanResponseView loanResponseView = mock(LoanResponseView.class);
        when(loanResponseView.getLenderId()).thenReturn(lenderId);
        final FinancierView financierView = new FinancierView();
        financierView.setHorizonId(horizonId);
        Optional<FinancierView> optFinancierView = Optional.ofNullable(financierView);
        when(mockFinancierClient.findByHorizonId(any(Integer.class))).thenReturn(optFinancierView);
        final FinanceDecisionModel buildFDM = FinanceDecisionModel.builder().build();
        final Optional<FinanceDecisionModel> optFinanceDecisionModel = Optional.of(buildFDM);
        when(mockLoanDecisionService.buildFinanceResponse(loanResponseView, financierView)).thenReturn(optFinanceDecisionModel);

        final Optional<FinanceDecisionModel> result = ReflectionTestUtils.invokeMethod(loanRequestViewToUserLoanRequestConverterUnderTest, "convertToFinanceDecisionModel", loanResponseView);
        assertNotNull(result);
    }

    @Test
    void testFinanceDecisionMapper() {
        // Arrange
        FinanceDecisionModel financeDecisionModel = FinanceDecisionModel.builder()
            .id(1)
            .approved(Boolean.TRUE)
            .stipulations(List.of("stipulation1", "stipulation2"))
            .build();
        FinanceDecision expectedFinanceDecision = FinanceDecision.builder()
            .id(1)
            .approved(Boolean.TRUE)
            .stipulations(List.of("stipulation1", "stipulation2"))
            .build();

        when(mockFinanceDecisionMapper.toModel(financeDecisionModel)).thenReturn(expectedFinanceDecision);

        // Act
        FinanceDecision financeDecision = mockFinanceDecisionMapper.toModel(financeDecisionModel);

        // Assert
        assertNotNull(financeDecision);
        assertEquals(financeDecisionModel.getId(), financeDecision.getId());
        assertEquals(financeDecisionModel.getStatus(), financeDecision.getStatus());
        assertEquals(financeDecisionModel.getStipulations(), financeDecision.getStipulations());
    }
}
