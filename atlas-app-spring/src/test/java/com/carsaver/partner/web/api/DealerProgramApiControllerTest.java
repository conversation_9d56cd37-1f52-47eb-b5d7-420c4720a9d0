package com.carsaver.partner.web.api;

import com.carsaver.core.DealerStatus;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.service.DealerProgramConvertor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
class DealerProgramApiControllerTest {
    public static final String DEALER_X_ID = "7127df27-de06-47c8-a69b-39f05a7d0391";

    private static final String PROGRAM_ID = "5e922fe4-e1e9-468c-b100-5b8f7cffcef3";

    @Mock
    private ProgramClient programClient;

    @Mock
    private UserClient userClient;

    @Mock
    private ProgramSubscriptionClient programSubscriptionClient;

    @Mock
    private DealerProgramConvertor dealerProgramConvertor;

    @InjectMocks
    private DealerProgramApiController dealerProgramApiController;

    @Test
    void testGetDealerPrograms_ReturnsOneProgram_WhenOneStatusNotCancelled() {
        final List<ProgramSubscriptionView> programSubscriptions = buildListOfProgramSubscriptionView(DealerStatus.LIVE);
        CollectionModel<ProgramSubscriptionView> dealerPrograms = CollectionModel.of(programSubscriptions);
        when(programSubscriptionClient.findByDealer(anyString())).thenReturn(dealerPrograms);
        ResponseEntity<List<DealerProgram>> result = dealerProgramApiController.getDealerPrograms(DEALER_X_ID);
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(1, result.getBody().size());
    }

    @Test
    void testGetDealerPrograms_ReturnsZeroProgram_WhenOneStatusCancelled() {
        final List<ProgramSubscriptionView> programSubscriptions = buildListOfProgramSubscriptionView(DealerStatus.CANCELLED);
        CollectionModel<ProgramSubscriptionView> dealerPrograms = CollectionModel.of(programSubscriptions);
        when(programSubscriptionClient.findByDealer(anyString())).thenReturn(dealerPrograms);
        ResponseEntity<List<DealerProgram>> result = dealerProgramApiController.getDealerPrograms(DEALER_X_ID);
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(0, result.getBody().size());
    }

    @Test
    void updateProgramConfig() {
    }

    @Test
    void convert() {
    }

    @Test
    void doConvert() {
    }

    @Test
    void testFetchBuyAtHomeProgram_WhenFoundAndFeatureToggleIsEnabled() {
        ReflectionTestUtils.setField(dealerProgramApiController, "buyAtHomeProgramId", PROGRAM_ID);
        ReflectionTestUtils.setField(dealerProgramApiController, "isBuyAtHomeProgramEnabled", true);
        List<ProgramSubscriptionView> view = buildListOfProgramSubscriptionViews();
        when(programSubscriptionClient.findByProgramAndDealer(PROGRAM_ID, DEALER_X_ID)).thenReturn(view.stream().findAny());
        var result = dealerProgramApiController.fetchBuyAtHomeProgram(DEALER_X_ID);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(true, result.getBody().isExists());
    }

    @Test
    void testFetchBuyAtHomeProgram_WhenNotFoundAndFeatureToggleIsEnabled() {
        ReflectionTestUtils.setField(dealerProgramApiController, "buyAtHomeProgramId", PROGRAM_ID);
        ReflectionTestUtils.setField(dealerProgramApiController, "isBuyAtHomeProgramEnabled", true);
        var result = dealerProgramApiController.fetchBuyAtHomeProgram(DEALER_X_ID);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(false, result.getBody().isExists());
    }

    @Test
    void testFetchBuyAtHomeProgram_WhenFeatureToggleIsDisabled() {
        ReflectionTestUtils.setField(dealerProgramApiController, "buyAtHomeProgramId", PROGRAM_ID);
        ReflectionTestUtils.setField(dealerProgramApiController, "isBuyAtHomeProgramEnabled", false);
        var result = dealerProgramApiController.fetchBuyAtHomeProgram(DEALER_X_ID);
        assertEquals(HttpStatus.BAD_REQUEST, result.getStatusCode());
        assertEquals(false, result.getBody().isExists());
    }

    private List<ProgramSubscriptionView> buildListOfProgramSubscriptionView(DealerStatus status) {
        DateTimeFormatter formatter = buildDateTimeFormatter();
        ProgramSubscriptionView view = buildProgramSubscriptionView(status, formatter);
        List<ProgramSubscriptionView> results = List.of(view);

        return results;
    }

    private List<ProgramSubscriptionView> buildListOfProgramSubscriptionViews() {

        DateTimeFormatter formatter = buildDateTimeFormatter();

        ProgramSubscriptionView view = new ProgramSubscriptionView();

        view.setId("d9ee362b-c5ff-4d6f-8075-b2a1247d6970");
        view.setDealerId(DEALER_X_ID);
        view.setProgramId("5e922fe4-e1e9-468c-b100-5b8f7cffcef3");
        view.setContractId(204654L);
        view.setStatus(DealerStatus.LIVE);
        view.setCreatedBy("ef00d55a-4434-47c6-a8c5-8884f942b3fa");
        view.setCreatedDate(ZonedDateTime.parse("2021-01-20 22:11:11.971742 +00:00", formatter));
        view.setLastModifiedBy("ad382bfe-55b7-40a4-bb21-ce226c27c62a");
        view.setLastModifiedDate(ZonedDateTime.parse("2021-06-07 15:40:06.687560 +00:00", formatter));
        view.setEnrollmentDate(null);
        view.setActivationDate(null);
        view.setCancellationDate(null);
        view.setConfig(null);
        view.setSuccessManagerId("cc038300-476d-42fc-9fc0-16d500366ae8");
        view.setAccountManagerId("abc8babb-57aa-4579-99a4-1ac871e42a38");
        view.setParticipationAgreementExecutedDate(null);
        view.setVendorNotifiedDate(null);
        view.setVendorAgreementToDealerDate(null);
        view.setIntegrationSurveyStartedDate(null);
        view.setTrainingNotificationSentDate(null);
        view.setTrainingNotificationCompletedDate(null);

        List<ProgramSubscriptionView> results = List.of(view);

        return results;
    }

    private ProgramSubscriptionView buildProgramSubscriptionView(DealerStatus status, DateTimeFormatter formatter) {
        ProgramSubscriptionView view = new ProgramSubscriptionView();
        view.setId("d9ee362b-c5ff-4d6f-8075-b2a1247d6970");
        view.setDealerId(DEALER_X_ID);
        view.setProgramId("5e922fe4-e1e9-468c-b100-5b8f7cffcef3");
        view.setContractId(204654L);
        view.setStatus(status);
        view.setCreatedBy("ef00d55a-4434-47c6-a8c5-8884f942b3fa");
        view.setCreatedDate(ZonedDateTime.parse("2021-01-20 22:11:11.971742 +00:00", formatter));
        view.setLastModifiedBy("ad382bfe-55b7-40a4-bb21-ce226c27c62a");
        view.setLastModifiedDate(ZonedDateTime.parse("2021-06-07 15:40:06.687560 +00:00", formatter));
        view.setEnrollmentDate(null);
        view.setActivationDate(null);
        view.setCancellationDate(null);
        view.setConfig(null);
        view.setSuccessManagerId("cc038300-476d-42fc-9fc0-16d500366ae8");
        view.setAccountManagerId("abc8babb-57aa-4579-99a4-1ac871e42a38");
        view.setParticipationAgreementExecutedDate(null);
        view.setVendorNotifiedDate(null);
        view.setVendorAgreementToDealerDate(null);
        view.setIntegrationSurveyStartedDate(null);
        view.setTrainingNotificationSentDate(null);
        view.setTrainingNotificationCompletedDate(null);

        return view;
    }

    private DateTimeFormatter buildDateTimeFormatter() {
        String dataGripDateTimePattern = "yyyy-MM-dd HH:mm:ss.SSSSSS xxx";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dataGripDateTimePattern);

        return formatter;
    }

    private DealerProgram buildDealerChatEnabledOnly() {
        DealerProgram result = DealerProgram.builder()
            .isChatEnabled(true)
            .build();

        return result;
    }

    private DealerProgram buildDealerProgram() {
        DealerProgram result = DealerProgram.builder()
            .name("Some Dealer Program")
            .status(DealerStatus.LIVE)
            .supportEmail(null)
            .supportPhone(null)
            .successManager(null)
            .accountManager(null)
            .build();

        return result;
    }

}
