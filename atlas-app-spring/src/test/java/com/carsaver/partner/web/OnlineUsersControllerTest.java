package com.carsaver.partner.web;

import com.carsaver.partner.model.user.OnlineUser;
import com.carsaver.partner.service.OnlineUsersService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class OnlineUsersControllerTest {

    private MockMvc mockMvc;

    @Mock
    private OnlineUsersService onlineUsersService;

    @InjectMocks

    private OnlineUsersController onlineUsersController;

    @BeforeEach
    void setup() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(onlineUsersController).build();
    }


    @Test
    void testGetOnlineUsers() throws Exception {
        OnlineUser onlineUser = OnlineUser.builder()
            .userId("user1")
            .firstName("John")
            .lastName("Doe")
            .referrerDomain("https://nissan-digital-retail.beta.carsaver.com")
            .minutesOnline(30L)
            .build();

        List<OnlineUser> mockOnlineUsers = Arrays.asList(onlineUser);

        when(onlineUsersService.getOnlineUsers()).thenReturn(mockOnlineUsers);

        mockMvc.perform(get("/api/online-users")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$", hasSize(1)))
            .andExpect(jsonPath("$[0].userId", is("user1")))
            .andExpect(jsonPath("$[0].firstName", is("John")))
            .andExpect(jsonPath("$[0].lastName", is("Doe")))
            .andExpect(jsonPath("$[0].referrerDomain", is("https://nissan-digital-retail.beta.carsaver.com")))
            .andExpect(jsonPath("$[0].minutesOnline", is(30)))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$", hasSize(1)))
            .andExpect(jsonPath("$[0].userId", is("user1")))
            .andExpect(jsonPath("$[0].firstName", is("John")))
            .andExpect(jsonPath("$[0].lastName", is("Doe")))
            .andExpect(jsonPath("$[0].referrerDomain", is("https://nissan-digital-retail.beta.carsaver.com")))
            .andExpect(jsonPath("$[0].minutesOnline", is(30)));
    }

    @Test
    void testGetOnlineUsers_NoUsersFound() throws Exception {
        when(onlineUsersService.getOnlineUsers()).thenReturn(Arrays.asList());

        mockMvc.perform(get("/api/online-users")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$", hasSize(0)));
    }

    @Test
    void testGetOnlineUsersByDealer() throws Exception {
        String dealerId = "dealer123";

        OnlineUser onlineUser = OnlineUser.builder()
            .userId("user1")
            .firstName("John")
            .lastName("Doe")
            .referrerDomain("https://nissan-digital-retail.beta.carsaver.com")
            .minutesOnline(15L)
            .build();

        List<OnlineUser> mockOnlineUsers = Arrays.asList(onlineUser);

        when(onlineUsersService.getOnlineUsersByDealer(dealerId)).thenReturn(mockOnlineUsers);

        mockMvc.perform(get("/api/online-users/dealers/"+dealerId)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$", hasSize(1)))
            .andExpect(jsonPath("$[0].userId", is("user1")))
            .andExpect(jsonPath("$[0].firstName", is("John")))
            .andExpect(jsonPath("$[0].lastName", is("Doe")))
            .andExpect(jsonPath("$[0].referrerDomain", is("https://nissan-digital-retail.beta.carsaver.com")))
            .andExpect(jsonPath("$[0].minutesOnline", is(15)));

        verify(onlineUsersService, times(1)).getOnlineUsersByDealer(dealerId);
    }

}
