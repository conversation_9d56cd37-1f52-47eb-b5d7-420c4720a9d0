package com.carsaver.partner.web;

import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.elasticsearch.model.LeadDoc;
import com.carsaver.elasticsearch.model.UserDoc;
import com.carsaver.elasticsearch.model.program.ProgramDoc;
import com.carsaver.elasticsearch.service.LeadDocService;
import com.carsaver.magellan.model.BaseLeadView;
import com.carsaver.magellan.model.ConnectionView;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Collection;
import java.util.List;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ExternalLeadRedirectControllerTest {


    private final LeadDocService leadDocService;
    private final ExternalLeadRedirectController leadClaimRedirectController;

    public ExternalLeadRedirectControllerTest() {
        this.leadDocService = mock(LeadDocService.class);
        this.leadClaimRedirectController = new ExternalLeadRedirectController(leadDocService);
    }

    @BeforeEach
    void setUp() {
        SecurityContextHolder.clearContext();
    }

    @AfterEach
    void tearDown() {
        SecurityContextHolder.clearContext();
    }

    @Test
    void leadClaim() {
        String LEAD_ID = "LEAD_ID";
        String DEALER_ID = "DEALER_ID";
        String expected = String.format("redirect:/leads/%s/claim?dealerIds=%s", LEAD_ID, DEALER_ID);

        BaseLeadView baseLeadView = new ConnectionView();
        baseLeadView.setId(LEAD_ID);
        baseLeadView.setDealerId(DEALER_ID);

        String result = leadClaimRedirectController.leadClaim(baseLeadView);
        Assertions.assertEquals(expected, result);
    }

    @Test
    void leadClaimNullTest() {
        String expected = "redirect:/";
        String result = leadClaimRedirectController.leadClaim(null);
        Assertions.assertEquals(expected, result);
    }

    @Test
    void adfRedirectLead_leadNotFound_test() {
        Mockito.when(leadDocService.findById(eq("NOT_FOUND_LEAD"))).thenReturn(null);
        String redirectEndpoint = leadClaimRedirectController.adfRedirectLead("NOT_FOUND_LEAD");
        Assertions.assertNotNull(redirectEndpoint);
        Assertions.assertEquals("redirect:/customers",redirectEndpoint);
    }

    @Test
    void adfRedirectLead_leadFound_test() {
        Mockito.when(leadDocService.findById(eq("FOUND_LEAD"))).thenReturn(LeadDoc.builder()
                .user(UserDoc.builder().id("USER_ID").build())
                .dealer(DealerDoc.builder().id("DEALER_ID").build())
            .build());

        // Test ROLE_DEALER scenario
        SecurityContext securityContext = mock(SecurityContext.class);
        Authentication authentication = mock(Authentication.class);
        SecurityContextHolder.setContext(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getAuthorities()).thenReturn((Collection) List.of(new SimpleGrantedAuthority("ROLE_DEALER")));
        
        String redirectEndpointForDealerUser = leadClaimRedirectController.adfRedirectLead("FOUND_LEAD");
        Assertions.assertNotNull(redirectEndpointForDealerUser);
        Assertions.assertEquals("redirect:/customers/USER_ID?dealerIds=DEALER_ID",redirectEndpointForDealerUser);

        // Test ROLE_PROGRAM scenario
        SecurityContext programSecurityContext = mock(SecurityContext.class);
        Authentication programAuthentication = mock(Authentication.class);
        SecurityContextHolder.setContext(programSecurityContext);
        when(programSecurityContext.getAuthentication()).thenReturn(programAuthentication);
        when(programAuthentication.getAuthorities()).thenReturn((Collection) List.of(new SimpleGrantedAuthority("ROLE_PROGRAM")));
        
        String redirectEndpointForProgramUser = leadClaimRedirectController.adfRedirectLead("FOUND_LEAD");
        Assertions.assertNotNull(redirectEndpointForProgramUser);
        Assertions.assertEquals("redirect:/customers/USER_ID?dealerIds=DEALER_ID&programIds=null",redirectEndpointForProgramUser);
    }


    @Test
    void adfRedirectLead_leadFoundWithProgramId_test() {
        Mockito.when(leadDocService.findById(eq("FOUND_LEAD"))).thenReturn(LeadDoc.builder()
                                                                                  .user(UserDoc.builder().id("USER_ID").program(ProgramDoc.builder().id("PROGRAM_ID").build()).build())
                                                                                  .dealer(DealerDoc.builder().id("DEALER_ID").build())
                                                                                  .build());

        // Test ROLE_DEALER scenario
        SecurityContext securityContext = mock(SecurityContext.class);
        Authentication authentication = mock(Authentication.class);
        SecurityContextHolder.setContext(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getAuthorities()).thenReturn((Collection) List.of(new SimpleGrantedAuthority("ROLE_DEALER")));
        
        String redirectEndpointForDealerUser = leadClaimRedirectController.adfRedirectLead("FOUND_LEAD");
        Assertions.assertNotNull(redirectEndpointForDealerUser);
        Assertions.assertEquals("redirect:/customers/USER_ID?dealerIds=DEALER_ID",redirectEndpointForDealerUser);

        // Test ROLE_PROGRAM scenario
        SecurityContext programSecurityContext = mock(SecurityContext.class);
        Authentication programAuthentication = mock(Authentication.class);
        SecurityContextHolder.setContext(programSecurityContext);
        when(programSecurityContext.getAuthentication()).thenReturn(programAuthentication);
        when(programAuthentication.getAuthorities()).thenReturn((Collection) List.of(new SimpleGrantedAuthority("ROLE_PROGRAM")));
        
        String redirectEndpointForProgramUser = leadClaimRedirectController.adfRedirectLead("FOUND_LEAD");
        Assertions.assertNotNull(redirectEndpointForProgramUser);
        Assertions.assertEquals("redirect:/customers/USER_ID?dealerIds=DEALER_ID&programIds=PROGRAM_ID",redirectEndpointForProgramUser);
    }
}
