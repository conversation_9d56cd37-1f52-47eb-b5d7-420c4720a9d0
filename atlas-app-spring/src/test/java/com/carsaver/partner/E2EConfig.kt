package com.carsaver.partner

import com.carsaver.atc.client.AtcTaxesClient
import com.carsaver.elasticsearch.ElasticClient
import com.carsaver.elasticsearch.ElasticProperties
import com.carsaver.elasticsearch.service.LeadDocService
import com.carsaver.elasticsearch.service.UserAndProspectDocService
import com.carsaver.elasticsearch.service.UserContractDocService
import com.carsaver.elasticsearch.service.UserDocService
import com.carsaver.elasticsearch.service.VehicleDocService
import com.carsaver.magellan.api.TwilioService
import com.carsaver.magellan.api.UserService
import com.carsaver.magellan.api.deal.DealSheetService
import com.carsaver.magellan.client.CampaignClient
import com.carsaver.magellan.client.CertificateClient
import com.carsaver.magellan.client.ZipCodeClient
import com.carsaver.magellan.offers.quote.LeaseQuoteService
import com.carsaver.partner.client.FeatureSubscriptionsClient
import com.carsaver.partner.client.ProgramSubscriptionsClient
import com.carsaver.partner.client.UserDocumentsClientV2
import com.carsaver.partner.client.adf.AdfClient
import com.carsaver.partner.client.bigquery.BigQueryClient
import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.client.customer.CustomerDealSummaryClient
import com.carsaver.partner.client.customer.CustomerTagsClient
import com.carsaver.partner.client.dealer.DealerClient
import com.carsaver.partner.client.finance.FinanceServiceClient
import com.carsaver.partner.client.inventory.VehicleForProgramsClient
import com.carsaver.partner.client.inventory.VehiclePricingClient
import com.carsaver.partner.client.inventory.v2.InventoryClientV2
import com.carsaver.partner.client.leads.v2.LeadClientV2
import com.carsaver.partner.client.nissan.NissanWebClient
import com.carsaver.partner.client.oauth.OAuthClient
import com.carsaver.partner.client.vehicle.VehicleClientV2
import com.carsaver.partner.config.NwanConfig
import com.carsaver.partner.config.TwilioConfig
import com.carsaver.partner.configuration.ConfigurationAuditService
import com.carsaver.partner.configuration.ConfigurationBuilderService
import com.carsaver.partner.configuration.ConfigurationClient
import com.carsaver.partner.configuration.lender.LenderPreferencesService
import com.carsaver.partner.converter.DRLoanRequestViewToUserLoanRequestConverter
import com.carsaver.partner.converter.LoanRequestViewToUserLoanRequestConverter
import com.carsaver.partner.converter.StringToBaseLeadViewConverter
import com.carsaver.partner.converter.UserVehicleViewToPayoffQuoteConverter
import com.carsaver.partner.customer.CustomerDetailsLinkService
import com.carsaver.partner.digitalretailotp.DigitalRetailOtpService
import com.carsaver.partner.elasticsearch.DealerDocService
import com.carsaver.partner.elasticsearch.VehicleSaleDocService
import com.carsaver.partner.extension.ChromeExtensionUsersIndexClient
import com.carsaver.partner.filter.AtlasContextFilter
import com.carsaver.partner.http.HttpService
import com.carsaver.partner.inshowroom.InShowRoomClient
import com.carsaver.partner.model.AtlasContext
import com.carsaver.partner.notes.NotesClient
import com.carsaver.partner.notes.NotesService
import com.carsaver.partner.notifications.NotificationClient
import com.carsaver.partner.prequalification.PreQualHistoryRepo
import com.carsaver.partner.reporting.elasticsearch.service.DealReportingService
import com.carsaver.partner.reporting.elasticsearch.service.LeadReportingService
import com.carsaver.partner.reporting.elasticsearch.service.ModelViewReportingService
import com.carsaver.partner.reporting.elasticsearch.service.ModuleUsageFinanceReportingService
import com.carsaver.partner.reporting.elasticsearch.service.ModuleUsageReportingService
import com.carsaver.partner.reporting.elasticsearch.service.UserReportingService
import com.carsaver.partner.reporting.elasticsearch.service.VehicleSaleReportingService
import com.carsaver.partner.reporting.service.ActiveUserService
import com.carsaver.partner.reporting.service.GoogleAnalyticsReportingService
import com.carsaver.partner.reporting.service.KeyMetricsService
import com.carsaver.partner.reporting.service.LenderBreakdownReportingService
import com.carsaver.partner.reporting.service.ProgramService
import com.carsaver.partner.reporting.service.TradeReportingService
import com.carsaver.partner.search.converter.DMATermFacetConverter
import com.carsaver.partner.search.converter.DealerGroupTermFacetConverter
import com.carsaver.partner.search.converter.DealerTermFacetConverter
import com.carsaver.partner.search.converter.ProgramTermFacetConverter
import com.carsaver.partner.search.converter.UserTermFacetConverter
import com.carsaver.partner.service.AccessoriesService
import com.carsaver.partner.service.AtlasActivityEventService
import com.carsaver.partner.service.AtlasDealerAccessoriesService
import com.carsaver.partner.service.CampaignService
import com.carsaver.partner.service.CustomerDealSummaryService
import com.carsaver.partner.service.CustomerTagService
import com.carsaver.partner.service.DashboardLeadDocService
import com.carsaver.partner.service.DashboardSalesDocService
import com.carsaver.partner.service.DashboardVehicleDocService
import com.carsaver.partner.service.DealModelService
import com.carsaver.partner.service.DealerCTAService
import com.carsaver.partner.service.DealerDeliveryFeeService
import com.carsaver.partner.service.DealerEntryPointsService
import com.carsaver.partner.service.DealerFeesService
import com.carsaver.partner.service.DealerMetricsService
import com.carsaver.partner.service.DealerProgramConvertor
import com.carsaver.partner.service.DealerProgramFeaturesService
import com.carsaver.partner.service.DealerProgramURLConvertor
import com.carsaver.partner.service.DealerUserService
import com.carsaver.partner.service.DealerValidationService
import com.carsaver.partner.service.DomoReportService
import com.carsaver.partner.service.FastPassService
import com.carsaver.partner.service.FinancierUtilsService
import com.carsaver.partner.service.InShowroomUsersService
import com.carsaver.partner.service.LenderDeskService
import com.carsaver.partner.service.LoggedInUserProgramService
import com.carsaver.partner.service.MailService
import com.carsaver.partner.service.MixpanelClient
import com.carsaver.partner.service.NissanAccessoriesService
import com.carsaver.partner.service.NotificationService
import com.carsaver.partner.service.OnlineUsersService
import com.carsaver.partner.service.PortalAutoLoginService
import com.carsaver.partner.service.ProgramApiService
import com.carsaver.partner.service.ProspectService
import com.carsaver.partner.service.QuickLinksService
import com.carsaver.partner.service.ReceiptService
import com.carsaver.partner.service.SmsValidationService
import com.carsaver.partner.service.UserAdfService
import com.carsaver.partner.service.UserDealerAssociationService
import com.carsaver.partner.service.UserDocumentsService
import com.carsaver.partner.service.UserVehicleService
import com.carsaver.partner.service.VehiclePricingService
import com.carsaver.partner.service.adf.AdfService
import com.carsaver.partner.service.desking.CloneDealService
import com.carsaver.partner.service.desking.CopyDealService
import com.carsaver.partner.service.desking.CreateDealService
import com.carsaver.partner.service.desking.DealNotesService
import com.carsaver.partner.service.desking.standard.CloneRequestDataToDealJacketConverter
import com.carsaver.partner.service.desking.standard.ClonedResponseConverter
import com.carsaver.partner.service.desking.standard.LowestDealService
import com.carsaver.partner.service.desking.standard.UpdateDealService
import com.carsaver.partner.service.desking.standard.lease.QuoteServiceClient
import com.carsaver.partner.service.desking.standard.paas.ModelConverter
import com.carsaver.partner.service.desking.standard.paas.PaasDealService
import com.carsaver.partner.service.desking.standard.paas.PaasRequestBuilder
import com.carsaver.partner.service.desking.standard.paas.PaasServiceClient
import com.carsaver.partner.service.nesna.DealerNesnaProductsService
import com.carsaver.partner.service.nesna.NesnaProtectionProductsService
import com.carsaver.partner.service.protection_products.ProtectionPlansRouteOneService
import com.carsaver.partner.service.protection_products.ProtectionProductsRequestService
import com.carsaver.partner.service.protection_products.ProtectionProductsService
import com.carsaver.partner.service.protection_products.ProtectionProductsServiceSafety
import com.carsaver.partner.service.reservations.ReservationService
import com.carsaver.partner.service.reservations.nissan.NissanReservationService
import com.carsaver.partner.service.return_policy.ReturnPolicyService
import com.carsaver.partner.service.routeone.RouteOneOTPService
import com.carsaver.partner.service.sellathome.UserVehicleSellInfoService
import com.carsaver.partner.service.split_io.SplitFeatureFlags
import com.carsaver.partner.service.sso.SsoService
import com.carsaver.partner.service.toggle.CarsaverFAndIFeatureToggleHandler
import com.carsaver.partner.service.toggle.ChatFeatureToggleHandler
import com.carsaver.partner.service.toggle.DisableProtectionProductsFeatureToggleHandler
import com.carsaver.partner.service.toggle.EmailAlertsFeatureToggleHandler
import com.carsaver.partner.service.toggle.GarageAlertsFeatureToggleHandler
import com.carsaver.partner.service.toggle.InAppAlertsFeatureToggleHandler
import com.carsaver.partner.service.toggle.LibertyMutualFeatureToggleHandler
import com.carsaver.partner.service.toggle.NesnaFinanceAndInsuranceFeatureToggleHandler
import com.carsaver.partner.service.toggle.RouteOneFinanceAndInsuranceFeatureToggleHandler
import com.carsaver.partner.service.toggle.SellAtHomeFeatureToggleHandler
import com.carsaver.partner.service.toggle.SmsAlertsFeatureToggleHandler
import com.carsaver.partner.service.toggle.SpanishTranslationFeatureToggleHandler
import com.carsaver.partner.service.toggle.factory.FeatureToggleHandlerFactory
import com.carsaver.partner.service.trade.DealerTradeQuoteService
import com.carsaver.partner.service.vehiclesearches.VehicleSearchesService
import com.carsaver.partner.service.warranty.WarrantyMailService
import com.carsaver.partner.service.warranty.WarrantyService
import com.carsaver.partner.service.warranty.WarrantyUserService
import com.carsaver.partner.support.AtlasContextProvider
import com.carsaver.partner.support.DataSanitizationService
import com.carsaver.partner.support.SanitizationAspect
import com.carsaver.partner.web.api.DealApiController
import com.carsaver.partner.web.api.DealerApiController
import com.carsaver.partner.web.api.DealerProgramApiController
import com.carsaver.partner.web.api.SecurityHelperService
import com.carsaver.partner.web.api.trade_in_adjustment.TradeInAdjustmentService
import com.carsaver.partner.web.api.user.UserDealsApiController
import com.carsaver.partner.web.api.user.UserLeadService
import com.carsaver.partner.web.api.user.elasticsearch.CustomersDocService
import com.carsaver.partner.web.api.user.elasticsearch.DealerAndProgramAccessHelper
import com.carsaver.partner.web.api.user.elasticsearch.DealerUserElasticSearchService
import com.carsaver.partner.web.api.user.elasticsearch.ExportService
import com.carsaver.partner.web.api.user.elasticsearch.LocaleService
import com.carsaver.partner.web.api.user.elasticsearch.ProgramUserElasticSearchService
import com.carsaver.partner.web.api.user.elasticsearch.SaleStagesStatsService
import com.carsaver.partner.web.api.user.elasticsearch.TraitsService
import com.carsaver.partner.web.api.warranty.WarrantyControllerHelper
import com.carsaver.partner.web.support.DealerShareSupport
import com.carsaver.search.support.FacetParser
import com.carsaver.split.io.client.SplitClientFacade
import com.carsaver.warranty.config.NwanProps
import com.carsaver.warranty.service.NwanClient
import org.mockito.Mockito
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.core.Ordered
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import javax.servlet.FilterChain
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

@Configuration
@Profile("e2e")
@EnableWebSecurity
open class E2ESecurityConfig : WebSecurityConfigurerAdapter() {

    override fun configure(http: HttpSecurity) {
        http
            .csrf().disable()
            .authorizeRequests()
            .anyRequest().permitAll()
    }

    @Primary
    @Bean
    open fun testAtlasContextFilter(): FilterRegistrationBean<AtlasContextFilter> {
        val registrationBean = FilterRegistrationBean<AtlasContextFilter>()

        val mockFilter = object : AtlasContextFilter() {
            override fun doFilterInternal(
                request: HttpServletRequest,
                response: HttpServletResponse,
                filterChain: FilterChain
            ) {
                // Create and populate a test AtlasContext
                val atlasContext = AtlasContext()
                atlasContext.firstName = "Test"
                atlasContext.lastName = "User"
                atlasContext.email = "<EMAIL>"
                atlasContext.userId = "test-user-id"

                request.setAttribute(AtlasContext.KEY, atlasContext)

                filterChain.doFilter(request, response)
            }
        }

        registrationBean.filter = mockFilter
        registrationBean.order = Ordered.HIGHEST_PRECEDENCE  // Make sure this context filter runs first
        registrationBean.addUrlPatterns("/*")

        return registrationBean
    }
}

inline fun <reified T> mock(): T = Mockito.mock(T::class.java)

@Configuration
@Profile("e2e")
open class E2EMockBeansConfig {

    // @Beans that return mocks
    // These are real @Bean implementations that return a Mock. We can't override MockBeans so we will override this instead in
    // whichever E2E test class implements a test container. This is to avoid instantiating test containers for test instances that don't need it
    // For most classes that just need to be mocked, @MockBean is fine. For E2E, exclude the production bean and implement this one as a test container in the test itself
    @Bean
    open fun preQualHistoryRepo(): PreQualHistoryRepo = mock()


    // @MockBeans
    @MockBean
    private lateinit var zipCodeClient: ZipCodeClient
    @MockBean
    private lateinit var atcTaxesClient: AtcTaxesClient
    @MockBean
    private lateinit var nwanClient: NwanClient
    @MockBean
    private lateinit var nwanProps: NwanProps
    @MockBean
    private lateinit var userDocumentsClientV2: UserDocumentsClientV2
    @MockBean
    private lateinit var adfClient: AdfClient
    @MockBean
    private lateinit var customerDealSummaryClient: CustomerDealSummaryClient
    @MockBean
    private lateinit var customerTagsClient: CustomerTagsClient
    @MockBean
    private lateinit var financeServiceClient: FinanceServiceClient
    @MockBean
    private lateinit var vehicleForProgramsClient: VehicleForProgramsClient
    @MockBean
    private lateinit var vehiclePricingClient: VehiclePricingClient
    @MockBean
    private lateinit var nissanWebClient: NissanWebClient
    @MockBean
    private lateinit var oAuthClient: OAuthClient
    @MockBean
    private lateinit var inShowRoomClient: InShowRoomClient
    @MockBean
    private lateinit var notesClient: NotesClient
    @MockBean
    private lateinit var notificationClient: NotificationClient
    @MockBean
    private lateinit var cloneDealClient: VehicleDocService
    @MockBean
    private lateinit var quoteServiceClient: QuoteServiceClient
    @MockBean
    private lateinit var paasServiceClient: PaasServiceClient

    // External clients
    @MockBean
    private lateinit var featureSubscriptionsClient: FeatureSubscriptionsClient
    @MockBean
    private lateinit var configService: ConfigServiceClient
    @MockBean
    private lateinit var programSubClient: ProgramSubscriptionsClient
    @MockBean
    private lateinit var configClient: ConfigurationClient
    @MockBean
    private lateinit var inventoryClientV2: InventoryClientV2
    @MockBean
    private lateinit var leadClientV2: LeadClientV2
    @MockBean
    private lateinit var vehicleClientV2: VehicleClientV2
    @MockBean
    private lateinit var facade: SplitClientFacade
    @MockBean
    private lateinit var certificateClient: CertificateClient
    @MockBean
    private lateinit var dealerClient: DealerClient
    @MockBean
    private lateinit var campaignClient: CampaignClient
    @MockBean
    private lateinit var bigQueryClient: BigQueryClient
    @MockBean
    private lateinit var mixpanelClient: MixpanelClient

    // Dynamo
    @MockBean
    private lateinit var dynamoClient: DynamoDbClient
    @MockBean
    private lateinit var chromeExtensionUsersIndexClient: ChromeExtensionUsersIndexClient

    // ElasticClient beans
    @MockBean
    @Qualifier("vega")
    private lateinit var elasticSearchClient: ElasticClient
    @MockBean
    @Qualifier("gibson")
    private lateinit var elasticSearchGibsonClient: ElasticClient
    @MockBean
    @Qualifier("nova")
    private lateinit var elasticSearchNovaClient: ElasticClient

    // ElasticProperties beans
    @MockBean
    @Qualifier("es-gibson-props")
    private lateinit var elasticGibsonProperties: ElasticProperties
    @MockBean
    @Qualifier("es-nova-props")
    private lateinit var elasticNovaProperties: ElasticProperties
    @MockBean
    @Qualifier("es-inventory-props")
    private lateinit var elasticProperties: ElasticProperties

    // Configuration services
    @MockBean
    private lateinit var configurationAuditService: ConfigurationAuditService
    @MockBean
    private lateinit var configurationBuilderService: ConfigurationBuilderService
    @MockBean
    private lateinit var nwanConfig: NwanConfig
    @MockBean
    private lateinit var twilioConfig: TwilioConfig

    // Converters
    @MockBean
    private lateinit var drLoanRequestViewToUserLoanRequestConverter: DRLoanRequestViewToUserLoanRequestConverter
    @MockBean
    private lateinit var loanRequestViewToUserLoanRequestConverter: LoanRequestViewToUserLoanRequestConverter
    @MockBean
    private lateinit var stringToBaseLeadViewConverter: StringToBaseLeadViewConverter
    @MockBean
    private lateinit var userVehicleViewToPayoffQuoteConverter: UserVehicleViewToPayoffQuoteConverter
    @MockBean
    private lateinit var cloneRequestDataToDealJacketConverter: CloneRequestDataToDealJacketConverter
    @MockBean
    private lateinit var clonedResponseConverter: ClonedResponseConverter
    @MockBean
    private lateinit var modelConverter: ModelConverter
    @MockBean
    private lateinit var paasRequestBuilder: PaasRequestBuilder
    @MockBean
    private lateinit var dmaTermFacetConverter: DMATermFacetConverter
    @MockBean
    private lateinit var dealerGroupTermFacetConverter: DealerGroupTermFacetConverter
    @MockBean
    private lateinit var dealerTermFacetConverter: DealerTermFacetConverter
    @MockBean
    private lateinit var programTermFacetConverter: ProgramTermFacetConverter
    @MockBean
    private lateinit var userTermFacetConverter: UserTermFacetConverter
    @MockBean
    private lateinit var dealerProgramConvertor: DealerProgramConvertor
    @MockBean
    private lateinit var dealerProgramURLConvertor: DealerProgramURLConvertor

    // Services
    @MockBean
    private lateinit var dealSheetService: DealSheetService
    @MockBean
    private lateinit var twilioService: TwilioService
    @MockBean
    private lateinit var userDealsApiController: UserDealsApiController
    @MockBean
    private lateinit var userService: UserService
    @MockBean
    private lateinit var dealApiController: DealApiController
    @MockBean
    private lateinit var dealerApiController: DealerApiController
    @MockBean
    private lateinit var dealerProgramApiController: DealerProgramApiController
    @MockBean
    private lateinit var facetParser: FacetParser
    @MockBean
    private lateinit var userDocService: UserDocService
    @MockBean
    private lateinit var userAndProspectDocService: UserAndProspectDocService
    @MockBean
    private lateinit var customersDocService: CustomersDocService
    @MockBean
    private lateinit var leadDocService: LeadDocService
    @MockBean
    private lateinit var userContractDocService: UserContractDocService
    @MockBean
    private lateinit var vehicleSaleDocService: VehicleSaleDocService
    @MockBean
    private lateinit var lenderPreferencesService: LenderPreferencesService
    @MockBean
    private lateinit var customerDetailsLinkService: CustomerDetailsLinkService
    @MockBean
    private lateinit var digitalRetailOtpService: DigitalRetailOtpService
    @MockBean
    private lateinit var dealerDocService: DealerDocService
    @MockBean
    private lateinit var httpService: HttpService
    @MockBean
    private lateinit var notesService: NotesService
    @MockBean
    private lateinit var dealReportingService: DealReportingService
    @MockBean
    private lateinit var leadReportingService: LeadReportingService
    @MockBean
    private lateinit var modelViewReportingService: ModelViewReportingService
    @MockBean
    private lateinit var moduleUsageFinanceReportingService: ModuleUsageFinanceReportingService
    @MockBean
    private lateinit var moduleUsageReportingService: ModuleUsageReportingService
    @MockBean
    private lateinit var userReportingService: UserReportingService
    @MockBean
    private lateinit var vehicleSaleReportingService: VehicleSaleReportingService
    @MockBean
    private lateinit var activeUserService: ActiveUserService
    @MockBean
    private lateinit var googleAnalyticsReportingService: GoogleAnalyticsReportingService
    @MockBean
    private lateinit var keyMetricsService: KeyMetricsService
    @MockBean
    private lateinit var lenderBreakdownReportingService: LenderBreakdownReportingService
    @MockBean
    private lateinit var programService: ProgramService
    @MockBean
    private lateinit var tradeReportingService: TradeReportingService
    @MockBean
    private lateinit var accessoriesService: AccessoriesService
    @MockBean
    private lateinit var atlasActivityEventService: AtlasActivityEventService
    @MockBean
    private lateinit var atlasDealerAccessoriesService: AtlasDealerAccessoriesService
    @MockBean
    private lateinit var campaignService: CampaignService
    @MockBean
    private lateinit var customerDealSummaryService: CustomerDealSummaryService
    @MockBean
    private lateinit var customerTagService: CustomerTagService
    @MockBean
    private lateinit var dashboardLeadDocService: DashboardLeadDocService
    @MockBean
    private lateinit var dashboardSalesDocService: DashboardSalesDocService
    @MockBean
    private lateinit var dashboardVehicleDocService: DashboardVehicleDocService
    @MockBean
    private lateinit var dealModelService: DealModelService
    @MockBean
    private lateinit var dealerCTAService: DealerCTAService
    @MockBean
    private lateinit var dealerDeliveryFeeService: DealerDeliveryFeeService
    @MockBean
    private lateinit var dealerEntryPointsService: DealerEntryPointsService
    @MockBean
    private lateinit var dealerFeesService: DealerFeesService
    @MockBean
    private lateinit var dealerMetricsService: DealerMetricsService
    @MockBean
    private lateinit var dealerProgramFeaturesService: DealerProgramFeaturesService
    @MockBean
    private lateinit var dealerUserService: DealerUserService
    @MockBean
    private lateinit var dealerValidationService: DealerValidationService
    @MockBean
    private lateinit var domoReportService: DomoReportService
    @MockBean
    private lateinit var fastPassService: FastPassService
    @MockBean
    private lateinit var financierUtilsService: FinancierUtilsService
    @MockBean
    private lateinit var inShowroomUsersService: InShowroomUsersService
    @MockBean
    private lateinit var lenderDeskService: LenderDeskService
    @MockBean
    private lateinit var loggedInUserProgramService: LoggedInUserProgramService
    @MockBean
    private lateinit var mailService: MailService
    @MockBean
    private lateinit var nissanAccessoriesService: NissanAccessoriesService
    @MockBean
    private lateinit var notificationService: NotificationService
    @MockBean
    private lateinit var onlineUsersService: OnlineUsersService
    @MockBean
    private lateinit var portalAutoLoginService: PortalAutoLoginService
    @MockBean
    private lateinit var programApiService: ProgramApiService
    @MockBean
    private lateinit var prospectService: ProspectService
    @MockBean
    private lateinit var quickLinksService: QuickLinksService
    @MockBean
    private lateinit var receiptService: ReceiptService
    @MockBean
    private lateinit var smsValidationService: SmsValidationService
    @MockBean
    private lateinit var userAdfService: UserAdfService
    @MockBean
    private lateinit var userDealerAssociationService: UserDealerAssociationService
    @MockBean
    private lateinit var userDocumentsService: UserDocumentsService
    @MockBean
    private lateinit var userVehicleService: UserVehicleService
    @MockBean
    private lateinit var vehiclePricingService: VehiclePricingService
    @MockBean
    private lateinit var adfService: AdfService
    @MockBean
    private lateinit var cloneDealService: CloneDealService
    @MockBean
    private lateinit var copyDealService: CopyDealService
    @MockBean
    private lateinit var createDealService: CreateDealService
    @MockBean
    private lateinit var dealNotesService: DealNotesService
    @MockBean
    private lateinit var lowestDealService: LowestDealService
    @MockBean
    private lateinit var updateDealService: UpdateDealService
    @MockBean
    private lateinit var leaseQuoteService: LeaseQuoteService
    @MockBean
    private lateinit var paasDealService: PaasDealService
    @MockBean
    private lateinit var dealerNesnaProductsService: DealerNesnaProductsService
    @MockBean
    private lateinit var nesnaProtectionProductsService: NesnaProtectionProductsService
    @MockBean
    private lateinit var protectionPlansRouteOneService: ProtectionPlansRouteOneService
    @MockBean
    private lateinit var protectionProductsRequestService: ProtectionProductsRequestService
    @MockBean
    private lateinit var protectionProductsService: ProtectionProductsService
    @MockBean
    private lateinit var protectionProductsServiceSafety: ProtectionProductsServiceSafety
    @MockBean
    private lateinit var reservationService: ReservationService
    @MockBean
    private lateinit var nissanReservationService: NissanReservationService
    @MockBean
    private lateinit var returnPolicyService: ReturnPolicyService
    @MockBean
    private lateinit var routeOneOTPService: RouteOneOTPService
    @MockBean
    private lateinit var userVehicleSellInfoService: UserVehicleSellInfoService
    @MockBean
    private lateinit var splitFeatureFlags: SplitFeatureFlags
    @MockBean
    private lateinit var ssoService: SsoService
    @MockBean
    private lateinit var carsaverFAndIFeatureToggleHandler: CarsaverFAndIFeatureToggleHandler
    @MockBean
    private lateinit var chatFeatureToggleHandler: ChatFeatureToggleHandler
    @MockBean
    private lateinit var disableProtectionProductsFeatureToggleHandler: DisableProtectionProductsFeatureToggleHandler
    @MockBean
    private lateinit var emailAlertsFeatureToggleHandler: EmailAlertsFeatureToggleHandler
    @MockBean
    private lateinit var garageAlertsFeatureToggleHandler: GarageAlertsFeatureToggleHandler
    @MockBean
    private lateinit var inAppAlertsFeatureToggleHandler: InAppAlertsFeatureToggleHandler
    @MockBean
    private lateinit var libertyMutualFeatureToggleHandler: LibertyMutualFeatureToggleHandler
    @MockBean
    private lateinit var nesnaFinanceAndInsuranceFeatureToggleHandler: NesnaFinanceAndInsuranceFeatureToggleHandler
    @MockBean
    private lateinit var routeOneFinanceAndInsuranceFeatureToggleHandler: RouteOneFinanceAndInsuranceFeatureToggleHandler
    @MockBean
    private lateinit var sellAtHomeFeatureToggleHandler: SellAtHomeFeatureToggleHandler
    @MockBean
    private lateinit var smsAlertsFeatureToggleHandler: SmsAlertsFeatureToggleHandler
    @MockBean
    private lateinit var spanishTranslationFeatureToggleHandler: SpanishTranslationFeatureToggleHandler
    @MockBean
    private lateinit var featureToggleHandlerFactory: FeatureToggleHandlerFactory
    @MockBean
    private lateinit var dealerTradeQuoteService: DealerTradeQuoteService
    @MockBean
    private lateinit var vehicleSearchesService: VehicleSearchesService
    @MockBean
    private lateinit var warrantyMailService: WarrantyMailService
    @MockBean
    private lateinit var warrantyService: WarrantyService
    @MockBean
    private lateinit var warrantyUserService: WarrantyUserService

    // Support classes
    @MockBean
    private lateinit var atlasContextProvider: AtlasContextProvider
    @MockBean
    private lateinit var dataSanitizationService: DataSanitizationService
    @MockBean
    private lateinit var sanitizationAspect: SanitizationAspect

    // Web API services
    @MockBean
    private lateinit var securityHelperService: SecurityHelperService
    @MockBean
    private lateinit var tradeInAdjustmentService: TradeInAdjustmentService
    @MockBean
    private lateinit var userLeadService: UserLeadService
    @MockBean
    private lateinit var dealerAndProgramAccessHelper: DealerAndProgramAccessHelper
    @MockBean
    private lateinit var dealerUserElasticSearchService: DealerUserElasticSearchService
    @MockBean
    private lateinit var exportService: ExportService
    @MockBean
    private lateinit var localeService: LocaleService
    @MockBean
    private lateinit var programUserElasticSearchService: ProgramUserElasticSearchService
    @MockBean
    private lateinit var saleStagesStatsService: SaleStagesStatsService
    @MockBean
    private lateinit var traitsService: TraitsService
    @MockBean
    private lateinit var warrantyControllerHelper: WarrantyControllerHelper
    @MockBean
    private lateinit var dealerShareSupport: DealerShareSupport
}

