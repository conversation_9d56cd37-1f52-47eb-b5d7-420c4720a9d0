import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.configuration.ratesheet.FallbackRateSheet
import com.carsaver.partner.configuration.ratesheet.FallbackRateSheet.Rate
import com.carsaver.partner.configuration.ratesheet.FallbackRateSheet.AgeRange
import com.carsaver.partner.configuration.ratesheet.FallbackRateSheet.Term
import com.carsaver.partner.configuration.ratesheet.RateSheetService
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verify
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class RateSheetServiceTest {

    private val configServiceClient: ConfigServiceClient = mock()

    private val rateSheetService: RateSheetService = RateSheetService(configServiceClient)

    @Test
    fun `getRateSheetConfiguration should call configServiceClient and transform response`() {
        // Given
        val dealerId = "123"
        val rateNew = Rate(
            ageRange = AgeRange(min = "0", max = "1"),
            stockTypeNew = true,
        )
        val rateUsed = Rate(
            ageRange = AgeRange(min = "2", max = "3"),
            stockTypeNew = false,
        )
        val term = Term(term = "60", rates = listOf(rateNew, rateUsed))
        val fallbackRateSheet = FallbackRateSheet(terms = listOf(term))

        whenever(configServiceClient.getRateSheet(dealerId, null)).thenReturn(fallbackRateSheet)

        // When
        val result = rateSheetService.getRateSheetConfiguration(dealerId)

        // Then
        verify(configServiceClient).getRateSheet(dealerId, null)

        assertNotNull(result)
        assertEquals("New", ((result["terms"] as List<*>).get(0) as Term).rates?.get(0)?.ageRange?.min)
        assertEquals("New", ((result["terms"] as List<*>).get(0) as Term).rates?.get(0)?.ageRange?.max)
        assertEquals(true, ((result["terms"] as List<*>).get(0) as Term).rates?.get(0)?.stockTypeNew)

        assertEquals("2", ((result["terms"] as List<*>).get(0) as Term).rates?.get(1)?.ageRange?.min)
        assertEquals("3", ((result["terms"] as List<*>).get(0) as Term).rates?.get(1)?.ageRange?.max)
        assertEquals(false, ((result["terms"] as List<*>).get(0) as Term).rates?.get(1)?.stockTypeNew)
    }

    @Test
    fun `getRateSheetConfiguration should handle null ageRange when stockTypeNew is true`() {
        // Given
        val dealerId = "456"
        val rateNewWithNullAgeRange = Rate(
            ageRange = null,
            stockTypeNew = true,
        )
        val term = Term(term = "72", rates = listOf(rateNewWithNullAgeRange))
        val fallbackRateSheet = FallbackRateSheet(terms = listOf(term))

        whenever(configServiceClient.getRateSheet(dealerId, null)).thenReturn(fallbackRateSheet)

        // When
        val result = rateSheetService.getRateSheetConfiguration(dealerId)

        // Then
        verify(configServiceClient).getRateSheet(dealerId, null)
        assertNotNull(result)
        assertEquals("New", ((result["terms"] as List<*>).get(0) as Term).rates?.get(0)?.ageRange?.min)
        assertEquals("New", ((result["terms"] as List<*>).get(0) as Term).rates?.get(0)?.ageRange?.max)
        assertEquals(true, ((result["terms"] as List<*>).get(0) as Term).rates?.get(0)?.stockTypeNew)
    }

    @Test
    fun `getRateSheetConfiguration should handle null terms`() {
        // Given
        val dealerId = "789"
        val fallbackRateSheet = FallbackRateSheet(terms = null)

        whenever(configServiceClient.getRateSheet(dealerId, null)).thenReturn(fallbackRateSheet)

        // When
        val result = rateSheetService.getRateSheetConfiguration(dealerId)

        // Then
        verify(configServiceClient).getRateSheet(dealerId, null)
        assertNotNull(result)
        assertNull(result["terms"])
    }

    @Test
    fun `getRateSheetConfiguration should handle null rates in a term`() {
        // Given
        val dealerId = "101"
        val termWithNullRates = Term(term = "36", rates = null)
        val fallbackRateSheet = FallbackRateSheet(terms = listOf(termWithNullRates))

        whenever(configServiceClient.getRateSheet(dealerId, null)).thenReturn(fallbackRateSheet)

        // When
        val result = rateSheetService.getRateSheetConfiguration(dealerId)

        // Then
        verify(configServiceClient).getRateSheet(dealerId, null)
        assertNotNull(result)
        assertEquals("36", ((result["terms"] as List<*>).get(0) as Term).term)
        // Check that no rate-specific keys are present
        assertEquals(1, (result["terms"]as List<*>).size)
    }

    @Test
    fun `saveRateSheetConfiguration should call configServiceClient and set stockTypeNew - MIN`() {
        // Given
        val dealerId = "123"
        val userId = "user1"
        val domain = "example.com"
        val rateNew = Rate(ageRange = FallbackRateSheet.AgeRange(min = "New", max = "1"), stockTypeNew = null)
        val rateUsed = Rate(ageRange = FallbackRateSheet.AgeRange(min = "0", max = "1"), stockTypeNew = null)
        val term = Term(rates = listOf(rateNew, rateUsed))
        val request = FallbackRateSheet(terms = listOf(term))

        // When
        rateSheetService.saveRateSheetConfiguration(dealerId, userId, domain, request)

        // Then
        verify(configServiceClient).saveRateSheet(dealerId, userId, domain, request)
        assert(request.terms?.first()?.rates?.first()?.stockTypeNew == true)
        assert(request.terms?.first()?.rates?.get(1)?.stockTypeNew == false)
    }

    @Test
    fun `saveRateSheetConfiguration should call configServiceClient and set stockTypeNew - MAX`() {
        // Given
        val dealerId = "123"
        val userId = "user1"
        val domain = "example.com"
        val rateNew = Rate(ageRange = FallbackRateSheet.AgeRange(min = "1", max = "New"), stockTypeNew = null)
        val rateUsed = Rate(ageRange = FallbackRateSheet.AgeRange(min = "0", max = "1"), stockTypeNew = null)
        val term = Term(rates = listOf(rateNew, rateUsed))
        val request = FallbackRateSheet(terms = listOf(term))

        // When
        rateSheetService.saveRateSheetConfiguration(dealerId, userId, domain, request)

        // Then
        verify(configServiceClient).saveRateSheet(dealerId, userId, domain, request)
        assert(request.terms?.first()?.rates?.first()?.stockTypeNew == true)
        assert(request.terms?.first()?.rates?.get(1)?.stockTypeNew == false)
    }

    @Test
    fun `saveRateSheetConfiguration should handle null terms and rates`() {
        // Given
        val dealerId = "123"
        val userId = "user1"
        val domain = "example.com"
        val requestWithNullTerms = FallbackRateSheet(terms = null)
        val requestWithNullRates = FallbackRateSheet(terms = listOf(Term(rates = null)))


        // When
        rateSheetService.saveRateSheetConfiguration(dealerId, userId, domain, requestWithNullTerms)
        rateSheetService.saveRateSheetConfiguration(dealerId, userId, domain, requestWithNullRates)

        // Then
        verify(configServiceClient).saveRateSheet(dealerId, userId, domain, requestWithNullTerms)
        verify(configServiceClient).saveRateSheet(dealerId, userId, domain, requestWithNullRates)
        // No exceptions should be thrown
    }

    @Test
    fun `saveRateSheetConfiguration should handle null ageRange`() {
        // Given
        val dealerId = "123"
        val userId = "user1"
        val domain = "example.com"
        val rateWithNullAgeRange = Rate(ageRange = null, stockTypeNew = null)
        val term = Term(rates = listOf(rateWithNullAgeRange))
        val request = FallbackRateSheet(terms = listOf(term))

        // When
        rateSheetService.saveRateSheetConfiguration(dealerId, userId, domain, request)

        // Then
        verify(configServiceClient).saveRateSheet(dealerId, userId, domain, request)
        assert(request.terms?.first()?.rates?.first()?.stockTypeNew == false)
    }
}
