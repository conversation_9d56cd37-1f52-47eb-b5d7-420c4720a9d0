package com.carsaver.partner.configuration.ctaconfig

import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigBeanMapper
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.util.Optional

class CtaConfigBeanMapperTest {

    @Test
    fun `configToMap converts CtaConfig to correct map representation`() {
        // Given
        val ctaConfig = createTestCtaConfig()

        // When
        val result = CtaConfigBeanMapper.configToMap(ctaConfig)

        // Then
        // VDP formatting options
        assertEquals("300", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/width"])
        assertEquals("50", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/height"])
        assertEquals("Arial", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-family"])
        assertEquals("16", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-size"])
        assertEquals("bold", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-weight"])
        assertEquals("center", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/align-content"])
        assertEquals("10", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/padding"])
        assertEquals("15", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/margin"])
        assertEquals("5", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/radius"])

        // VDP primary button
        assertEquals("Vehicle Details Page", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/link-destination"])
        assertEquals("#FFFFFF", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/text-color"])
        assertEquals("bold", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/font-weight"])
        assertEquals("#F0F0F0", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/hover-text-color"])
        assertEquals("#0056b3", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/hover-background-color"])
        assertEquals("#007BFF", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/background-color"])
        assertEquals("View Details", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/button-text"])
        assertEquals("true", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/display"])
        assertEquals("View Details", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/button-text"])
        assertEquals("false", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/enable-image-cta"])

        // SRP formatting options
        assertEquals("300", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/width"])
        assertEquals("50", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/height"])
        assertEquals("Arial", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-family"])
        assertEquals("16", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-size"])
        assertEquals("bold", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-weight"])
        assertEquals("center", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/align-content"])
        assertEquals("10", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/padding"])
        assertEquals("15", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/margin"])
        assertEquals("5", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/radius"])

        // SRP primary button
        assertEquals("Vehicle Details Page", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/link-destination"])
        assertEquals("#FFFFFF", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/text-color"])
        assertEquals("bold", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/font-weight"])
        assertEquals("#F0F0F0", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/hover-text-color"])
        assertEquals("#0056b3", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/hover-background-color"])
        assertEquals("#007BFF", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/background-color"])
        assertEquals("View Details", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/button-text"])
        assertEquals("true", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/display"])
        assertEquals("View Details", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/button-text"])
        assertEquals("false", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/enable-image-cta"])
    }

    @Test
    fun `configToMap handles null and empty values correctly`() {
        // Given
        val ctaConfig = createPartialCtaConfig()

        // When
        val result = CtaConfigBeanMapper.configToMap(ctaConfig)

        // Then
        assertEquals(null, result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/width"])
        assertEquals("Arial", result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-family"])
        assertEquals(null, result["websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-size"])
        assertEquals("center", result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/align-content"])
        assertEquals(null, result["websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/display"])
    }

    private fun createTestCtaConfig(): CtaConfigResponse {
        // Create VDP buttons
        val vdpPrimaryButton = CtaConfigResponse.ButtonOptions(
            font = Optional.of("Arial"),
            fontSize = Optional.of("16"),
            fontWeight = Optional.of("bold"),
            textAlign = Optional.of("center"),
            width = Optional.of("300"),
            height = Optional.of("50"),
            padding = Optional.of("10"),
            margin = Optional.of("15"),
            borderRadius = Optional.of("5"),
            active = Optional.of("true"),
            imageName = Optional.of("View Details"),
            name = Optional.of("View Details"),
            destination = Optional.of("Vehicle Details Page"),
            color = Optional.of("#FFFFFF"),
            backgroundColor = Optional.of("#007BFF"),
            hoverColor = Optional.of("#F0F0F0"),
            hoverBackgroundColor = Optional.of("#0056b3"),
            renderType = Optional.of("HTML")
        )

        // Create SRP buttons (same as VDP for this test)
        val srpPrimaryButton = CtaConfigResponse.ButtonOptions(
            font = Optional.of("Arial"),
            fontSize = Optional.of("16"),
            fontWeight = Optional.of("bold"),
            textAlign = Optional.of("center"),
            width = Optional.of("300"),
            height = Optional.of("50"),
            padding = Optional.of("10"),
            margin = Optional.of("15"),
            borderRadius = Optional.of("5"),
            active = Optional.of("true"),
            imageName = Optional.of("View Details"),
            name = Optional.of("View Details"),
            destination = Optional.of("Vehicle Details Page"),
            color = Optional.of("#FFFFFF"),
            backgroundColor = Optional.of("#007BFF"),
            hoverColor = Optional.of("#F0F0F0"),
            hoverBackgroundColor = Optional.of("#0056b3"),
            renderType = Optional.of("HTML")
        )

        // Create CtaPages
        val vdpPage = CtaConfigResponse.CtaPage(
            primaryButton = Optional.of(vdpPrimaryButton),
            secondButton = null,
            thirdButton = null,
            fourthButton = null
        )

        val srpPage = CtaConfigResponse.CtaPage(
            primaryButton = Optional.of(srpPrimaryButton),
            secondButton = null,
            thirdButton = null,
            fourthButton = null
        )

        // Create and return the CtaConfig
        return CtaConfigResponse(
            vdp = Optional.of(vdpPage),
            listings = Optional.of(srpPage)
        )
    }

    private fun createPartialCtaConfig(): CtaConfigResponse {
        // Create VDP formatting options with some nulls
        val vdpFormattingOptions = CtaConfigResponse.ButtonOptions(
            font = Optional.of("Arial"),
            fontSize = null,
            fontWeight = Optional.of("bold"),
            textAlign = null,
            width = null,
            height = Optional.of("50"),
            padding = null,
            margin = null,
            borderRadius = null
        )

        // Create SRP formatting options with some empty optionals
        val srpFormattingOptions = CtaConfigResponse.ButtonOptions(
            font = Optional.empty(),
            fontSize = null,
            fontWeight = null,
            textAlign = Optional.of("center"),
            width = Optional.empty(),
            height = null,
            padding = null,
            margin = null,
            borderRadius = null
        )

        // Create CtaPages
        val vdpPage = CtaConfigResponse.CtaPage(
            primaryButton = Optional.of(vdpFormattingOptions),
            secondButton = null,
            thirdButton = null,
            fourthButton = null
        )

        val srpPage = CtaConfigResponse.CtaPage(
            primaryButton = Optional.of(srpFormattingOptions),
            secondButton = null,
            thirdButton = null,
            fourthButton = null
        )

        // Create and return the CtaConfig
        return CtaConfigResponse(
            vdp = Optional.of(vdpPage),
            listings = Optional.of(srpPage)
        )
    }

}
