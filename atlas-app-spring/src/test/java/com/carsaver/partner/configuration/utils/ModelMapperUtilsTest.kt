package com.carsaver.partner.configuration.utils

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.mockito.MockitoAnnotations

class ModelMapperUtilsTest {

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun `mapToModel should map string value to string field`() {
        // Given
        val map = mapOf("stringField" to "test value")
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals("test value", result.stringField)
    }
    
    @Test
    fun `mapToModel should map integer value to int field`() {
        // Given
        val map = mapOf("intField" to 42)
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals(42, result.intField)
    }

    @Test
    fun `mapToModel should map string integer value to int field`() {
        // Given
        val map = mapOf("intField" to "42")
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals(42, result.intField)
    }
    
    @Test
    fun `mapToModel should map boolean value to boolean field`() {
        // Given
        val map = mapOf("booleanField" to true)
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertTrue(result.booleanField)
    }

    @Test
    fun `mapToModel should map string boolean value to boolean field`() {
        // Given
        val map = mapOf("booleanField" to "true")
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertTrue(result.booleanField)
    }
    
    @Test
    fun `mapToModel should map json string to list field`() {
        // Given
        val map = mapOf("listField" to "[\"item1\", \"item2\"]")
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals(2, result.listField.size)
        assertEquals("item1", result.listField[0])
        assertEquals("item2", result.listField[1])
    }
    
    @Test
    fun `mapToModel should handle null values`() {
        // Given
        val map = mapOf("stringField" to null)
        val model = TestModel(stringField = "original")
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertNull(result.stringField)
    }
    
    @Test
    fun `mapToModel should handle invalid type conversions`() {
        // Given
        val map = mapOf("intField" to "not a number")
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals(0, result.intField) // Default int value
    }
    
    @Test
    fun `mapToModel should handle invalid JSON for lists`() {
        // Given
        val map = mapOf("listField" to "{invalid json}")
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertTrue(result.listField.isEmpty()) // Default empty list
    }
    
    @Test
    fun `mapToModel should set and restore field accessibility`() {
        // Given
        val map = mapOf("privateField" to "private value")
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals("private value", result.getPrivateField())
    }
    
    @Test
    fun `mapToModel should handle fields from parent class`() {
        // Given
        val map = mapOf("parentField" to "parent value")
        val model = ChildTestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals("parent value", result.parentField)
    }
    
    @Test
    fun `mapToModel should ignore non-existent fields`() {
        // Given
        val map = mapOf("nonExistentField" to "some value")
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        // Should not throw exception
        assertNotNull(result)
    }

    @Test
    fun `mapToModel should handle object value for int field`() {
        // Given
        val complexObject = object { val someProperty = "test" }
        val map = mapOf("intField" to complexObject)
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals(0, result.intField) // Default int value since conversion returns null
    }
    
    @Test
    fun `mapToModel should handle object value for boolean field`() {
        // Given
        val complexObject = object { val someProperty = "test" }
        val map = mapOf("booleanField" to complexObject)
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertFalse(result.booleanField) // Default boolean value since conversion returns null
    }
    
    @Test
    fun `mapToModel should handle custom object field`() {
        // Given
        val customObject = CustomObject("test name")
        val map = mapOf("customField" to customObject)
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals(customObject, result.customField)
        assertEquals("test name", result.customField?.name)
    }
    
    @Test
    fun `mapToModel should handle exception when finding field`() {
        // Given
        val map = mapOf("fieldWithException" to "value")
        val model = TestModelWithFieldException()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        // Test should complete without throwing exception
        assertNotNull(result)
    }
    
    @Test
    fun `mapToModel should handle List field with a direct List value`() {
        // Given
        val listValue = listOf("item1", "item2", "item3")
        val map = mapOf("listField" to listValue)
        val model = TestModel()
        
        // When
        val result = ModelMapperUtils.mapToModel(map, model)
        
        // Then
        assertEquals(3, result.listField.size)
        assertEquals("item1", result.listField[0])
        assertEquals("item3", result.listField[2])
    }

    @Test
    fun `creating ModelMapperUtils instance`() {
        // Simply create an instance of ModelMapperUtils to ensure class coverage
        val instance = ModelMapperUtils()
        
        // Then
        assertNotNull(instance)
    }

    // Test model classes
    open class ParentTestModel {
        var parentField: String = ""
    }
    
    class ChildTestModel : ParentTestModel() {
        var childField: String = ""
    }
    
    class TestModel(
        var stringField: String = "",
        var intField: Int = 0,
        var booleanField: Boolean = false,
        var listField: List<String> = listOf()
    ) {
        private var privateField: String = ""
        var customField: CustomObject? = null
        
        fun getPrivateField(): String = privateField
    }
    
    data class CustomObject(val name: String)
    
    class TestModelWithFieldException {
        // This getter will throw an exception when accessed via reflection
        val fieldWithException: String
            get() = throw RuntimeException("Test exception")
    }
}
