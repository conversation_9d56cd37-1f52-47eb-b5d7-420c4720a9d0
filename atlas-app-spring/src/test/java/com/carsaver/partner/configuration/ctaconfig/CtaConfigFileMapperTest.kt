package com.carsaver.partner.configuration.ctaconfig

import com.carsaver.partner.configuration.ctaconfig.CtaConfigRequest.ButtonOptions
import com.carsaver.partner.configuration.ctaconfig.CtaConfigRequest.CtaPage
import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigFileMapper
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
import java.util.*

class CtaConfigFileMapperTest {

    @Test
    fun processFiles_fileEmpty_test() {
        val dealerId: String = "DEALER_ID"
        val ctaConfig: CtaConfigRequest = CtaConfigRequest()
        val vdpFiles: MutableList<MultipartFile?> = mutableListOf(null, null, null, null)
        val vlpFiles: MutableList<MultipartFile?> = mutableListOf(null, null, null, null)

        val notNullFiles = CtaConfigFileMapper.injectFiles(dealerId, ctaConfig, vdpFiles, vlpFiles)


        // Assert response list contains no null files
        assertTrue(notNullFiles.isEmpty())
        assertNull(ctaConfig.vdp)
        assertNull(ctaConfig.listings)
    }


    @Test
    fun processFiles_one_vdp_file_test() {
        val dealerId: String = "DEALER_ID"
        val ctaConfig: CtaConfigRequest = CtaConfigRequest()

        val file1 = MockMultipartFile("file1", "original1.jpg", "image/jpeg", TestUtils.vlp(),)
        val vdpFiles: MutableList<MultipartFile?> = mutableListOf(file1, null, null, null)
        val vlpFiles: MutableList<MultipartFile?> = mutableListOf(null, null, null, null)

        val notNullFiles = CtaConfigFileMapper.injectFiles(dealerId, ctaConfig, vdpFiles, vlpFiles)


        // Assert response list contains no null files
        assertFalse(notNullFiles.isEmpty())
        assertNull(ctaConfig.listings)

        assertNotNull(ctaConfig.vdp!!.get().primaryButton!!.get().imageFileName)
        assertEquals("DEALER_ID-vdp-cta-1.jpg", ctaConfig.vdp!!.get().primaryButton!!.get().imageFileName!!.get())
        assertEquals("original1.jpg", ctaConfig.vdp!!.get().primaryButton!!.get().imageName!!.get())
        assertEquals("57679", ctaConfig.vdp!!.get().primaryButton!!.get().imageSize!!.get())
    }

    @Test
    fun processFiles_one_and_four_vdp_file_test() {
        val dealerId: String = "DEALER_ID"
        val ctaConfig: CtaConfigRequest = CtaConfigRequest()

        val file1 = MockMultipartFile("file1", "original1.jpg", "image/jpeg", TestUtils.vlp())
        val file4 = MockMultipartFile("file4", "original4.jpg", "image/png", TestUtils.vlp())
        val vdpFiles: MutableList<MultipartFile?> = mutableListOf(file1, null, null, file4)
        val vlpFiles: MutableList<MultipartFile?> = mutableListOf(null, null, null, null)

        val notNullFiles = CtaConfigFileMapper.injectFiles(dealerId, ctaConfig, vdpFiles, vlpFiles)


        // Assert response list contains no null files
        assertFalse(notNullFiles.isEmpty())
        assertNull(ctaConfig.listings)

        assertNotNull(ctaConfig.vdp!!.get().primaryButton!!.get().imageFileName)
        assertEquals("DEALER_ID-vdp-cta-1.jpg", ctaConfig.vdp!!.get().primaryButton!!.get().imageFileName!!.get())
        assertEquals("DEALER_ID-vdp-cta-1.jpg", vdpFiles[0]!!.originalFilename)
        assertEquals("original1.jpg", ctaConfig.vdp!!.get().primaryButton!!.get().imageName!!.get())

        assertNotNull(ctaConfig.vdp!!.get().fourthButton!!.get().imageFileName)
        assertEquals("DEALER_ID-vdp-cta-4.jpg", ctaConfig.vdp!!.get().fourthButton!!.get().imageFileName!!.get())
        assertEquals("original4.jpg", ctaConfig.vdp!!.get().fourthButton!!.get().imageName!!.get())
        assertEquals("DEALER_ID-vdp-cta-4.jpg", vdpFiles[3]!!.originalFilename)
    }

    @Test
    fun processFiles_one_listings_file_test() {
        val dealerId: String = "DEALER_ID"
        val ctaConfig: CtaConfigRequest = CtaConfigRequest()

        val file1 = MockMultipartFile("file1", "original1.jpg", "image/jpeg", TestUtils.vlp())
        val vdpFiles: MutableList<MultipartFile?> = mutableListOf(null, null, null, null)
        val vlpFiles: MutableList<MultipartFile?> = mutableListOf(file1, null, null, null)

        val notNullFiles = CtaConfigFileMapper.injectFiles(dealerId, ctaConfig, vdpFiles, vlpFiles)


        // Assert response list contains no null files
        assertFalse(notNullFiles.isEmpty())
        assertNull(ctaConfig.vdp)

        assertNotNull(ctaConfig.listings!!.get().primaryButton!!.get().imageFileName)
        assertEquals("DEALER_ID-vlp-cta-1.jpg", ctaConfig.listings!!.get().primaryButton!!.get().imageFileName!!.get())
        assertEquals("DEALER_ID-vlp-cta-1.jpg", vlpFiles[0]!!.originalFilename)
        assertEquals("original1.jpg", ctaConfig.listings!!.get().primaryButton!!.get().imageName!!.get())
    }

    @Test
    fun processFiles_one_and_four_listings_file_test() {
        val dealerId: String = "DEALER_ID"
        val ctaConfig: CtaConfigRequest = CtaConfigRequest()

        val file1 = MockMultipartFile("file1", "original1.jpg", "image/jpeg", TestUtils.vlp())
        val file4 = MockMultipartFile("file4", "original4.jpg", "image/png", TestUtils.vlp())
        val vdpFiles: MutableList<MultipartFile?> = mutableListOf(null, null, null, null)
        val vlpFiles: MutableList<MultipartFile?> = mutableListOf(file1, null, null, file4)

        val notNullFiles = CtaConfigFileMapper.injectFiles(dealerId, ctaConfig, vdpFiles, vlpFiles)


        // Assert response list contains no null files
        assertFalse(notNullFiles.isEmpty())
        assertNull(ctaConfig.vdp)

        assertNotNull(ctaConfig.listings!!.get().primaryButton!!.get().imageFileName)
        assertEquals("DEALER_ID-vlp-cta-1.jpg", ctaConfig.listings!!.get().primaryButton!!.get().imageFileName!!.get())
        assertEquals("DEALER_ID-vlp-cta-1.jpg", vlpFiles[0]!!.originalFilename)
        assertEquals("original1.jpg", ctaConfig.listings!!.get().primaryButton!!.get().imageName!!.get())

        assertNotNull(ctaConfig.listings!!.get().fourthButton!!.get().imageFileName)
        assertEquals("DEALER_ID-vlp-cta-4.jpg", ctaConfig.listings!!.get().fourthButton!!.get().imageFileName!!.get())
        assertEquals("DEALER_ID-vlp-cta-4.jpg", vlpFiles[3]!!.originalFilename)
        assertEquals("original4.jpg", ctaConfig.listings!!.get().fourthButton!!.get().imageName!!.get())
    }

    @Test
    fun processFiles_image_name_fields_test() {
        val dealerId: String = "DEALER_ID"
        val ctaConfig: CtaConfigRequest = CtaConfigRequest(
            vdp = Optional.of(
                CtaPage(
                    primaryButton = Optional.of(ButtonOptions(imageDisplayName = Optional.of("IMAGE ALT TEXT - PRIMARY BUTTON"))),
                    secondButton = Optional.empty(),
                    thirdButton = Optional.empty(),
                    fourthButton = Optional.of(ButtonOptions(imageDisplayName = Optional.of("IMAGE ALT TEXT - FOURTH BUTTON")))
                )
            )
        )

        val file1 = MockMultipartFile("file1", "original1.jpg", "image/jpeg", TestUtils.vlp())
        val file4 = MockMultipartFile("file4", "original4.jpg", "image/png", TestUtils.vlp())
        val vdpFiles: MutableList<MultipartFile?> = mutableListOf(file1, null, null, file4)
        val vlpFiles: MutableList<MultipartFile?> = mutableListOf(null, null, null, null)

        val notNullFiles = CtaConfigFileMapper.injectFiles(dealerId, ctaConfig, vdpFiles, vlpFiles)

        // Assert response list contains no null files
        assertFalse(notNullFiles.isEmpty())
        assertNull(ctaConfig.listings)

        assertNotNull(ctaConfig.vdp!!.get().primaryButton!!.get().imageFileName)
        assertEquals("DEALER_ID-vdp-cta-1.jpg", ctaConfig.vdp!!.get().primaryButton!!.get().imageFileName!!.get())
        assertEquals("DEALER_ID-vdp-cta-1.jpg", vdpFiles[0]!!.originalFilename)
        assertEquals("original1.jpg", ctaConfig.vdp!!.get().primaryButton!!.get().imageName!!.get())
        assertEquals("IMAGE ALT TEXT - PRIMARY BUTTON", ctaConfig.vdp!!.get().primaryButton!!.get().imageDisplayName!!.get())

        assertNotNull(ctaConfig.vdp!!.get().fourthButton!!.get().imageFileName)
        assertEquals("DEALER_ID-vdp-cta-4.jpg", ctaConfig.vdp!!.get().fourthButton!!.get().imageFileName!!.get())
        assertEquals("original4.jpg", ctaConfig.vdp!!.get().fourthButton!!.get().imageName!!.get())
        assertEquals("DEALER_ID-vdp-cta-4.jpg", vdpFiles[3]!!.originalFilename)
        assertEquals("IMAGE ALT TEXT - FOURTH BUTTON", ctaConfig.vdp!!.get().fourthButton!!.get().imageDisplayName!!.get())
    }
}
