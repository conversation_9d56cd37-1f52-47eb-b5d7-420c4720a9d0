package com.carsaver.partner.customer;

import com.carsaver.elasticsearch.model.LeadCertificate;
import com.carsaver.elasticsearch.model.LeadDoc;
import com.carsaver.elasticsearch.model.UserContractDoc;
import com.carsaver.elasticsearch.model.sale.VehicleSaleDoc;
import com.carsaver.elasticsearch.service.LeadDocService;
import com.carsaver.elasticsearch.service.UserContractDocService;
import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.LoanClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.UserVehicleClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.magellan.model.lead.SaleStatus;
import com.carsaver.magellan.model.user.CreditPreApproval;
import com.carsaver.magellan.model.user.CreditProfile;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.magellan.model.vehicle.UserVehicleQuoteView;
import com.carsaver.partner.client.activity.ActivityAggregation;
import com.carsaver.partner.client.activity.ActivityClient;
import com.carsaver.partner.client.bigquery.BigQueryClient;
import com.carsaver.partner.client.digitalretail.ChatHistorySummary;
import com.carsaver.partner.client.digitalretail.DealerTokenExchange;
import com.carsaver.partner.client.digitalretail.DealerTokenExchangeResponse;
import com.carsaver.partner.client.digitalretail.DigitalRetailClient;
import com.carsaver.partner.client.oauth.OAuthToken;
import com.carsaver.partner.elasticsearch.VehicleSaleDocService;
import com.carsaver.partner.elasticsearch.criteria.LeadSearchCriteria;
import com.carsaver.partner.elasticsearch.criteria.UserContractSearchCriteria;
import com.carsaver.partner.elasticsearch.criteria.VehicleSaleSearchCriteria;
import com.carsaver.partner.http.HttpService;
import com.carsaver.partner.notes.NotesService;
import com.carsaver.partner.notes.UserNoteResponse;
import com.carsaver.partner.prequalification.CreditResponseStatusDTO;
import com.carsaver.partner.prequalification.PreQualRecord;
import com.carsaver.partner.prequalification.PreQualService;
import com.carsaver.partner.service.UserDocumentsService;
import com.carsaver.partner.service.UserVehicleService;
import com.carsaver.partner.service.sellathome.UserVehicleSellInfoService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.web.api.user.UserLeadService;
import com.carsaver.search.support.SearchResults;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.PagedModel;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.hibernate.validator.internal.util.Contracts.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CustomerDetailsLinkServiceTest {

    public static final String PRE_QUALIFICATIONS = "Pre-Qualifications";
    private String userId;
    private List<String> dealerIds;

    @Mock
    private UserClient userClient;

    @Mock
    private LoanClient loanClient;

    @Mock
    private UserVehicleClient userVehicleClient;

    @Mock
    private CertificateClient certificateClient;

    @Mock
    private UserDocumentsService userDocumentsService;

    @Mock
    private UserVehicleService userVehicleService;

    @Mock
    private UserVehicleSellInfoService userVehicleSellInfoService;

    @Mock
    private VehicleSaleDocService vehicleSaleDocService;

    @Mock
    private LeadDocService leadDocService;

    @Mock
    private SplitFeatureFlags splitFeatureFlags;

    @Mock
    private NotesService notesService;

    @Mock
    private UserContractDocService userContractDocService;

    @Mock
    private UserLeadService userLeadService;

    @Mock
    private HttpService httpService;

    @Mock
    private BigQueryClient bigQueryClient;
    @Mock
    private ActivityClient activityClient;
    @Mock
    private DigitalRetailClient digitalRetailClient;

    @Mock
    PreQualService preQualService;

    @InjectMocks
    private CustomerDetailsLinkService customerDetailsLinkService;

    @BeforeEach
    public void setUp() {
        customerDetailsLinkService = new CustomerDetailsLinkService(
            userClient,
            loanClient,
            certificateClient,
            userVehicleClient,
            userVehicleService,
            userDocumentsService,
            userVehicleSellInfoService,
            vehicleSaleDocService,
            leadDocService,
            preQualService,
            splitFeatureFlags,
            notesService,
            userContractDocService,
            userLeadService,
            activityClient,
            digitalRetailClient,
            bigQueryClient
        );

        this.dealerIds = List.of(UUID.randomUUID().toString());
        this.userId = UUID.randomUUID().toString();
    }

    @Test
    void testGetPreApprovalsLink() {
        CreditProfile mockCreditProfile = new CreditProfile();
        CreditPreApproval approval = new CreditPreApproval();
        approval.setPreApprovalDate(LocalDate.now());
        mockCreditProfile.setPreApprovals(Map.of(1, approval));
        when(userClient.findCreditProfileByUser(userId)).thenReturn(Optional.of(mockCreditProfile));

        // Run the test
        CustomerDetailsLink preApprovalsLink = customerDetailsLinkService.getPreApprovalsLink(userId, CustomerDetailsLinkType.ACTIVE);

        // Assertions
        assertNotNull(preApprovalsLink);
        assertEquals("Pre-Approvals", preApprovalsLink.getName());
        assertEquals(CustomerDetailsLinkType.ACTIVE, preApprovalsLink.getStatus());
        assertEquals(1, preApprovalsLink.getCount());
    }

    @Test
    void testGetOverviewLink() {
        CustomerDetailsLink overviewLink = customerDetailsLinkService.getOverviewLink(CustomerDetailsLinkType.INACTIVE);
        assertNotNull(overviewLink);
        assertEquals("Overview", overviewLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, overviewLink.getStatus());
        assertEquals(null, overviewLink.getUrl());
        assertEquals(0, overviewLink.getCount());
    }

    @Test
    void testGetActivityLink() {
        ActivityAggregation activityEventAggregation = new ActivityAggregation();
        activityEventAggregation.setCount(1);
        when(activityClient.getDigitalRetailLogsByUserIdAndDealerIdsCount(dealerIds, userId)).thenReturn(activityEventAggregation);
        var token = new OAuthToken();
        token.setAccessToken("token");
        when(digitalRetailClient.getAllChatHistoryCount(userId)).thenReturn(1);
        CustomerDetailsLink activityLink = customerDetailsLinkService.getActivityLink(dealerIds, userId, CustomerDetailsLinkType.INACTIVE, "example.com");
        assertNotNull(activityLink);
        assertEquals("Activity", activityLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, activityLink.getStatus());
        assertEquals("https://ar-example.com/adaptive-retail", activityLink.getUrl());
        assertEquals(2, activityLink.getCount());
    }

    @Test
    void testGetRecentlyViewedVehiclesLink() {
        CertificateView view = new CertificateView();
        view.setDealerId(dealerIds.get(0));
        PagedModel.PageMetadata metadata = new PagedModel.PageMetadata(1, 0, 1, 1);
        PagedModel<CertificateView> views = PagedModel.of(List.of(view), metadata);

        // Mock for single dealer case
        when(certificateClient.findRecentlyViewedByUserAndDealer(
            eq(userId),
            eq(dealerIds.get(0)),
            any(PageRequest.class)
        )).thenReturn(views);

        CustomerDetailsLink recentlyViewedLink = customerDetailsLinkService.getRecentlyViewedVehiclesLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE, "example.com");
        assertNotNull(recentlyViewedLink);
        assertEquals("Viewed Vehicles", recentlyViewedLink.getName());
        assertEquals(CustomerDetailsLinkType.ACTIVE, recentlyViewedLink.getStatus());
        assertEquals("https://example.com/adaptive-retail/garage/recently-viewed", recentlyViewedLink.getUrl());
        assertEquals(1, recentlyViewedLink.getCount());
    }

    @Test
    void testGetSavedSearchesLink() {
        CustomerDetailsLink savedSearchesLink = customerDetailsLinkService.getSavedSearchesLink(CustomerDetailsLinkType.INACTIVE);
        assertNotNull(savedSearchesLink);
        assertEquals("Searches", savedSearchesLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, savedSearchesLink.getStatus());
        assertEquals(null, savedSearchesLink.getUrl());
        assertEquals(0, savedSearchesLink.getCount());
    }

    @Test
    void testGetCurrentVehiclesLink() {
        UserVehicleView view = new UserVehicleView();
        PagedModel.PageMetadata metadata = new PagedModel.PageMetadata(1, 0, 1, 1);
        PagedModel<UserVehicleView> views = PagedModel.of(List.of(view), metadata);
        UserVehicleQuoteView quote = new UserVehicleQuoteView();
        when(splitFeatureFlags.isAtlasNewCustomerDetailsPageCurrentVehiclesEnabled()).thenReturn(false);

        when(userVehicleClient.findAllByUserAndRecordTypeAndDeletedFalse(
            eq(userId),
            eq("TRADE"),
            any(PageRequest.class))
        ).thenReturn(views);

        // Mock for each dealer ID in the list
        for (String dealerId : dealerIds) {
            when(userVehicleService.getUserVehicleQuote(eq(view), eq(dealerId)))
                .thenReturn(quote);
        }

        CustomerDetailsLink currentVehiclesLink = customerDetailsLinkService.getCurrentVehiclesLink(dealerIds, userId, CustomerDetailsLinkType.EXPIRED, "example.com");
        assertNotNull(currentVehiclesLink);
        assertEquals("Current Vehicles", currentVehiclesLink.getName());
        assertEquals(CustomerDetailsLinkType.EXPIRED, currentVehiclesLink.getStatus());
        assertEquals("https://example.com/adaptive-retail/garage/trade", currentVehiclesLink.getUrl());
        assertEquals(1, currentVehiclesLink.getCount());
    }

    @Test
    void testGetCurrentVehiclesLinkWithSellAtHome() {
        UserVehicleView view = new UserVehicleView();
        view.setRecordType("TRADE");

        UserVehicleView sellView = new UserVehicleView();
        view.setRecordType("SELL");


        PagedModel.PageMetadata metadata = new PagedModel.PageMetadata(1, 0, 1, 1);
        PagedModel<UserVehicleView> views = PagedModel.of(List.of(view, sellView), metadata);
        UserVehicleQuoteView quote = new UserVehicleQuoteView();
        when(splitFeatureFlags.isAtlasNewCustomerDetailsPageCurrentVehiclesEnabled()).thenReturn(true);

        when(userVehicleClient.findAllByUserAndDeletedFalse(
            anyString(),
            any(PageRequest.class))
        ).thenReturn(views);

        // Mock for each dealer ID in the list
        for (String dealerId : dealerIds) {
            when(userVehicleService.getUserVehicleQuote(eq(view), eq(dealerId)))
                .thenReturn(quote);
        }

        CustomerDetailsLink currentVehiclesLink = customerDetailsLinkService.getCurrentVehiclesLink(dealerIds, userId, CustomerDetailsLinkType.EXPIRED, "example.com");
        assertNotNull(currentVehiclesLink);
        assertEquals("Current Vehicles", currentVehiclesLink.getName());
        assertEquals(CustomerDetailsLinkType.EXPIRED, currentVehiclesLink.getStatus());
        assertEquals("https://example.com/adaptive-retail/garage/trade", currentVehiclesLink.getUrl());
        assertEquals(2, currentVehiclesLink.getCount());
    }

    @Test
    void testGetPreQualificationsLink() {
        CustomerDetailsLink preQualificationsLink = customerDetailsLinkService.getPreQualificationsLink(userId, CustomerDetailsLinkType.INACTIVE);
        assertNotNull(preQualificationsLink);
        assertEquals("Pre-Qualifications", preQualificationsLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, preQualificationsLink.getStatus());
        assertEquals(null, preQualificationsLink.getUrl());
        assertEquals(0, preQualificationsLink.getCount());
    }

    @Test
    void testGetPreQualificationsLink_WhenHasPassedRecord_StatusRemainsUnchanged() {
        var recordPassed = createPreQualRecordWithStatus(CreditResponseStatusDTO.PASSED);

        when(preQualService.getPreQualRecordsByCustomerId(userId))
            .thenReturn(List.of(recordPassed));

        CustomerDetailsLink preQualificationsLink = customerDetailsLinkService.getPreQualificationsLink(userId, CustomerDetailsLinkType.ACTIVE);

        assertNotNull(preQualificationsLink);
        assertEquals(PRE_QUALIFICATIONS, preQualificationsLink.getName());
        assertEquals(CustomerDetailsLinkType.ACTIVE, preQualificationsLink.getStatus());
        assertEquals(1, preQualificationsLink.getCount());
    }

    @Test
    void testGetPreQualificationsLink_WhenNoPassedRecord_StatusIsExpired() {
        var failedRecord = createPreQualRecordWithStatus(CreditResponseStatusDTO.FAIL);

        var lockedRecord = createPreQualRecordWithStatus(CreditResponseStatusDTO.LOCKED);

        when(preQualService.getPreQualRecordsByCustomerId(userId))
            .thenReturn(List.of(failedRecord, lockedRecord));

        CustomerDetailsLink preQualificationsLink = customerDetailsLinkService.getPreQualificationsLink(userId, CustomerDetailsLinkType.ACTIVE);

        assertNotNull(preQualificationsLink);
        assertEquals(PRE_QUALIFICATIONS, preQualificationsLink.getName());
        assertEquals(CustomerDetailsLinkType.EXPIRED, preQualificationsLink.getStatus());
        assertEquals(2, preQualificationsLink.getCount());
    }

    @Test
    void testGetPreQualificationsLink_WhenNoRecords_StatusInactive() {
        when(preQualService.getPreQualRecordsByCustomerId(userId))
            .thenReturn(Collections.emptyList());

        CustomerDetailsLink preQualificationsLink = customerDetailsLinkService.getPreQualificationsLink(userId, CustomerDetailsLinkType.ACTIVE);

        assertNotNull(preQualificationsLink);
        assertEquals(PRE_QUALIFICATIONS, preQualificationsLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, preQualificationsLink.getStatus());
        assertEquals(0, preQualificationsLink.getCount());
    }

    @Test
    void testGetLeadsLink() {
        LeadSearchCriteria criteria = new LeadSearchCriteria();
        criteria.setUserId(userId);
        criteria.setDealerIds(dealerIds);
        LeadDoc leadDoc = new LeadDoc();
        leadDoc.setCertificate(LeadCertificate.builder().build());

        SearchResults<LeadDoc> results = mock(SearchResults.class);
        when(results.getContent()).thenReturn(List.of(leadDoc));

        when(leadDocService.search(criteria, PageRequest.of(0, Integer.MAX_VALUE))).thenReturn(results);

        CustomerDetailsLink leadsLink = customerDetailsLinkService.getLeadsLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE, "example.com");
        assertNotNull(leadsLink);
        assertEquals("Leads", leadsLink.getName());
        assertEquals("https://ar-example.com/adaptive-retail", leadsLink.getUrl());
        assertEquals(1, leadsLink.getCount());
    }

    @Test
    void testGetFinanceApplicationsLink() {
        when(loanClient.findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED))
            .thenReturn(CollectionModel.of(List.of()));

        CustomerDetailsLink financeApplicationsLink = customerDetailsLinkService.getFinanceApplicationsLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE);
        assertNotNull(financeApplicationsLink);
        assertEquals("Finance Applications", financeApplicationsLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, financeApplicationsLink.getStatus());
        assertEquals(null, financeApplicationsLink.getUrl());
    }

    @Test
    void testGetNotesLink() {
        CustomerDetailsLink notesLink = customerDetailsLinkService.getNotesLink(dealerIds, userId, CustomerDetailsLinkType.INACTIVE);
        assertNotNull(notesLink);
        assertEquals("Notes", notesLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, notesLink.getStatus());
        assertEquals(null, notesLink.getUrl());
        assertEquals(0, notesLink.getCount());
    }

    @Test
    void testGetNotesLinkWithNotes() {
        List<String> dealerIds = List.of("dealer1", "dealer2");
        String userId = "user1";
        List<UserNoteResponse> notes = List.of(new UserNoteResponse());

        when(notesService.getUserNotes(userId, "dealer1")).thenReturn(notes);
        when(notesService.getUserNotes(userId, "dealer2")).thenReturn(notes);

        CustomerDetailsLink notesLink = customerDetailsLinkService.getNotesLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE);

        assertNotNull(notesLink);
        assertEquals(CustomerDetailsLinkType.ACTIVE, notesLink.getStatus());
        assertEquals(2, notesLink.getCount());
    }

    @Test
    void testGetNotesLinkWithNoDealerIds() {
        List<String> emptyDealerIds = List.of();
        CustomerDetailsLink notesLink = customerDetailsLinkService.getNotesLink(emptyDealerIds, userId, CustomerDetailsLinkType.ACTIVE);
        assertNotNull(notesLink);
        assertEquals("Notes", notesLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, notesLink.getStatus());
        assertEquals(null, notesLink.getUrl());
        assertEquals(0, notesLink.getCount());
    }

    @Test
    void testGetNotesLinkWithEmptyNotes() {
        when(notesService.getUserNotes(anyString(), anyString())).thenReturn(List.of());

        CustomerDetailsLink notesLink = customerDetailsLinkService.getNotesLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE);
        assertNotNull(notesLink);
        assertEquals("Notes", notesLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, notesLink.getStatus());
        assertEquals(null, notesLink.getUrl());
        assertEquals(0, notesLink.getCount());
    }

    @Test
    void testGetNotesLinkWithPartialNotes() {
        List<String> dealerIds = List.of("dealer1", "dealer2");
        String userId = "user1";
        List<UserNoteResponse> notesForDealer1 = List.of(new UserNoteResponse());
        List<UserNoteResponse> emptyNotes = List.of();

        when(notesService.getUserNotes(userId, "dealer1")).thenReturn(notesForDealer1);
        when(notesService.getUserNotes(userId, "dealer2")).thenReturn(emptyNotes);

        CustomerDetailsLink notesLink = customerDetailsLinkService.getNotesLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE);

        assertNotNull(notesLink);
        assertEquals("Notes", notesLink.getName());
        assertEquals(CustomerDetailsLinkType.ACTIVE, notesLink.getStatus());
        assertEquals(1, notesLink.getCount());
    }

    @Test
    void testGetCustomerDetailsLinksContainsNotesLink() {
        when(splitFeatureFlags.isArNotesEnabled()).thenReturn(true);
        when(splitFeatureFlags.isArContractRequestEnabled()).thenReturn(true);
        when(splitFeatureFlags.isCustomerDetailsPreQualificationsEnabled()).thenReturn(false);
        when(notesService.getUserNotes(anyString(), anyString()))
            .thenReturn(List.of(new UserNoteResponse()));
        when(userClient.findById(anyString()))
            .thenReturn(new UserView());
        when(loanClient.findLoanRequestsByUserAndStatusGreaterThan(anyString(), any()))
            .thenReturn(CollectionModel.of(Collections.emptyList()));
        when(certificateClient.findSavedByUserAndDealer(anyString(), anyString(), any(PageRequest.class)))
            .thenReturn(PagedModel.empty());
        when(certificateClient.findRecentlyViewedByUserAndDealer(anyString(), anyString(), any(PageRequest.class)))
            .thenReturn(PagedModel.empty());
        when(userVehicleClient.findAllByUserAndRecordTypeAndDeletedFalse(anyString(), anyString(), any(PageRequest.class)))
            .thenReturn(PagedModel.empty());
        when(userDocumentsService.getUserDocuments(anyString(), anyString()))
            .thenReturn(Collections.emptyList());

        VehicleSaleSearchCriteria criteria = new VehicleSaleSearchCriteria();
        criteria.setUserId(userId);
        criteria.setDealerIds(dealerIds);
        criteria.setStatuses(SaleStatus.VERIFIED);
        when(vehicleSaleDocService.search(criteria, PageRequest.of(0, Integer.MAX_VALUE)))
            .thenReturn(SearchResults.<VehicleSaleDoc>builder().build());

        LeadSearchCriteria leadCriteria = new LeadSearchCriteria();
        leadCriteria.setUserId(userId);
        leadCriteria.setDealerIds(dealerIds);
        when(leadDocService.search(leadCriteria, PageRequest.of(0, Integer.MAX_VALUE)))
            .thenReturn(SearchResults.<LeadDoc>builder().build());

        UserContractSearchCriteria userContractCriteria = new UserContractSearchCriteria();
        userContractCriteria.setUserId(userId);
        userContractCriteria.setDealerIds(dealerIds);
        when(userContractDocService.search(any(UserContractSearchCriteria.class), any(PageRequest.class)))
            .thenReturn(SearchResults.<UserContractDoc>builder().build());

        List<CustomerDetailsLink> links = customerDetailsLinkService.getCustomerDetailsLinks(dealerIds, userId);

        // Verify that the list contains a link for "Notes"
        boolean containsNotesLink = links.stream()
            .anyMatch(link -> "Notes".equals(link.getName()));
        assertTrue(containsNotesLink, "The list of links should contain a link for 'Notes'");

        // Verify that the list contains a link for Contract Requests
        boolean containsContractRequestsLink = links.stream()
            .anyMatch(link -> "Contract Requests".equals(link.getName()));
        assertTrue(containsContractRequestsLink, "The list of links should contain a link for 'Contract Requests'");
    }

    @Test
    void testGetDocumentsLink() {
        when(userDocumentsService.getUserDocuments(anyString(), anyString())).thenReturn(List.of());

        CustomerDetailsLink documentsLink = customerDetailsLinkService.getDocumentsLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE);
        assertNotNull(documentsLink);
        assertEquals("Documents", documentsLink.getName());
        assertEquals(CustomerDetailsLinkType.INACTIVE, documentsLink.getStatus());
        assertEquals(null, documentsLink.getUrl());
        assertEquals(0, documentsLink.getCount());
    }

    @Test
    void testGetContractRequestsLinkWithRequest() {
        UserContractSearchCriteria criteria = new UserContractSearchCriteria();
        criteria.setUserId(userId);
        criteria.setDealerIds(dealerIds);
        UserContractDoc userContractDoc = new UserContractDoc();

        SearchResults<UserContractDoc> results = mock(SearchResults.class);
        when(results.getContent()).thenReturn(List.of(userContractDoc));

        when(userContractDocService.search(criteria, PageRequest.of(0, Integer.MAX_VALUE))).thenReturn(results);

        CustomerDetailsLink contractRequestsLink = customerDetailsLinkService.getContractRequestsLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE);
        assertNotNull(contractRequestsLink);
        assertEquals("Contract Requests", contractRequestsLink.getName());
        assertEquals(null, contractRequestsLink.getUrl());
        assertEquals(1, contractRequestsLink.getCount());
    }

    @Test
    void testGetContractRequestsLinkWithNoRequests() {
        UserContractSearchCriteria criteria = new UserContractSearchCriteria();
        criteria.setUserId(userId);
        criteria.setDealerIds(dealerIds);

        SearchResults<UserContractDoc> results = mock(SearchResults.class);
        when(results.getContent()).thenReturn(Collections.emptyList());

        when(userContractDocService.search(criteria, PageRequest.of(0, Integer.MAX_VALUE))).thenReturn(results);

        CustomerDetailsLink contractRequestsLink = customerDetailsLinkService.getContractRequestsLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE);
        assertNotNull(contractRequestsLink);
        assertEquals("Contract Requests", contractRequestsLink.getName());
        assertEquals(null, contractRequestsLink.getUrl());
        assertEquals(0, contractRequestsLink.getCount());
        assertEquals(CustomerDetailsLinkType.INACTIVE, contractRequestsLink.getStatus());
    }

    @Test
    public void testGetCampaignDomainForNissanBetaCampaign() {
        CampaignView campaignView = new CampaignView();
        campaignView.setId(CustomerDetailsLinkService.NISSAN_LEGACY_BETA_CAMPAIGN_ID);
        String domain = customerDetailsLinkService.getCampaignDomain(campaignView);
        assertEquals(CustomerDetailsLinkService.NISSANATHOME_BETA_CARSAVER_COM, domain);
    }

    @Test
    public void testGetCampaignDomainForNissanProdCampaign() {
        CampaignView campaignView = new CampaignView();
        campaignView.setId(CustomerDetailsLinkService.NISSAN_LEGACY_PROD_CAMPAIGN_ID);
        String domain = customerDetailsLinkService.getCampaignDomain(campaignView);
        assertEquals(CustomerDetailsLinkService.NISSANATHOME_CARSAVER_COM, domain);
    }

    @Test
    public void testGetCampaignDomainForOtherCampaign() {
        CampaignView campaignView = new CampaignView();
        campaignView.setId("other-campaign-id");
        campaignView.setDomain("other-domain.com");
        String domain = customerDetailsLinkService.getCampaignDomain(campaignView);
        assertEquals("other-domain.com", domain);
    }

    @Test
    public void testGetCampaignDomainForNullCampaign() {
        String domain = customerDetailsLinkService.getCampaignDomain(null);
        assertEquals("", domain);
    }

    @Test
    public void testSearches() {
        List<String> dealerIds = List.of("dealer1");
        String userId = "user1";
        when(bigQueryClient.getVehicleSearchCount(dealerIds, userId)).thenReturn(11L);


        CustomerDetailsLink searches = customerDetailsLinkService.getSearchesLink(dealerIds, userId, CustomerDetailsLinkType.ACTIVE, "example.com");

        assertNotNull(searches);
        assertEquals(11, searches.getCount());
        assertEquals("https://ar-example.com/adaptive-retail", searches.getUrl());
    }

    private PreQualRecord createPreQualRecordWithStatus(
        CreditResponseStatusDTO status
    ) {
        return new PreQualRecord(
            "testCustomerId",
            OffsetDateTime.now(),
            status,
            null,
            "testSource",
            "EXPERIAN",
            "testProgram",
            "A",
            OffsetDateTime.now().plusDays(30),
            500
        );
    }
}
