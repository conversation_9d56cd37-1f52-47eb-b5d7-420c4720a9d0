package com.carsaver.partner.service;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.Notification;
import com.carsaver.partner.model.UpdateNotificationEvent;
import com.carsaver.partner.notifications.NotificationClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class NotificationServiceTest {

    @Mock
    private DealerUserService dealerUserService;

    @Mock
    private NotificationClient notificationClient;

    @Mock
    private UserClient userClient;

    @InjectMocks
    private NotificationService notificationService;

    @Test
    void testGetNotificationsForDealerUser_ProgramManager() {
        String dealerId = "dealer123";
        String userId = "user123";

        when(dealerUserService.isProgramManager(dealerId, userId)).thenReturn(true);

        Notification notification1 = new Notification();
        notification1.setUserId("user1");
        notification1.setPriority(1);

        Notification notification2 = new Notification();
        notification2.setUserId("user2");
        notification2.setPriority(2);

        when(notificationClient.fetchNotifications(dealerId))
            .thenReturn(Arrays.asList(notification1, notification2));

        UserView userView1 = new UserView();
        userView1.setFirstName("John");
        userView1.setLastName("Doe");

        UserView userView2 = new UserView();
        userView2.setFirstName("Jane");
        userView2.setLastName("Smith");

        when(userClient.findById("user1")).thenReturn(userView1);
        when(userClient.findById("user2")).thenReturn(userView2);

        List<Notification> result = notificationService.getNotificationsForDealerUser(dealerId, userId);

        assertEquals(2, result.size());
        assertEquals("Jane Smith", result.get(0).getFullName());
        assertEquals("John Doe", result.get(1).getFullName());

        verify(notificationClient, times(1)).fetchNotifications(dealerId);
        verify(userClient, times(1)).findById("user1");
        verify(userClient, times(1)).findById("user2");
    }

    @Test
    void testGetNotificationsForDealerUser_NotProgramManager() {
        String dealerId = "dealer123";
        String userId = "user123";

        when(dealerUserService.isProgramManager(dealerId, userId)).thenReturn(false);

        List<Notification> result = notificationService.getNotificationsForDealerUser(dealerId, userId);

        assertEquals(Collections.emptyList(), result);
        verifyNoInteractions(notificationClient, userClient);
    }

    @Test
    void testUpdateNotification_Displayed() {
        UpdateNotificationEvent event = new UpdateNotificationEvent();
        event.setEventType(NotificationService.DISPLAYED);
        event.setUserId("user123");
        event.setTime("2024-12-22T10:00:00Z");

        notificationService.updateNotification(event);

        verify(notificationClient, times(1)).markAsDisplayed("user123", "2024-12-22T10:00:00Z");
    }

    @Test
    void testUpdateNotification_CtaClicked() {
        UpdateNotificationEvent event = new UpdateNotificationEvent();
        event.setEventType(NotificationService.CTA_CLICKED);
        event.setUserId("user123");
        event.setTime("2024-12-22T10:00:00Z");

        notificationService.updateNotification(event);

        verify(notificationClient, times(1)).markAsCtaClicked("user123", "2024-12-22T10:00:00Z");
    }

    @Test
    void testUpdateNotification_DisplayExpired() {
        UpdateNotificationEvent event = new UpdateNotificationEvent();
        event.setEventType(NotificationService.DISPLAY_EXPIRED);
        event.setUserId("user123");
        event.setTime("2024-12-22T10:00:00Z");

        notificationService.updateNotification(event);

        verify(notificationClient, times(1)).markAsDisplayExpired("user123", "2024-12-22T10:00:00Z");
    }

    @Test
    void testUpdateNotification_InvalidEventType() {
        UpdateNotificationEvent event = new UpdateNotificationEvent();
        event.setEventType("invalidEvent");
        event.setUserId("user123");
        event.setTime("2024-12-22T10:00:00Z");

        Notification notification = notificationService.updateNotification(event);

        assertNull(notification);
    }
}
