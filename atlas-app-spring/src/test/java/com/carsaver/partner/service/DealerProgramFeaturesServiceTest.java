package com.carsaver.partner.service;

import com.carsaver.partner.client.DealerChatConfigClient;
import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.model.DealerChatConfig;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.service.toggle.CarsaverFAndIFeatureToggleHandler;
import com.carsaver.partner.service.toggle.ChatFeatureToggleHandler;
import com.carsaver.partner.service.toggle.DealerTrackFeatureToggleHandler;
import com.carsaver.partner.service.toggle.DisableProtectionProductsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.EmailAlertsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.FeatureToggleHandler;
import com.carsaver.partner.service.toggle.GarageAlertsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.LibertyMutualFeatureToggleHandler;
import com.carsaver.partner.service.toggle.NesnaFinanceAndInsuranceFeatureToggleHandler;
import com.carsaver.partner.service.toggle.RouteOneFinanceAndInsuranceFeatureToggleHandler;
import com.carsaver.partner.service.toggle.SellAtHomeFeatureToggleHandler;
import com.carsaver.partner.service.toggle.SmsAlertsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.SpanishTranslationFeatureToggleHandler;
import com.carsaver.partner.service.toggle.factory.FeatureToggleHandlerFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpSession;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DealerProgramFeaturesServiceTest {

    @Mock
    private DealerChatConfigClient dealerChatConfigClient;

    @Mock
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    @Mock
    private HttpSession session;
    @Mock
    private SplitFeatureFlags splitFeatureFlags;

    @InjectMocks
    private DealerProgramFeaturesService dealerProgramFeaturesService;

    @BeforeEach
    void setUp() {

        FeatureToggleHandler chatFeatureToggleHandler = new ChatFeatureToggleHandler(dealerChatConfigClient);
        FeatureToggleHandler disableProtectionProductsFeatureToggleHandler = new DisableProtectionProductsFeatureToggleHandler(
            featureSubscriptionsClient, session);

        FeatureToggleHandler libertyMutualFeatureToggleHandler = new LibertyMutualFeatureToggleHandler(
            featureSubscriptionsClient);

        FeatureToggleHandler nesnaFinanceAndFeatureToggleHandler = new NesnaFinanceAndInsuranceFeatureToggleHandler(
            featureSubscriptionsClient, session);
        FeatureToggleHandler routeOneFinanceAndFeatureToggleHandler = new RouteOneFinanceAndInsuranceFeatureToggleHandler(
            featureSubscriptionsClient, session);

        FeatureToggleHandler sellAtHomeFeatureToggleHandler = new SellAtHomeFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);

        FeatureToggleHandler inAppAlertsFeatureToggle = new GarageAlertsFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);

        FeatureToggleHandler emailAlertsFeatureToggle = new EmailAlertsFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);

        FeatureToggleHandler garageAlertsFeatureToggle = new GarageAlertsFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);
        FeatureToggleHandler smsAlertsFeatureToggle = new SmsAlertsFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);
        FeatureToggleHandler spanishTranslationFeatureToggle = new SpanishTranslationFeatureToggleHandler(
            featureSubscriptionsClient);
        FeatureToggleHandler carsaverFandIFeature = new CarsaverFAndIFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);
        
        FeatureToggleHandler dealerTrackFeatureToggle = new DealerTrackFeatureToggleHandler(
            featureSubscriptionsClient);

        FeatureToggleHandlerFactory featureToggleHandlerFactory = new FeatureToggleHandlerFactory(
            List.of(
                chatFeatureToggleHandler,
                disableProtectionProductsFeatureToggleHandler,
                libertyMutualFeatureToggleHandler,
                nesnaFinanceAndFeatureToggleHandler,
                routeOneFinanceAndFeatureToggleHandler,
                sellAtHomeFeatureToggleHandler,
                inAppAlertsFeatureToggle,
                emailAlertsFeatureToggle,
                garageAlertsFeatureToggle,
                smsAlertsFeatureToggle,
                spanishTranslationFeatureToggle,
                carsaverFandIFeature,
                dealerTrackFeatureToggle
            )
        );

        dealerProgramFeaturesService = new DealerProgramFeaturesService(featureToggleHandlerFactory);
    }

    @Test
    void testChatFeatureToggleHandler() {
        String configType = "chat";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isChatEnabled(true)
            .build();

        when(dealerChatConfigClient.upsertDealerChatConfig(any())).thenReturn(
            DealerChatConfig.builder().chatEnabled(true).build());

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature( dealerId, programId, toggleConfigRequest);

        assertNotNull(result);
        assertEquals(expectedDealerProgram.getIsChatEnabled(), result.getIsChatEnabled());
    }

    @Test
    void testDisableProtectionProductsFeatureToggleHandler() {
        String configType = "disable_protection_products_global_toggle";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(false);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isRouteOneFAndIEnabled(false)
            .isNesnaFAndIEnabled(false)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder().featureRId("1").dealerId("dealerId").active(false).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature( dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testLibertyMutualFeatureToggleHandler() {
        String configType = "liberty_mutual";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isLibertyMutualEnabled(true)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertNotNull(result);
        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testNesnaFinanceAndInsuranceFeatureToggleHandler() {
        String configType = "nesna_finance_and_insurance";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isNesnaFAndIEnabled(true)
            .isRouteOneFAndIEnabled(false)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }


    @Test
    void testRouteOneFinanceAndInsuranceFeatureToggleHandlerCalled() {
        String configType = "route_one_finance_and_insurance";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isRouteOneFAndIEnabled(true)
            .isNesnaFAndIEnabled(false)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testEmailAlertsFeatureToggleHandler_FeatureDisabled() {
        String configType = "email_alerts";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);
        when(splitFeatureFlags.isEmailAlertsFeatureEnabled(anyString(), anyString())).thenReturn(false);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isEmailAlertsEnabled(true)
            .isEmailAlertsFeatureEnabled(false)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testSmsAlertsFeatureToggleHandler() {
        String configType = "sms_alerts";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);
        when(splitFeatureFlags.isSmsAlertsFeatureEnabled(anyString(), anyString())).thenReturn(true);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isSmsAlertsEnabled(true)
            .isSmsAlertsFeatureEnabled(true)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testSmsAlertsFeatureToggleHandler_FeatureDisabled() {
        String configType = "sms_alerts";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(false);
        toggleConfigRequest.setConfigType(configType);
        when(splitFeatureFlags.isSmsAlertsFeatureEnabled(anyString(), anyString())).thenReturn(false);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isSmsAlertsEnabled(true)
            .isSmsAlertsFeatureEnabled(false)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testGarageAlertsFeatureToggleHandler() {
        String configType = "garage_alerts";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        when(splitFeatureFlags.isGarageAlertsFeatureEnabled(anyString(), anyString())).thenReturn(true);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isGarageAlertsFeatureEnabled(true)
            .isGarageAlertsEnabled(true)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testGarageAlertsFeatureToggleHandler_Disabled() {
        String configType = "garage_alerts";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        when(splitFeatureFlags.isGarageAlertsFeatureEnabled(anyString(), anyString())).thenReturn(false);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isGarageAlertsFeatureEnabled(false)
            .isGarageAlertsEnabled(true)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testSpanishTranslationFeatureToggleHandler() {
        String configType = "spanish_translation";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isSpanishTranslationEnabled(true)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertNotNull(result);
        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testCarsaverFinanceAndInsuranceFeatureToggleHandler() {
        String configType = "carsaver_f_and_i_feature";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isCarsaverFAndIFeatureEnabled(true)
            .isCarsaverFAndIEnabled(true)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(splitFeatureFlags.isCarsaverFAndIEnabled()).thenReturn(true);

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testCarsaverFinanceAndInsuranceFeatureToggle_Disabled() {
        String configType = "carsaver_f_and_i_feature";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isCarsaverFAndIFeatureEnabled(false)
            .isCarsaverFAndIEnabled(true)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(splitFeatureFlags.isCarsaverFAndIEnabled()).thenReturn(false);

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testDealerTrackFeatureToggleHandler() {
        String configType = "dealer_track";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isDealerTrackEnabled(true)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(true).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }

    @Test
    void testDealerTrackFeatureToggleHandlerDisabled() {
        String configType = "dealer_track";
        String dealerId = "dealer123";
        String programId = "program123";
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(false);
        toggleConfigRequest.setConfigType(configType);

        DealerProgram expectedDealerProgram = DealerProgram.builder()
            .isDealerTrackEnabled(false)
            .build();

        FeatureSubscriptionResponse featureSubscriptionResponse = FeatureSubscriptionResponse.builder()
            .featureRId("1").dealerId(dealerId).active(false).build();

        when(featureSubscriptionsClient.saveFeatureSubscription(any())).thenReturn(featureSubscriptionResponse);

        DealerProgram result = dealerProgramFeaturesService.updateDealerProgramFeature(dealerId, programId, toggleConfigRequest);

        assertEquals(expectedDealerProgram, result);
    }
}
