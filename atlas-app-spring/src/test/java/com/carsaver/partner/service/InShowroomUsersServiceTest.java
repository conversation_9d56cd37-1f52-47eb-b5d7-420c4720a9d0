package com.carsaver.partner.service;

import com.carsaver.elasticsearch.model.DealerCheckIn;
import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.elasticsearch.model.program.ProgramDoc;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.connection.DealerConnectionRequest;
import com.carsaver.partner.inshowroom.InShowRoomClient;
import com.carsaver.partner.web.api.user.elasticsearch.model.InShowRoomUsers;
import com.carsaver.search.support.SearchResults;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class InShowroomUsersServiceTest {

    @Mock
    private DealerClient dealerClient;

    @Mock
    private UserClient userClient;

    @Mock
    private SearchResults<UserAndProspectDoc> mockResults;

    @Mock
    private InShowRoomClient inShowRoomClient;

    @InjectMocks
    private InShowroomUsersService inShowroomUsersService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(inShowroomUsersService, "inShowroomMinutesThreshold", 60);
        ReflectionTestUtils.setField(inShowroomUsersService, "inShowroomLeadHourThreshold", 6);
    }

    @Test
    void testFindInShowroomUsers() {
        String dealerId = "dealer123";
        DealerView dealerView = new DealerView();
        dealerView.setTimeZone("America/New_York");
        when(dealerClient.findById(dealerId)).thenReturn(dealerView);

        UserAndProspectDoc user1 = mock(UserAndProspectDoc.class);
        UserAndProspectDoc user2 = mock(UserAndProspectDoc.class);

        DealerCheckIn checkIn1 = mock(DealerCheckIn.class);
        DealerCheckIn checkIn2 = mock(DealerCheckIn.class);
        ProgramDoc programDoc = new ProgramDoc();
        programDoc.setId("program123");

        when(user1.getMostRecentDealerCheckIn()).thenReturn(checkIn1);
        when(user2.getMostRecentDealerCheckIn()).thenReturn(checkIn2);

        when(checkIn1.getCreatedDate()).thenReturn(ZonedDateTime.now().minusMinutes(30));
        when(checkIn2.getCreatedDate()).thenReturn(ZonedDateTime.now().minusMinutes(10));

        when(user1.getId()).thenReturn("user1");
        when(user1.getFirstName()).thenReturn("John");
        when(user1.getLastName()).thenReturn("Doe");
        when(user1.getProgram()).thenReturn(programDoc);

        when(user2.getId()).thenReturn("user2");
        when(user2.getFirstName()).thenReturn("Jane");
        when(user2.getLastName()).thenReturn("Smith");
        when(user2.getProgram()).thenReturn(null);

        UserView userView1 = mock(UserView.class);
        UserView userView2 = mock(UserView.class);



        when(userClient.findById("user1")).thenReturn(userView1);
        when(userClient.findById("user2")).thenReturn(userView2);

        when(mockResults.getContent()).thenReturn(Arrays.asList(user1, user2));

        List<InShowRoomUsers> result = inShowroomUsersService.findInShowroomUsers(mockResults, dealerId);

        assertNotNull(result);
        assertEquals(2, result.size());

        assertEquals("user1", result.get(1).getUserId());
        assertEquals("user2", result.get(0).getUserId());

        assertEquals("program123", result.get(1).getProgramId());
        assertNull(result.get(0).getProgramId());
    }



    @Test
    void testFindInShowroomUsersWhenCheckInIsNull() {
        String dealerId = "dealer123";
        DealerView dealerView = new DealerView();
        dealerView.setTimeZone("America/New_York");

        UserAndProspectDoc userWithoutCheckIn = mock(UserAndProspectDoc.class);
        when(userWithoutCheckIn.getMostRecentDealerCheckIn()).thenReturn(null);

        when(dealerClient.findById(dealerId)).thenReturn(dealerView);
        when(mockResults.getContent()).thenReturn(Collections.singletonList(userWithoutCheckIn));

        List<InShowRoomUsers> result = inShowroomUsersService.findInShowroomUsers(mockResults, dealerId);

        assertTrue(result.isEmpty(), "Result should be empty when all users have no check-ins.");

        verify(dealerClient, times(1)).findById(dealerId);
        verify(userWithoutCheckIn, times(1)).getMostRecentDealerCheckIn();
        verifyNoInteractions(userClient);
    }


    @Test
    void testFindInShowroomUsers_For_New_Leads() {
        DealerView dealerView = new DealerView();
        dealerView.setTimeZone("America/New_York");
        when(dealerClient.findById("dealer123")).thenReturn(dealerView);

        String dealerId  = "dealerId";
        DealerConnectionRequest request = new DealerConnectionRequest();
        request.setUserId("user123");
        request.setCreatedDate(ZonedDateTime.now().minusMinutes(45));

        UserView userView = mock(UserView.class);
        CampaignView campaignView = mock(CampaignView.class);
        when(campaignView.getProgramId()).thenReturn("program123");
        campaignView.setProgramId("program123");
        when(userView.getId()).thenReturn("user123");
        when(userView.getFirstName()).thenReturn("John");
        when(userView.getLastName()).thenReturn("Doe");
        when(userView.getFullName()).thenReturn("John Doe");


        when(userView.getCampaign()).thenReturn(campaignView);
        when(inShowRoomClient.getDealerConnectionsByType(any(), anyString(), any())).thenReturn(List.of(request));
        when(userClient.findById("user123")).thenReturn(userView);

        List<InShowRoomUsers> users = inShowroomUsersService.findInShowroomUsers("dealer123");

        assertNotNull(users);
        assertEquals(1, users.size());
        assertEquals("John Doe", users.get(0).getFullName());
        assertEquals("program123", users.get(0).getProgramId());
    }

    @Test
    void testFindInShowroomUsers_For_Older_Than_240_Mins_Leads() {
        ReflectionTestUtils.setField(inShowroomUsersService, "inShowroomMinutesThreshold", 240);
        ReflectionTestUtils.setField(inShowroomUsersService, "inShowroomLeadHourThreshold", 6);
        DealerView dealerView = new DealerView();
        dealerView.setTimeZone("America/New_York");
        when(dealerClient.findById("dealer123")).thenReturn(dealerView);

        DealerConnectionRequest request = new DealerConnectionRequest();
        request.setUserId("user123");
        request.setCreatedDate(ZonedDateTime.now().minusMinutes(300));

        UserView userView = mock(UserView.class);
        CampaignView campaignView = mock(CampaignView.class);
        when(campaignView.getProgramId()).thenReturn("program123");
        campaignView.setProgramId("program123");
        when(userView.getId()).thenReturn("user123");
        when(userView.getFirstName()).thenReturn("John");
        when(userView.getLastName()).thenReturn("Doe");
        when(userView.getFullName()).thenReturn("John Doe");


        when(userView.getCampaign()).thenReturn(campaignView);
        when(inShowRoomClient.getDealerConnectionsByType(any(), any(), any())).thenReturn(List.of(request));
        when(userClient.findById("user123")).thenReturn(userView);

        List<InShowRoomUsers> users = inShowroomUsersService.findInShowroomUsers("dealer123");

        assertNotNull(users);
        assertEquals(0, users.size());
    }


    @Test
    void testFindInShowroomUsers_NotFound() {
        ReflectionTestUtils.setField(inShowroomUsersService, "inShowroomMinutesThreshold", 240);
        ReflectionTestUtils.setField(inShowroomUsersService, "inShowroomLeadHourThreshold", 6);
        DealerView dealerView = new DealerView();
        dealerView.setTimeZone("America/New_York");
        when(dealerClient.findById("dealer123")).thenReturn(dealerView);

        DealerConnectionRequest request = new DealerConnectionRequest();
        request.setUserId("user123");
        request.setCreatedDate(ZonedDateTime.now().minusMinutes(300));

        UserView userView = mock(UserView.class);
        CampaignView campaignView = mock(CampaignView.class);
        when(campaignView.getProgramId()).thenReturn("program123");
        campaignView.setProgramId("program123");
        when(userView.getId()).thenReturn("user123");
        when(userView.getFirstName()).thenReturn("John");
        when(userView.getLastName()).thenReturn("Doe");
        when(userView.getFullName()).thenReturn("John Doe");


        when(userView.getCampaign()).thenReturn(campaignView);
        String dealerId ="dealerId";
        when(inShowRoomClient.getDealerConnectionsByType(any(), eq(dealerId), any())).thenReturn(List.of(request));
        when(userClient.findById("user123")).thenReturn(null);

        List<InShowRoomUsers> users = inShowroomUsersService.findInShowroomUsers("dealer123");

        assertNotNull(users);
        assertEquals(0, users.size());
    }



}
