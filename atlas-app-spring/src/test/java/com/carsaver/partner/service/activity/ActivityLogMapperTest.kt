package com.carsaver.partner.service.activity

import com.carsaver.partner.TestUtils
import com.carsaver.partner.client.activity.ActivityLog
import com.carsaver.partner.client.dealer.CertificateV2
import com.carsaver.partner.client.dealer.DealerClient
import com.carsaver.partner.client.dealer.DealerV2
import com.carsaver.partner.client.digitalretail.ChatHistorySummary
import com.carsaver.partner.client.inventory.v2.InventoryClientV2
import com.carsaver.partner.client.inventory.v2.InventoryVehicleV2
import com.carsaver.partner.client.leads.v2.LeadClientV2
import com.carsaver.partner.client.leads.v2.LeadV2
import com.carsaver.partner.client.vehicle.StyleSummaryV2
import com.carsaver.partner.client.vehicle.VehicleClientV2
import com.carsaver.partner.service.activity.ActivityLogMapper.Companion.getCertificateId
import com.carsaver.partner.service.activity.ActivityLogMapper.Companion.safeGetMetadataAsInt
import com.carsaver.partner.service.activity.ActivityLogMapperTest.Companion.CAMPAIGN_ID
import com.carsaver.partner.service.activity.ActivityLogMapperTest.Companion.INVENTORY_ID
import com.carsaver.partner.service.activity.ActivityLogsServiceTest.Companion.DEALER_ID
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.whenever
import java.time.LocalDateTime
import java.util.function.Consumer

internal class ActivityLogMapperTest {
    private val leadsClientV2: LeadClientV2 = Mockito.mock(LeadClientV2::class.java)
    private val dealerClient: DealerClient = Mockito.mock(
        DealerClient::class.java
    )
    private val inventoryClient: InventoryClientV2 = Mockito.mock(InventoryClientV2::class.java)
    private val vehicleClientV2: VehicleClientV2 = Mockito.mock(VehicleClientV2::class.java)

    private val mapper = ActivityLogMapper(leadsClientV2, dealerClient, inventoryClient, vehicleClientV2)
    private val dealer = DealerV2(timeZone = "America/New_York")

    @Test
    fun mapPreQual() {
        val map = mapper.map(PRE_QUAL_COMPLETED, dealer, listOf(), "userId")
        Assertions.assertTrue(map.stream().allMatch { log: ActivityLogDTO -> log.preQualStatus == "Success" })
        println(map)
    }

    @Test
    fun testActivityType() {
        helper(ACCOUNT_CREATED, ActivityType.ACCOUNT_CREATED)
        helper(DEAL_SAVED, ActivityType.DEAL_SAVED)
        helper(FINANCE_SUBMITTED, ActivityType.CREDIT_APPLICATION_SUBMITTED)
        helper(LEAD_SENT_ADF_TO_CRM, ActivityType.LEAD_SENT)
        helper(LEAD_SENT_ADF_TO_NISSAN, ActivityType.LEAD_SENT)
        helper(LEAD_SET_ADF_PREFLIGHT_TO_CRM, ActivityType.LEAD_SENT)
        helper(LOGIN, ActivityType.LOGIN)
        helper(PRE_QUAL_COMPLETED, ActivityType.PRE_QUAL_COMPLETED)
        helper(VEHICLE_VIEWED, ActivityType.VEHICLE_VIEWED)
        helper(CHECKIN, ActivityType.CHECKIN)
        helper(WEBSITE_VISITS_SUCCESSFUL_LOGIN, ActivityType.WEBSITE_VISIT_SUCCESSFUL_LOGIN)
    }

    @Test
    fun testHasCampaignId() {
        hasCampaignId(PRE_QUAL_COMPLETED)
        hasCampaignId(DEAL_SAVED)
        hasCampaignId(FINANCE_SUBMITTED)
        hasCampaignId(LOGIN)
        hasCampaignId(VEHICLE_VIEWED)
        hasCampaignId(CHECKIN)
    }

    @Test
    fun testHasCertificateId() {
        hasCertificated(DEAL_SAVED)
        hasCertificated(FINANCE_SUBMITTED)
        hasCertificated(VEHICLE_VIEWED)
    }


    @Test
    fun testCertWithoutInventoryId() {
        val lead = TestUtils.generate(LeadV2::class.java)
        lead.certificateId = CERTIFICATE_ID
        whenever(leadsClientV2.getLeadById(LEAD_ID)).thenReturn(lead)

        val cert = TestUtils.generate(CertificateV2::class.java)
        cert.inventoryId = null
        cert.source!!.campaignId = CAMPAIGN_ID
        whenever(dealerClient.getCertificateById(CERTIFICATE_ID)).thenReturn(cert)

        val vehicle = TestUtils.generate(
            InventoryVehicleV2::class.java
        )
        vehicle.active = true
        vehicle.styleId = STYLE_ID
        vehicle.vin = VIN
        whenever(inventoryClient.getInventoryVehicleById(INVENTORY_ID)).thenReturn(vehicle)

        val styleSummary = TestUtils.generate(StyleSummaryV2::class.java)
        styleSummary.make = "make"
        styleSummary.model = "model"
        styleSummary.year = 2025
        whenever(vehicleClientV2.getStyleByStyleId(STYLE_ID)).thenReturn(styleSummary)

        val generate = TestUtils.generate(ActivityLog::class.java)
        generate.leadId = LEAD_ID
        generate.eventType = "email"
        generate.eventName = "adf-to-nissan-digital-shift"
        val actual = mapper.map(listOf(generate), dealer, chatHistory(), "userId")

        Assertions.assertEquals(2, actual.size)
        val activityLogDTO = actual[0]
        Assertions.assertEquals(ActivityType.LEAD_SENT, activityLogDTO.activityType)
        Assertions.assertEquals(null, activityLogDTO.campaignId)
        Assertions.assertEquals(null, activityLogDTO.vehicleId)
        Assertions.assertEquals(null, activityLogDTO.vin)
        Assertions.assertEquals(null, activityLogDTO.styleId)
        Assertions.assertEquals(null, activityLogDTO.make)
        Assertions.assertEquals(null, activityLogDTO.model)
        Assertions.assertEquals(null, activityLogDTO.year)
        Assertions.assertEquals(CERTIFICATE_ID, activityLogDTO.certificateId)
        Assertions.assertEquals("America/New_York", activityLogDTO.eventTime!!.zone.id) // time is changed
    }


    @Test
    fun testRetrieveData() {
        val lead = TestUtils.generate(LeadV2::class.java)
        lead.certificateId = CERTIFICATE_ID
        whenever(leadsClientV2.getLeadById(LEAD_ID)).thenReturn(lead)

        val cert = TestUtils.generate(CertificateV2::class.java)
        cert.inventoryId = INVENTORY_ID
        cert.source!!.campaignId = CAMPAIGN_ID
        whenever(dealerClient.getCertificateById(CERTIFICATE_ID)).thenReturn(cert)

        val vehicle = TestUtils.generate(
            InventoryVehicleV2::class.java
        )
        vehicle.active = true
        vehicle.styleId = STYLE_ID
        vehicle.vin = VIN
        whenever(inventoryClient.getInventoryVehicleById(INVENTORY_ID)).thenReturn(vehicle)

        val styleSummary = TestUtils.generate(StyleSummaryV2::class.java)
        styleSummary.make = "make"
        styleSummary.model = "model"
        styleSummary.year = 2025
        whenever(vehicleClientV2.getStyleByStyleId(STYLE_ID)).thenReturn(styleSummary)

        val generate = TestUtils.generate(ActivityLog::class.java)
        generate.leadId = LEAD_ID
        generate.eventType = "email"
        generate.eventName = "adf-to-nissan-digital-shift"
        val actual = mapper.map(listOf(generate), dealer, chatHistory(), "userId")

        Assertions.assertEquals(2, actual.size)
        val activityLogDTO = actual[0]
        Assertions.assertEquals(ActivityType.LEAD_SENT, activityLogDTO.activityType)
        Assertions.assertEquals(CAMPAIGN_ID, activityLogDTO.campaignId)
        Assertions.assertEquals(INVENTORY_ID, activityLogDTO.vehicleId)
        Assertions.assertEquals(VIN, activityLogDTO.vin)
        Assertions.assertEquals(STYLE_ID, activityLogDTO.styleId as Int)
        Assertions.assertEquals("make", activityLogDTO.make)
        Assertions.assertEquals("model", activityLogDTO.model)
        Assertions.assertEquals(2025, activityLogDTO.year)
        Assertions.assertEquals(CERTIFICATE_ID, activityLogDTO.certificateId)
        Assertions.assertEquals("America/New_York", activityLogDTO.eventTime!!.zone.id) // time is changed
    }

    @Test
    fun safeGetMetadataAsInt() {
        val activityLog = ActivityLog()
        activityLog.metadata = mapOf("styleId" to 123)
        Assertions.assertEquals(123, safeGetMetadataAsInt(activityLog, "styleId"))
    }

    @Test
    fun certificateId() {
        val log = ActivityLog()
        log.metadata = mapOf("certificateId" to 123L)
        Assertions.assertEquals(123L, getCertificateId(log))
    }

    private fun helper(logs: List<ActivityLog>, type: ActivityType) {
        val map = mapper.map(logs, dealer, listOf(), "userId")
        map.forEach(Consumer { log: ActivityLogDTO ->
            Assertions.assertEquals(
                type,
                log.activityType
            )
        })
        map.forEach(Consumer { log: ActivityLogDTO ->
            Assertions.assertTrue(
                log.dealerId?.isNotBlank() == true
            )
        })
        map.forEach(Consumer { log: ActivityLogDTO ->
            Assertions.assertNotNull(
                log.eventTime
            )
        })
    }

    private fun hasCampaignId(logs: List<ActivityLog>) {
        val map = mapper.map(logs, dealer, chatHistory(), "userId")
        Assertions.assertTrue(map.stream().anyMatch { log: ActivityLogDTO ->
                log.campaignId?.isNotBlank() == true
        })
    }

    private fun hasCertificated(logs: List<ActivityLog>) {
        val map = mapper.map(logs, dealer, chatHistory(), "userId")
        Assertions.assertTrue(map.stream().anyMatch { log: ActivityLogDTO -> log.certificateId != null })
    }

    companion object {
        const val LEAD_ID: String = "leadId"
        const val INVENTORY_ID: String = "inventoryId"
        const val CAMPAIGN_ID: String = "campaignId"
        const val STYLE_ID: Int = 121
        const val VIN: String = "vin"
        const val CERTIFICATE_ID: Long = 150L
        private val ACCOUNT_CREATED: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/account-created.json")
        private val DEAL_SAVED: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/deal-saved.json")
        private val FINANCE_SUBMITTED: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/finance-submitted.json")
        private val LEAD_SENT_ADF_TO_CRM: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/lead-sent-adf-to-crm.json")
        private val LEAD_SENT_ADF_TO_NISSAN: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/lead-sent-adf-to-nissan.json")
        private val LEAD_SET_ADF_PREFLIGHT_TO_CRM: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/lead-set-adf-preflight-to-crm.json")
        private val LOGIN: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/login.json")
        private val PRE_QUAL_COMPLETED: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/pre-qual-completed.json")
        private val VEHICLE_VIEWED: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/vehicle-viewed.json")
        private val CHECKIN: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/checkin.json")
        private val WEBSITE_VISITS_SUCCESSFUL_LOGIN: List<ActivityLog> =
            ActivityTestUtils.loadJsonFileAsCamelCaseMap("src/test/resources/digital-retail-logs/website-visits-successful-login.json")
    }
}

fun chatHistory(startTime: LocalDateTime = LocalDateTime.now()): List<ChatHistorySummary> {
    return listOf(ChatHistorySummary(
        startTime = startTime,
        endTime = LocalDateTime.now(),
        activityName = "activity",
        chatContentLocation = "chatContentLocation",
        dealerId = DEALER_ID,
        campaignId = CAMPAIGN_ID,
        vehicleId = INVENTORY_ID,
    ))
}
