package com.carsaver.partner.service.activity

import com.carsaver.magellan.auth.CarSaverJWTToken
import com.carsaver.magellan.auth.TokenResponse
import com.carsaver.partner.AtlasApplication
import com.carsaver.partner.TestUtils
import com.carsaver.partner.client.activity.ActivityClient
import com.carsaver.partner.client.dealer.DealerClient
import com.carsaver.partner.client.digitalretail.DigitalRetailClient
import com.carsaver.partner.client.inventory.v2.InventoryClientV2
import com.carsaver.partner.client.leads.v2.LeadClientV2
import com.carsaver.partner.client.oauth.OAuthClient
import com.carsaver.partner.client.vehicle.VehicleClientV2
import com.carsaver.partner.http.HttpService
import lombok.SneakyThrows
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.core.context.SecurityContextImpl
import java.io.File
import java.util.function.Consumer

@Tag("integration")
internal class ActivityIntegrationTest @SneakyThrows constructor() {
    private val oauthClient: OAuthClient
    private val leadClientV2: LeadClientV2
    private val dealerClient: DealerClient
    private val inventoryClientV2: InventoryClientV2
    private val vehicleClientV2: VehicleClientV2
    private val service: ActivityLogsService
    private val activityClient: ActivityClient
    private val digitalRetail: DigitalRetailClient


    init {
        val httpService = HttpService(AtlasApplication.client())

        val properties = TestUtils.getBetaOauthProperties()

        this.oauthClient = OAuthClient(httpService, properties)

        this.leadClientV2 = LeadClientV2("https://api-beta.carsaver.com/lead", httpService, oauthClient)
        this.dealerClient = DealerClient("https://api-beta.carsaver.com/dealer", httpService, oauthClient)
        this.inventoryClientV2 = InventoryClientV2("https://api-beta.carsaver.com/inventory", httpService, oauthClient)
        this.vehicleClientV2 = VehicleClientV2("https://api-beta.carsaver.com/vehicle", httpService, oauthClient)
        this.activityClient = ActivityClient("https://api-beta.carsaver.com/activity", httpService, oauthClient)
        this.digitalRetail =
            DigitalRetailClient("https://api-beta.carsaver.com/digital-retail", httpService, oauthClient)

        this.service = ActivityLogsService(
            activityClient,
            dealerClient,
            ActivityLogMapper(leadClientV2, dealerClient, inventoryClientV2, vehicleClientV2),
            digitalRetail
        )
    }

    @Tag("integration")
    @Test
    fun test() {
        val response = TokenResponse()
        response.accessToken = File("test-access-token").readText()
        SecurityContextHolder.setContext(
            SecurityContextImpl(
                CarSaverJWTToken(
                    response, listOf()
                )
            )
        )

        val process = service.process("22ce97b6-3823-4f62-9f53-3b047ad698c0", listOf("500f87d74af4b50002000027"))
        process.forEach(Consumer { x: DigitalRetailActivityLogContainer? ->
            println(
                x
            )
        })
    }

    @Test
    fun deals() {
    }

    @Test
    fun inventory() {
    }
}
