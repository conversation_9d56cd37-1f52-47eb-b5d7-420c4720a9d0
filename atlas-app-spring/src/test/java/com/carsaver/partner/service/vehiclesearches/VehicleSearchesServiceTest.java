package com.carsaver.partner.service.vehiclesearches;

import com.carsaver.partner.client.bigquery.BigQueryClient;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.unitils.reflectionassert.ReflectionAssert.assertReflectionEquals;

class VehicleSearchesServiceTest {

    @Test
    @SneakyThrows
    void test() {
        ObjectMapper mapper = new ObjectMapper();
        List<Map<String, Object>> maps = mapper.readValue(new File("src/test/resources/json/bigQuery/data.json"),
            new TypeReference<>() {
            });
        BigQueryClient bigQueryClient = mock(BigQueryClient.class);
        when(bigQueryClient.getVehicleSearchesData("userId", "dealerId")).thenReturn(maps);
        VehicleSearchesService service = new VehicleSearchesService(bigQueryClient);

        List<VehicleSearches> map = service.process("userId", "dealerId");
        assertReflectionEquals(List.of(
            new VehicleSearches(
                Map.of("models", List.of("Kicks"), "makes", List.of("Nissan"), "stockTypes", List.of("NEW")),
                LocalDateTime.of(2024, 11, 26, 16, 0, 33, 442_000_000),
                6, "https://nissanathome.carsaver.com/buy/search-results-page?models=Kicks&" +
                "dealerId=b9c1c697-d17e-4cdc-a82c-a84d1f4d7157&makes=Nissan&stockTypes=NEW"
            ),
            new VehicleSearches(
                Map.of("makes", List.of("Nissan"), "stockTypes", List.of("NEW")),
                LocalDateTime.of(2024, 11, 26, 15, 59, 49, 258_000_000),
                4, "https://nissanathome.carsaver.com/buy/search-results-page?" +
                "dealerId=b9c1c697-d17e-4cdc-a82c-a84d1f4d7157&makes=Nissan&stockTypes=NEW"
            ),
            new VehicleSearches(
                Map.of("makes", List.of("Nissan"), "stockTypes", List.of("NEW"), "years", List.of("2023", "2024")),
                LocalDateTime.of(2024, 11, 26, 15, 59, 38, 476000000),
                1, "https://nissanathome.carsaver.com/buy/search-results-page?" +
                "dealerId=b9c1c697-d17e-4cdc-a82c-a84d1f4d7157&makes=Nissan&stockTypes=NEW&years=2023%2C2024"
            )
        ), map);
    }

}
