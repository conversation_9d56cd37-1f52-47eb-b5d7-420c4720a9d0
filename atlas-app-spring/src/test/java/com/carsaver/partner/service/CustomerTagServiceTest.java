package com.carsaver.partner.service;

import com.carsaver.partner.client.customer.CustomerTagsClient;
import com.carsaver.partner.model.retail.CustomerTagsResponse;
import com.carsaver.partner.model.retail.UserTags;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

public class CustomerTagServiceTest {

    public static final String PREVIOUS_CUSTOMER = "Previous Customer";
    public static final String GUARANTEED_TRADE_VALUE = "Guaranteed Trade Value";
    public static final String PRE_QUALIFIED = "Pre-Qualified";

    @InjectMocks
    private CustomerTagService customerTagService;

    @Mock
    private CustomerTagsClient customerTagsClient;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testFetchCustomerTags() {
        String customerId = "12345";
        List<String> dealerIds = List.of("1", "2");
        CustomerTagsResponse expectedResponse = new CustomerTagsResponse();
        expectedResponse.setCustomerTags(List.of(UserTags.builder().tagId(1).name("TRADE").userId("user1").createdDate(null).build()));

        when(customerTagsClient.fetchCustomerTags(customerId, dealerIds)).thenReturn(expectedResponse);

        CustomerTagsResponse actualResponse = customerTagService.fetchCustomerTags(customerId, dealerIds);
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    void testFetchCustomerTagsWithDuplicateTagIds() {
        String customerId = "12345";
        List<String> dealerIds = List.of("1", "2");

        CustomerTagsResponse mockResponse = new CustomerTagsResponse();
        mockResponse.setCustomerTags(List.of(
            UserTags.builder().tagId(25).name(PREVIOUS_CUSTOMER).userId("user1").userId("user1").build(),
            UserTags.builder().tagId(21).tagId(20).name(GUARANTEED_TRADE_VALUE).userId("user1").build(),
            UserTags.builder().tagId(22).name(PRE_QUALIFIED).dealerId("1").userId("user1").userId("user1").build(),
            UserTags.builder().tagId(22).name(PRE_QUALIFIED).dealerId("2").userId("user1").build()
        ));

        when(customerTagsClient.fetchCustomerTags(customerId, dealerIds)).thenReturn(mockResponse);

        CustomerTagsResponse actualResponse = customerTagService.fetchCustomerTags(customerId, dealerIds);

        assertEquals(PREVIOUS_CUSTOMER, actualResponse.getCustomerTags().get(0).getName());
        assertEquals(GUARANTEED_TRADE_VALUE, actualResponse.getCustomerTags().get(1).getName());
        assertEquals(PRE_QUALIFIED, actualResponse.getCustomerTags().get(2).getName());
        assertEquals(3, actualResponse.getCustomerTags().size());
    }
}
