package com.carsaver.partner.service;

import com.carsaver.magellan.client.DealerFinancierClient;
import com.carsaver.magellan.model.dealer.DealerFinancierModel;
import com.carsaver.magellan.model.dealer.DealerFinancierModelView;
import com.carsaver.partner.repository.LenderDesk;
import com.carsaver.partner.repository.LenderDeskRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LenderDeskServiceTest {

    @Mock
    private LenderDeskRepository lenderDeskRepository;

    @Mock
    private DealerFinancierClient dealerFinancierClient;

    @InjectMocks
    private LenderDeskService lenderDeskService;

    @Test
    void testGetDealerFinanciersByDealerId() {
        String dealerId = "dealer1";
        DealerFinancierModel financier1 = new DealerFinancierModel();
        financier1.setFinancierId(1001);
        DealerFinancierModel financier2 = new DealerFinancierModel();
        financier2.setFinancierId(1002);

        List<DealerFinancierModel> dealerFinancierModels = Arrays.asList(financier1, financier2);
        DealerFinancierModelView dealerFinancierModelView= new DealerFinancierModelView();
        dealerFinancierModelView.setLmsPreference("DealerTrack");
        dealerFinancierModelView.setDealerFinancierModels(dealerFinancierModels);

        when(dealerFinancierClient.getDealerFinanciersByDealerId(dealerId))
            .thenReturn(dealerFinancierModelView);
        when(lenderDeskRepository.findByFinancierId(1001))
            .thenReturn(new LenderDesk(1001,"Financer1",Collections.singletonList("code1")));
        when(lenderDeskRepository.findByFinancierId(1002))
            .thenReturn(new LenderDesk(1002,"Financer2",Collections.emptyList()));

        DealerFinancierModelView financiers = lenderDeskService.getDealerFinanciersByDealerId(dealerId);

        verify(dealerFinancierClient).getDealerFinanciersByDealerId(dealerId);
        verify(lenderDeskRepository).findByFinancierId(1001);
        verify(lenderDeskRepository).findByFinancierId(1002);

        assertTrue(financiers.getDealerFinancierModels().get(0).isLenderDeskCodesConfigured());
        assertFalse(financiers.getDealerFinancierModels().get(1).isLenderDeskCodesConfigured());
    }

    @Test
    void testRefreshDealerFinanciersByDealerId() {
        // Prepare test data
        String dealerId = "dealer2";
        DealerFinancierModel financier1 = new DealerFinancierModel();
        financier1.setFinancierId(1001);
        DealerFinancierModel financier2 = new DealerFinancierModel();
        financier2.setFinancierId(null);

        when(dealerFinancierClient.refreshDealerFinanciersByDealerId(dealerId))
            .thenReturn(Arrays.asList(financier1, financier2));
        when(lenderDeskRepository.findByFinancierId(1001))
            .thenReturn(new LenderDesk(1001,"Financer1",Collections.singletonList("code1")));

        List<DealerFinancierModel> financiers = lenderDeskService.refreshDealerFinanciersByDealerId(dealerId);

        verify(dealerFinancierClient).refreshDealerFinanciersByDealerId(dealerId);
        verify(lenderDeskRepository).findByFinancierId(1001);

        assertTrue(financiers.get(0).isLenderDeskCodesConfigured());
        assertFalse(financiers.get(1).isLenderDeskCodesConfigured());
    }
}
