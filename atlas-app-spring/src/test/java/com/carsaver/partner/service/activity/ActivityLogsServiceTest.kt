package com.carsaver.partner.service.activity

import com.carsaver.partner.client.activity.ActivityClient
import com.carsaver.partner.client.dealer.DealerClient
import com.carsaver.partner.client.dealer.DealerV2
import com.carsaver.partner.client.digitalretail.ChatComment
import com.carsaver.partner.client.digitalretail.ChatHistoryItem
import com.carsaver.partner.client.digitalretail.DealerTokenExchangeResponse
import com.carsaver.partner.client.digitalretail.DigitalRetailClient
import com.carsaver.partner.client.digitalretail.Type
import com.carsaver.partner.client.oauth.OAuthToken
import com.carsaver.partner.exception.BadRequestException
import com.carsaver.partner.exception.ConflictException
import com.carsaver.partner.exception.NotFoundException
import com.carsaver.partner.model.user.UserView
import com.carsaver.partner.service.activity.ActivityLogMapperTest.Companion.CAMPAIGN_ID
import com.carsaver.partner.service.activity.ActivityLogMapperTest.Companion.INVENTORY_ID
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.unitils.reflectionassert.ReflectionAssert.assertReflectionEquals
import java.io.File
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime

internal class ActivityLogsServiceTest {
    private val activityClient: ActivityClient = Mockito.mock(ActivityClient::class.java)
    private val dealerClient: DealerClient = Mockito.mock(
        DealerClient::class.java
    )
    private val activityLogMapper: ActivityLogMapper = Mockito.mock(ActivityLogMapper::class.java)
    private val digitalRetailClient: DigitalRetailClient = Mockito.mock(DigitalRetailClient::class.java)

    private val service = ActivityLogsService(
        activityClient, dealerClient, activityLogMapper, digitalRetailClient
    )

    private val mapper = jacksonObjectMapper()
        .findAndRegisterModules()
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)

    @Test
    fun test() {

        val list = listOf(

            createDto(ActivityType.DEAL_SAVED, 1), // website
            createDto(ActivityType.VEHICLE_VIEWED, 2), // other website

            createDto(ActivityType.CHECKIN, 3), // showroom starts
            createDto(ActivityType.ACCOUNT_CREATED, 4),
            createDto(ActivityType.LEAD_SENT, 5),
            createDto(ActivityType.LEAD_SENT, 6),

            createDto(ActivityType.CHECKIN, 7), // showroom starts
            createDto(ActivityType.LEAD_SENT, 8),
            createDto(ActivityType.LEAD_SENT, 9),
            createDto(ActivityType.LEAD_SENT, 10),
            createDto(ActivityType.LEAD_SENT, 11),
            createDto(ActivityType.LEAD_SENT, 12),
            createDto(ActivityType.WEBSITE_VISIT_SUCCESSFUL_LOGIN, 13),
            createDto(ActivityType.CHAT, 14),

            // not show room (website again)
            createDto(ActivityType.CHECKIN, 23, day = 2), // show room
            createDto(ActivityType.LEAD_SENT, 1, day = 3), // back to website

        )

        assertThrows<BadRequestException> { service.process(USER_ID, listOf(DEALER_ID, "other")) }
        assertThrows<BadRequestException> { service.process(USER_ID, listOf()) }
        assertThrows<NotFoundException> { service.process(USER_ID, listOf(DEALER_ID)) }

        whenever(dealerClient.getDealerById(any())).thenReturn(
            DealerV2(
                timeZone = "America/New_York",
                websiteUrl = "https://dealer.com"
            )
        )
        whenever(digitalRetailClient.exchangeDealerToken(any())).thenReturn(
            DealerTokenExchangeResponse(
                UserView(), OAuthToken().apply { accessToken = "some-access-token" }
            ))
        whenever(digitalRetailClient.getAllChatHistory(any())).thenReturn(
            chatHistory(list[0].eventTime!!.toLocalDateTime())
        )

        assertTrue(service.process(USER_ID, listOf(DEALER_ID)).isEmpty())

        whenever(activityClient.getDigitalRetailLogsByUserIdAndDealerIds(any(), any())).thenReturn(listOf())
        whenever(activityLogMapper.map(any(), any(), any(), any())).thenReturn(list)

        var actual = service.process(USER_ID, listOf(DEALER_ID))
        assertEquals("dealer.com", actual[0]?.dealerWebsite)

        list.last().origin = "nissanusa"
        actual = service.process(USER_ID, listOf(DEALER_ID))
        assertEquals("NissanUSA.com", actual[0]?.dealerWebsite)

        val file = File("src/test/resources/digital-retail-logs/expected.json")
        val expected = mapper.readValue<List<DigitalRetailActivityLogContainer>>(file)

        assertEquals(expected, actual)


    }

    companion object {
        const val USER_ID: String = "userId"
        const val DEALER_ID: String = "dealerId"
    }

    @Test
    fun testGroupLogsByDayAndType() {

        val list = listOf(

            createDto(ActivityType.DEAL_SAVED, 1), // website
            createDto(ActivityType.VEHICLE_VIEWED, 2), // other website

            createDto(ActivityType.CHECKIN, 3), // showroom starts
            createDto(ActivityType.ACCOUNT_CREATED, 4),
            createDto(ActivityType.LEAD_SENT, 5),
            createDto(ActivityType.LEAD_SENT, 6),

            createDto(ActivityType.CHECKIN, 7), // showroom starts
            createDto(ActivityType.LEAD_SENT, 8),
            createDto(ActivityType.LEAD_SENT, 9),
            createDto(ActivityType.LEAD_SENT, 10),
            createDto(ActivityType.LEAD_SENT, 11),
            createDto(ActivityType.LEAD_SENT, 12),

            // not show room (website again)
            createDto(ActivityType.CHECKIN, 23, day = 2), // show room
            createDto(ActivityType.LEAD_SENT, 1, day = 3), // back to website

        )

        val result = ActivityLogsService.groupLogsByDaysAndType(
            list.shuffled()
        )

        assertEquals(5, result.size)
        assertEquals(listOf(createDto(ActivityType.LEAD_SENT, 1, day = 3)), result[0])
        assertEquals(listOf(createDto(ActivityType.CHECKIN, 23, day = 2)), result[1])
        assertReflectionEquals(
            listOf(
                createDto(ActivityType.CHECKIN, 7), // showroom starts
                createDto(ActivityType.LEAD_SENT, 8),
                createDto(ActivityType.LEAD_SENT, 9),
                createDto(ActivityType.LEAD_SENT, 10),

                ), result[2]
        )
        assertEquals(
            listOf(
                createDto(ActivityType.CHECKIN, 3), // showroom starts
                createDto(ActivityType.ACCOUNT_CREATED, 4),
                createDto(ActivityType.LEAD_SENT, 5),
                createDto(ActivityType.LEAD_SENT, 6)
            ), result[3]
        )

        assertReflectionEquals(
            listOf(
                createDto(ActivityType.DEAL_SAVED, 1), // website
                createDto(ActivityType.VEHICLE_VIEWED, 2), // other website
                createDto(ActivityType.LEAD_SENT, 11),
                createDto(ActivityType.LEAD_SENT, 12),
            ), result[4]
        )
    }

    private fun createDto(type: ActivityType, hour: Int, day: Int = 1): ActivityLogDTO {
        return ActivityLogDTO(
            dealerId = DEALER_ID,
            activityType = type,
            eventTime = ZonedDateTime.of(2021, 1, day, hour, 0, 0, 0, ZoneId.of("UTC")),
        )
    }

    @Test
    fun retrieveChatLog() {
        val date = ZonedDateTime.of(2021, 1, 1, 1, 0, 0, 0, ZoneId.of("UTC"))

        assertThrows<NotFoundException> {
            service.retrieveChatLog(USER_ID, DEALER_ID, date)
        }

        whenever(dealerClient.getDealerById(any())).thenReturn(DealerV2())

        assertThrows<ConflictException> {
            service.retrieveChatLog(USER_ID, DEALER_ID, date)
        }

        whenever(dealerClient.getDealerById(any())).thenReturn(DealerV2(timeZone = "America/New_York"))
        whenever(digitalRetailClient.exchangeDealerToken(any())).thenReturn(
            DealerTokenExchangeResponse(
                UserView(), OAuthToken().apply { accessToken = "some-access-token" }
            ))
        whenever(digitalRetailClient.getChatHistoryContent(any(), any())).thenReturn(
            ChatHistoryItem(
                startTime = date.toLocalDateTime(),
                endTime = date.toLocalDateTime().plusDays(1),
                activityName = "activity",
                chatContent = listOf(
                    ChatComment(
                        Type.AGENT, "content"
                    )
                ),
                dealerId = DEALER_ID,
                campaignId = CAMPAIGN_ID,
                vehicleId = INVENTORY_ID,
            )
        )
        val actual = service.retrieveChatLog(USER_ID, DEALER_ID, date)
        assertEquals(
            ChatHistoryContentResponse(
                activityLog = ActivityLogDTO(
                    activityType = ActivityType.CHAT,
                    userId = "userId",
                    campaignId = "campaignId",
                    vehicleId = "inventoryId",
                    vin = null,
                    styleId = null,
                    dealerId = "dealerId",
                    eventTime = ZonedDateTime.parse("2020-12-31T20:00-05:00[America/New_York]"),
                    certificateId = null,
                    leadId = null,
                    make = null,
                    model = null,
                    year = null,
                    isVehicleActive = null,
                    preQualStatus = null,
                    url = null,
                    origin = null
                ),
                content = ChatHistoryItem(
                    startTime = LocalDateTime.parse("2021-01-01T01:00"),
                    endTime = LocalDateTime.parse("2021-01-02T01:00"),
                    activityName = "activity",
                    chatContent = listOf(
                        ChatComment(
                            type = Type.AGENT,
                            content = "content",
                            viewUrl = null
                        )
                    ),
                    dealerId = "dealerId",
                    vehicleId = "inventoryId",
                    campaignId = "campaignId",
                    referrer = null,
                    referrerHistory = null,
                    source = null
                )
            ), actual
        )


    }
}
