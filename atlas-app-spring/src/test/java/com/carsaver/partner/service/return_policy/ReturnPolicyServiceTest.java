package com.carsaver.partner.service.return_policy;

import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.model.return_policy.ReturnPolicyRequest;
import com.carsaver.partner.model.return_policy.ReturnPolicyResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ReturnPolicyServiceTest {
    private static final String DEALER_ID = UUID.randomUUID().toString();
    private static final String PROGRAM_ID = UUID.randomUUID().toString();
    private static final String ID = UUID.randomUUID().toString();
    private static final String DEALER_SERVICE = "http://localhost:8080/dealer";

    @InjectMocks
    ReturnPolicyService dealerReturnPolicyService;

    @Mock
    NissanWebClient client;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(dealerReturnPolicyService, "dealerServiceUrl", DEALER_SERVICE);
    }

    @Test
    void retrievePolicyByDealerIdAndProgramId() {
        ReturnPolicyResponse result = buildResponse();
        when(client.get(any(), any(), anyString())).thenReturn(result);
        ReturnPolicyResponse returnPolicyResponse = dealerReturnPolicyService.retrieveReturnPoliciesForDealer(DEALER_ID, PROGRAM_ID);
        assertNotNull(returnPolicyResponse);
    }

    @Test
    void retrievePolicyByDealerIdAndProgramIdThrowsNotFound() {
        when(client.get(any(), any(), anyString())).thenThrow(new NotFoundException());
        assertThrows(NotFoundException.class, () -> dealerReturnPolicyService.retrieveReturnPoliciesForDealer(DEALER_ID, PROGRAM_ID));
    }

    @Test
    void upsertDealerReturnPolicy() {
        ReturnPolicyRequest request = ReturnPolicyRequest.builder().build();
        ReturnPolicyResponse result = buildResponse();
        when(client.post(any(), any(), any(), anyString())).thenReturn(result);
        ReturnPolicyResponse returnPolicyResponse = dealerReturnPolicyService.upsertDealerReturnPolicy(request);
        assertNotNull(returnPolicyResponse);
    }


    ReturnPolicyResponse buildResponse() {
        ReturnPolicyResponse policy =  new ReturnPolicyResponse();
        policy.setPolicyName("TEST");
        policy.setDealerId(DEALER_ID);
        policy.setPolicySubTitle("TEST_SUBTITLE");
        policy.setPolicyDescription("TEST_DESCRIPTION");
        policy.setDays(29);
        policy.setProgramId(PROGRAM_ID);
        policy.setId(ID);
        policy.setIsActive(Boolean.TRUE);
        policy.setIsActiveDisclosureLink(true);
        policy.setDisclosureLink("https://nissan-ecommerce.beta.carsaver.com/");
        return policy;
    }

}
