package com.carsaver.partner.service;

import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.elasticsearch.model.sale.VehicleSaleDoc;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.model.DealerLinkView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.elasticsearch.VehicleSaleDocService;
import com.carsaver.partner.exception.ForbiddenException;
import com.carsaver.search.support.SearchResults;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.CollectionModel;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;

class UserDealerAssociationServiceTest {

    private final VehicleSaleDocService vehicleSaleDocService = Mockito.mock(VehicleSaleDocService.class);
    private final DealerLinkClient dealerLinkClient = Mockito.mock(DealerLinkClient.class);
    private final UserDealerAssociationService userDealerAssociationService = new UserDealerAssociationService(vehicleSaleDocService, dealerLinkClient);

    private final String USER_ID_1 = "USER_ID_1";
    private final String DEALER_ID_1 = "DEALER_ID_1";
    private final String DEALER_ID_2 = "DEALER_ID_2";
    private final String DEALER_ID_3 = "DEALER_ID_3";

    @Test
    void validateUserDealerAssociationUserNullTest() {
        assertThrows(NullPointerException.class, () -> userDealerAssociationService.validateUserDealerAssociation(null, Collections.emptyList()));
    }

    @Test
    void validateUserDealerAssociationDealerLinkFoundTest() {
        DealerLinkView dealerLinkView = new DealerLinkView();
        dealerLinkView.setId("DEALER_LINK_1");
        dealerLinkView.setDealerId(DEALER_ID_1);

        Mockito.when(dealerLinkClient.findAllByUser(any())).thenReturn(CollectionModel.of(List.of(dealerLinkView)));
        UserView userView = new UserView();
        userView.setId(USER_ID_1);
        userDealerAssociationService.validateUserDealerAssociation(userView, List.of(DEALER_ID_1, DEALER_ID_2, DEALER_ID_3));
    }

    @Test
    void validateUserDealerAssociationDealerLinkNotFoundAndSaleRecordNotFoundTest() {
        Mockito.when(dealerLinkClient.findAllByUser(any())).thenReturn(CollectionModel.of(List.of()));
        UserView userView = new UserView();
        userView.setId(USER_ID_1);

        List<VehicleSaleDoc> vehicleSaleDocs = Collections.emptyList();
        SearchResults<VehicleSaleDoc> dealerDocSearchResults = SearchResults.<VehicleSaleDoc>builder()
            .content(vehicleSaleDocs).build();

        Mockito.when(vehicleSaleDocService.search(any(), (Pageable) any())).thenReturn(dealerDocSearchResults);

        SecurityContextHolder.clearContext();

        Assert.assertThrows(ForbiddenException.class, () -> userDealerAssociationService.validateUserDealerAssociation(userView, List.of(DEALER_ID_1, DEALER_ID_2, DEALER_ID_3)));
    }

    @Test
    void validateUserDealerAssociationDealerLinkNotFoundAndSaleRecordFoundTest() {
        Mockito.when(dealerLinkClient.findAllByUser(any())).thenReturn(CollectionModel.of(List.of()));
        UserView userView = new UserView();
        userView.setId(USER_ID_1);

        VehicleSaleDoc vehicleSaleDoc = new VehicleSaleDoc();
        vehicleSaleDoc.setId("SALE_ID_1");
        vehicleSaleDoc.setDealer(DealerDoc.builder().id(DEALER_ID_1).build());

        List<VehicleSaleDoc> vehicleSaleDocs = List.of(vehicleSaleDoc);
        SearchResults<VehicleSaleDoc> dealerDocSearchResults = SearchResults.<VehicleSaleDoc>builder()
            .content(vehicleSaleDocs).build();

        Mockito.when(vehicleSaleDocService.search(any(), (Pageable) any())).thenReturn(dealerDocSearchResults);

        UserView result = userDealerAssociationService.validateUserDealerAssociation(userView, List.of(DEALER_ID_1, DEALER_ID_2, DEALER_ID_3));
        Assert.assertEquals(result, userView);
    }
}
