package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmailAlertsFeatureToggleHandlerTest {

    @Mock
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    @Mock
    private SplitFeatureFlags splitFeatureFlags;



    @InjectMocks
    private EmailAlertsFeatureToggleHandler toggleHandler;


    @Test
    void shouldHandleFeatureToggleWhenRequestIsSuccessful() {
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setConfigType("email_alerts");
        toggleConfigRequest.setIsEnabled(true);

        FeatureSubscriptionResponse lmResponse = new FeatureSubscriptionResponse();
        lmResponse.setActive(true);

        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class)))
                .thenReturn(lmResponse);

        when(splitFeatureFlags.isEmailAlertsFeatureEnabled("dealer123", "program123"))
                .thenReturn(true);

        DealerProgram result = toggleHandler.handleFeatureToggle("dealer123", "program123", toggleConfigRequest);

        assertNotNull(result);
        assertTrue(result.getIsEmailAlertsEnabled());
        assertTrue(result.getIsEmailAlertsFeatureEnabled());

        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
        verify(splitFeatureFlags, times(1)).isEmailAlertsFeatureEnabled("dealer123", "program123");
    }

    @Test
    void shouldThrowInternalServerErrorWhenResponseIsNull() {
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setConfigType("email_alerts");
        toggleConfigRequest.setIsEnabled(false);  // Disable the feature

        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class)))
                .thenReturn(null);

        assertThrows(InternalServerError.class, () ->
                toggleHandler.handleFeatureToggle("dealer123", "program123", toggleConfigRequest));


        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void shouldThrowInternalServerErrorWhenActiveIsNull() {
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setConfigType("email_alerts");
        toggleConfigRequest.setIsEnabled(false);

        FeatureSubscriptionResponse lmResponse = new FeatureSubscriptionResponse();
        lmResponse.setActive(null);

        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class)))
                .thenReturn(lmResponse);

        assertThrows(InternalServerError.class, () ->
                toggleHandler.handleFeatureToggle("dealer123", "program123", toggleConfigRequest));


        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void shouldReturnFalseOnSupportsForWrongConfigType() {
        assertFalse(toggleHandler.supports("some_other_config_type"));
    }

    @Test
    void shouldReturnTrueOnSupportsForCorrectConfigType() {
        assertTrue(toggleHandler.supports("email_alerts"));
    }
}
